package cn.com.chinastock.cnf.examples.mcp.service;

import cn.com.chinastock.cnf.examples.mcp.model.CreateUserRequest;
import cn.com.chinastock.cnf.examples.mcp.model.User;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 用户服务
 * 
 * <AUTHOR> Boot Team
 * @since 0.1.1
 */
@Service
public class UserService {
    
    private final Map<Long, User> users = new ConcurrentHashMap<>();
    private final AtomicLong idGenerator = new AtomicLong(1);
    
    public UserService() {
        // 初始化一些测试数据
        initTestData();
    }
    
    /**
     * 初始化测试数据
     */
    private void initTestData() {
        createUser(new CreateUserRequest("admin", "管理员", "<EMAIL>", 30, "IT部", "系统管理员"));
        createUser(new CreateUserRequest("john", "约翰·史密斯", "<EMAIL>", 28, "开发部", "高级工程师"));
        createUser(new CreateUserRequest("jane", "简·多伊", "<EMAIL>", 26, "产品部", "产品经理"));
        createUser(new CreateUserRequest("bob", "鲍勃·威尔逊", "<EMAIL>", 32, "销售部", "销售经理"));
    }
    
    /**
     * 根据ID获取用户
     */
    public User getUserById(Long id) {
        User user = users.get(id);
        if (user == null) {
            throw new RuntimeException("用户不存在，ID: " + id);
        }
        return user;
    }
    
    /**
     * 根据用户名获取用户
     */
    public User getUserByUsername(String username) {
        return users.values().stream()
                .filter(user -> user.getUsername().equals(username))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("用户不存在，用户名: " + username));
    }
    
    /**
     * 获取所有用户
     */
    public List<User> getAllUsers() {
        return new ArrayList<>(users.values());
    }
    
    /**
     * 根据部门获取用户列表
     */
    public List<User> getUsersByDepartment(String department) {
        return users.values().stream()
                .filter(user -> department.equals(user.getDepartment()))
                .collect(Collectors.toList());
    }
    
    /**
     * 创建用户
     */
    public User createUser(CreateUserRequest request) {
        // 检查用户名是否已存在
        boolean usernameExists = users.values().stream()
                .anyMatch(user -> user.getUsername().equals(request.getUsername()));
        
        if (usernameExists) {
            throw new RuntimeException("用户名已存在: " + request.getUsername());
        }
        
        Long id = idGenerator.getAndIncrement();
        User user = new User(id, request.getUsername(), request.getName(), 
                           request.getEmail(), request.getAge(), 
                           request.getDepartment(), request.getPosition());
        
        users.put(id, user);
        return user;
    }
    
    /**
     * 更新用户信息
     */
    public User updateUser(Long id, CreateUserRequest request) {
        User existingUser = getUserById(id);
        
        // 检查用户名是否被其他用户使用
        boolean usernameConflict = users.values().stream()
                .anyMatch(user -> !user.getId().equals(id) && 
                         user.getUsername().equals(request.getUsername()));
        
        if (usernameConflict) {
            throw new RuntimeException("用户名已被其他用户使用: " + request.getUsername());
        }
        
        existingUser.setUsername(request.getUsername());
        existingUser.setName(request.getName());
        existingUser.setEmail(request.getEmail());
        existingUser.setAge(request.getAge());
        existingUser.setDepartment(request.getDepartment());
        existingUser.setPosition(request.getPosition());

        return existingUser;
    }
    
    /**
     * 删除用户
     */
    public void deleteUser(Long id) {
        User user = users.remove(id);
        if (user == null) {
            throw new RuntimeException("用户不存在，ID: " + id);
        }
    }
    
    /**
     * 搜索用户
     */
    public List<User> searchUsers(String keyword) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return getAllUsers();
        }
        
        String lowerKeyword = keyword.toLowerCase();
        return users.values().stream()
                .filter(user -> 
                    user.getName().toLowerCase().contains(lowerKeyword) ||
                    user.getUsername().toLowerCase().contains(lowerKeyword) ||
                    user.getEmail().toLowerCase().contains(lowerKeyword) ||
                    (user.getDepartment() != null && user.getDepartment().toLowerCase().contains(lowerKeyword)) ||
                    (user.getPosition() != null && user.getPosition().toLowerCase().contains(lowerKeyword))
                )
                .collect(Collectors.toList());
    }
    
    /**
     * 获取用户统计信息
     */
    public Map<String, Object> getUserStatistics() {
        List<User> allUsers = getAllUsers();
        
        Map<String, Long> departmentCount = allUsers.stream()
                .filter(user -> user.getDepartment() != null)
                .collect(Collectors.groupingBy(User::getDepartment, Collectors.counting()));
        
        double averageAge = allUsers.stream()
                .mapToInt(User::getAge)
                .average()
                .orElse(0.0);
        
        Map<String, Object> statistics = new ConcurrentHashMap<>();
        statistics.put("totalUsers", allUsers.size());
        statistics.put("averageAge", Math.round(averageAge * 100.0) / 100.0);
        statistics.put("departmentDistribution", departmentCount);
        
        return statistics;
    }
}
