# Galaxy Boot MCP Example

这是一个演示如何使用 Galaxy Boot MCP Starter 的示例应用。通过 `@McpTool` 注解，可以轻松将 SpringBoot RestController 接口暴露为 MCP 工具，使得 AI 可以通过聊天的方式调用这些 API。

## 功能演示

### 用户管理工具
- `getUserById` - 根据用户ID获取用户信息
- `getUserByUsername` - 根据用户名获取用户信息
- `getAllUsers` - 获取所有用户列表
- `getUsersByDepartment` - 根据部门获取用户列表
- `createUser` - 创建新用户
- `updateUser` - 更新用户信息
- `deleteUser` - 删除用户
- `searchUsers` - 搜索用户
- `getUserStatistics` - 获取用户统计信息

### 计算器工具
- `add` - 加法运算
- `subtract` - 减法运算
- `multiply` - 乘法运算
- `divide` - 除法运算
- `power` - 幂运算
- `sqrt` - 平方根
- `percentage` - 百分比计算
- `complexCalculation` - 复合计算

## 快速开始

### 1. 启动应用

```bash
mvn spring-boot:run
```

应用将在 `http://localhost:8080` 启动。

### 2. 查看MCP端点

- SSE端点: `http://localhost:8080/mcp/sse`
- 消息端点: `http://localhost:8080/mcp/message`

### 3. 测试REST API

#### 用户管理API

```bash
# 获取所有用户
curl http://localhost:8080/api/users

# 根据ID获取用户
curl http://localhost:8080/api/users/1

# 创建用户
curl -X POST http://localhost:8080/api/users \
  -H "Content-Type: application/json" \
  -d '{
    "username": "test",
    "name": "测试用户",
    "email": "<EMAIL>",
    "age": 25,
    "department": "测试部",
    "position": "测试工程师"
  }'

# 搜索用户
curl "http://localhost:8080/api/users/search?keyword=管理"

# 获取统计信息
curl http://localhost:8080/api/users/statistics
```

#### 计算器API

```bash
# 加法
curl "http://localhost:8080/api/calculator/add?a=10&b=5"

# 除法
curl "http://localhost:8080/api/calculator/divide?a=10&b=3"

# 复合计算
curl -X POST http://localhost:8080/api/calculator/complex \
  -H "Content-Type: application/json" \
  -d '{"a": 10, "b": 3}'
```

## MCP工具配置

### 工具分组
- `user` - 用户管理相关工具
- `math` - 数学计算相关工具

### 工具标签
- `public` - 公开可用的工具
- `admin` - 需要管理员权限的工具
- `query` - 查询类工具
- `create` - 创建类工具
- `update` - 更新类工具
- `delete` - 删除类工具

### 配置示例

```yaml
galaxy:
  mcp:
    tool:
      # 只暴露public标签的工具
      tag-filters:
        - public
      # 只暴露特定分组的工具
      group-filters:
        - user
        - math
      # 单个工具配置
      items:
        createUser:
          enabled: false  # 禁用创建用户工具
```

## AI聊天示例

当MCP服务器运行后，AI可以通过以下方式调用工具：

### 用户管理对话示例

**用户**: "帮我查看所有用户"
**AI**: 调用 `getAllUsers` 工具，返回用户列表

**用户**: "创建一个新用户，用户名是alice，姓名是爱丽丝，邮箱是*****************，年龄28岁，部门是开发部"
**AI**: 调用 `createUser` 工具，传入相应参数

**用户**: "搜索开发部的用户"
**AI**: 调用 `searchUsers` 工具，关键词为"开发部"

### 计算器对话示例

**用户**: "计算 15 + 25"
**AI**: 调用 `add` 工具，参数 a=15, b=25

**用户**: "计算 100 的平方根"
**AI**: 调用 `sqrt` 工具，参数 number=100

**用户**: "计算 30 是 120 的百分之几"
**AI**: 调用 `percentage` 工具，参数 part=30, total=120

## 日志查看

启动应用后，可以在控制台看到MCP工具的注册信息：

```
Galaxy Boot MCP: Found 12 MCP tools
Galaxy Boot MCP: Registered tool 'getUserById' from method UserController.getUserById
Galaxy Boot MCP: Registered tool 'createUser' from method UserController.createUser
Galaxy Boot MCP: Registered tool 'add' from method CalculatorController.add
...
```

## 自定义扩展

### 添加新的工具

1. 在Controller中添加新方法
2. 使用 `@McpTool` 注解标记
3. 配置工具属性（名称、描述、分组、标签等）

```java
@McpTool(
    name = "customTool",
    description = "自定义工具描述",
    group = "custom",
    tags = {"custom", "example"}
)
@GetMapping("/custom")
public String customTool(@RequestParam String input) {
    return "处理结果: " + input;
}
```

### 配置工具过滤

通过配置文件控制哪些工具暴露给AI：

```yaml
galaxy:
  mcp:
    tool:
      group-filters:
        - user  # 只暴露用户管理工具
      tag-filters:
        - public  # 只暴露公开工具
```

## 注意事项

1. **参数验证**: 使用 `@Valid` 注解进行参数验证
2. **异常处理**: 工具执行异常会被自动捕获并返回错误信息
3. **类型转换**: 支持自动的参数类型转换和JSON序列化
4. **性能考虑**: 合理设置超时时间，避免长时间运行的操作

## 故障排除

### 工具未被发现
- 检查包扫描路径配置
- 确认 `@McpTool` 注解正确使用
- 查看启动日志中的工具注册信息

### 参数类型错误
- 确保参数类型支持JSON序列化
- 检查请求参数格式是否正确
- 使用 `@Valid` 注解进行参数验证

### 连接问题
- 确认MCP端点配置正确
- 检查防火墙和网络设置
- 查看应用日志中的错误信息
