package cn.com.chinastock.cnf.examples.mcp.controller;

import cn.com.chinastock.cnf.mcp.annotation.McpTool;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;

/**
 * 计算器Controller
 * 
 * 演示简单的数学计算工具
 * 
 * <AUTHOR> Boot Team
 * @since 0.1.1
 */
@RestController
@RequestMapping("/api/calculator")
public class CalculatorController {
    
    /**
     * 加法运算
     */
    @McpTool(
        name = "add",
        description = "计算两个数的和",
        group = "math",
        tags = {"math", "calculation", "basic", "public"}
    )
    @GetMapping("/add")
    public double add(@RequestParam double a, @RequestParam double b) {
        return a + b;
    }
    
    /**
     * 减法运算
     */
    @McpTool(
        name = "subtract",
        description = "计算两个数的差（第一个数减去第二个数）",
        group = "math",
        tags = {"math", "calculation", "basic", "public"}
    )
    @GetMapping("/subtract")
    public double subtract(@RequestParam double a, @RequestParam double b) {
        return a - b;
    }
    
    /**
     * 乘法运算
     */
    @McpTool(
        name = "multiply",
        description = "计算两个数的乘积",
        group = "math",
        tags = {"math", "calculation", "basic", "public"}
    )
    @GetMapping("/multiply")
    public double multiply(@RequestParam double a, @RequestParam double b) {
        return a * b;
    }
    
    /**
     * 除法运算
     */
    @McpTool(
        name = "divide",
        description = "计算两个数的商（第一个数除以第二个数）",
        group = "math",
        tags = {"math", "calculation", "basic", "public"}
    )
    @GetMapping("/divide")
    public double divide(@RequestParam double a, @RequestParam double b) {
        if (b == 0) {
            throw new IllegalArgumentException("除数不能为零");
        }
        return a / b;
    }
    
    /**
     * 幂运算
     */
    @McpTool(
        name = "power",
        description = "计算第一个数的第二个数次幂",
        group = "math",
        tags = {"math", "calculation", "advanced", "public"}
    )
    @GetMapping("/power")
    public double power(@RequestParam double base, @RequestParam double exponent) {
        return Math.pow(base, exponent);
    }
    
    /**
     * 平方根
     */
    @McpTool(
        name = "sqrt",
        description = "计算一个数的平方根",
        group = "math",
        tags = {"math", "calculation", "advanced", "public"}
    )
    @GetMapping("/sqrt")
    public double sqrt(@RequestParam double number) {
        if (number < 0) {
            throw new IllegalArgumentException("不能计算负数的平方根");
        }
        return Math.sqrt(number);
    }
    
    /**
     * 百分比计算
     */
    @McpTool(
        name = "percentage",
        description = "计算百分比：(部分/总数) * 100",
        group = "math",
        tags = {"math", "calculation", "percentage", "public"}
    )
    @GetMapping("/percentage")
    public double percentage(@RequestParam double part, @RequestParam double total) {
        if (total == 0) {
            throw new IllegalArgumentException("总数不能为零");
        }
        return (part / total) * 100;
    }
    
    /**
     * 复合计算
     */
    @McpTool(
        name = "complexCalculation",
        description = "执行复合数学计算，支持加减乘除运算",
        group = "math",
        tags = {"math", "calculation", "complex", "public"},
        responseMimeType = "application/json"
    )
    @PostMapping("/complex")
    public Map<String, Object> complexCalculation(@RequestBody CalculationRequest request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            double a = request.getA();
            double b = request.getB();
            
            result.put("input", Map.of("a", a, "b", b));
            result.put("add", a + b);
            result.put("subtract", a - b);
            result.put("multiply", a * b);
            
            if (b != 0) {
                result.put("divide", BigDecimal.valueOf(a / b)
                    .setScale(4, RoundingMode.HALF_UP).doubleValue());
            } else {
                result.put("divide", "除数不能为零");
            }
            
            result.put("success", true);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 计算请求模型
     */
    public static class CalculationRequest {
        private double a;
        private double b;
        
        public CalculationRequest() {}
        
        public CalculationRequest(double a, double b) {
            this.a = a;
            this.b = b;
        }
        
        public double getA() {
            return a;
        }
        
        public void setA(double a) {
            this.a = a;
        }
        
        public double getB() {
            return b;
        }
        
        public void setB(double b) {
            this.b = b;
        }
        
        @Override
        public String toString() {
            return "CalculationRequest{" +
                   "a=" + a +
                   ", b=" + b +
                   '}';
        }
    }
}
