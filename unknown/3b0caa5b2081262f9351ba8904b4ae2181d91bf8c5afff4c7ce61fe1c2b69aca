# 服务器配置
server:
  port: 8080

# Spring Boot 基础配置
spring:
  application:
    name: galaxy-boot-redis-example
  
  # Spring Data Redis 配置
  data:
    redis:
      sentinel:
        master: mymaster
        nodes:
          - localhost:26379  # Sentinel 节点1（Docker 服务名）
          - localhost:26380  # Sentinel 节点2
          - localhost:26381  # Sentinel 节点3
        database: 0
#      host: localhost
#      port: 6379
#      password:
#      database: 0
      timeout: 5s
      connect-timeout: 5s
      lettuce:
        pool:
          enabled: true
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: 5s
          time-between-eviction-runs: 30s
        cluster:
          refresh:
            adaptive: true

# Galaxy Redis 增强配置
galaxy:
  redis:
    serialization:
      key-serializer: string         # Key 序列化器：string/fastjson/jackson/jdk
      value-serializer: fastjson     # Value 序列化器 - 使用Jackson避免兼容性问题
      hash-key-serializer: string    # Hash Key 序列化器
      hash-value-serializer: fastjson # Hash Value 序列化器 - 使用Jackson避免兼容性问题

  log:
    exception-pretty-print: true

# 日志配置
logging:
  level:
    org.springframework.data.redis: INFO
    io.lettuce.core: INFO

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,beans,configprops

