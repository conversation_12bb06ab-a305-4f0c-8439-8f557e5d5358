package cn.com.chinastock.cnf.examples.mcp.controller;

import cn.com.chinastock.cnf.examples.mcp.model.CreateUserRequest;
import cn.com.chinastock.cnf.examples.mcp.model.User;
import cn.com.chinastock.cnf.examples.mcp.service.UserService;
import cn.com.chinastock.cnf.mcp.annotation.McpTool;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 用户管理Controller
 * 
 * 演示如何使用@McpTool注解将RestController方法暴露为MCP工具
 * 
 * <AUTHOR> Boot Team
 * @since 0.1.1
 */
@RestController
@RequestMapping("/api/users")
public class UserController {
    
    @Autowired
    private UserService userService;
    
    /**
     * 根据ID获取用户信息
     */
    @McpTool(
        name = "getUserById",
        description = "根据用户ID获取用户详细信息，包括姓名、邮箱、部门等",
        group = "user",
        tags = {"user", "query", "public"}
    )
    @GetMapping("/{id}")
    public ResponseEntity<User> getUserById(@PathVariable Long id) {
        User user = userService.getUserById(id);
        return ResponseEntity.ok(user);
    }
    
    /**
     * 根据用户名获取用户信息
     */
    @McpTool(
        name = "getUserByUsername",
        description = "根据用户名获取用户详细信息",
        group = "user",
        tags = {"user", "query", "public"}
    )
    @GetMapping("/username/{username}")
    public ResponseEntity<User> getUserByUsername(@PathVariable String username) {
        User user = userService.getUserByUsername(username);
        return ResponseEntity.ok(user);
    }
    
    /**
     * 获取所有用户列表
     */
    @McpTool(
        name = "getAllUsers",
        description = "获取系统中所有用户的列表",
        group = "user",
        tags = {"user", "query", "list", "public"}
    )
    @GetMapping
    public ResponseEntity<List<User>> getAllUsers() {
        List<User> users = userService.getAllUsers();
        return ResponseEntity.ok(users);
    }
    
    /**
     * 根据部门获取用户列表
     */
    @McpTool(
        name = "getUsersByDepartment",
        description = "根据部门名称获取该部门的所有用户列表",
        group = "user",
        tags = {"user", "query", "department", "public"}
    )
    @GetMapping("/department/{department}")
    public ResponseEntity<List<User>> getUsersByDepartment(@PathVariable String department) {
        List<User> users = userService.getUsersByDepartment(department);
        return ResponseEntity.ok(users);
    }
    
    /**
     * 创建新用户
     */
    @McpTool(
        name = "createUser",
        description = "创建新用户，需要提供用户名、姓名、邮箱、年龄等信息",
        group = "user",
        tags = {"user", "create", "admin"},
        responseMimeType = "application/json"
    )
    @PostMapping
    public ResponseEntity<User> createUser(@Valid @RequestBody CreateUserRequest request) {
        User user = userService.createUser(request);
        return ResponseEntity.ok(user);
    }
    
    /**
     * 更新用户信息
     */
    @McpTool(
        name = "updateUser",
        description = "更新指定用户的信息",
        group = "user",
        tags = {"user", "update", "admin"}
    )
    @PutMapping("/{id}")
    public ResponseEntity<User> updateUser(@PathVariable Long id, 
                                          @Valid @RequestBody CreateUserRequest request) {
        User user = userService.updateUser(id, request);
        return ResponseEntity.ok(user);
    }
    
    /**
     * 删除用户
     */
    @McpTool(
        name = "deleteUser",
        description = "根据用户ID删除用户",
        group = "user",
        tags = {"user", "delete", "admin"}
    )
    @DeleteMapping("/{id}")
    public ResponseEntity<String> deleteUser(@PathVariable Long id) {
        userService.deleteUser(id);
        return ResponseEntity.ok("用户删除成功，ID: " + id);
    }
    
    /**
     * 搜索用户
     */
    @McpTool(
        name = "searchUsers",
        description = "根据关键词搜索用户，支持按姓名、用户名、邮箱、部门、职位搜索",
        group = "user",
        tags = {"user", "search", "public"}
    )
    @GetMapping("/search")
    public ResponseEntity<List<User>> searchUsers(@RequestParam String keyword) {
        List<User> users = userService.searchUsers(keyword);
        return ResponseEntity.ok(users);
    }
    
    /**
     * 获取用户统计信息
     */
    @McpTool(
        name = "getUserStatistics",
        description = "获取用户统计信息，包括总用户数、平均年龄、部门分布等",
        group = "user",
        tags = {"user", "statistics", "analytics", "public"},
        responseMimeType = "application/json"
    )
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getUserStatistics() {
        Map<String, Object> statistics = userService.getUserStatistics();
        return ResponseEntity.ok(statistics);
    }
}
