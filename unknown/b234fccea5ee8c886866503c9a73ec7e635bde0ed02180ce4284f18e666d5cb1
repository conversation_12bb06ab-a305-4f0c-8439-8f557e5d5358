package cn.com.chinastock.cnf.examples.mcp.model;

import com.alibaba.fastjson2.annotation.JSONField;

import java.time.LocalDateTime;

/**
 * 用户信息模型
 * 
 * <AUTHOR> Boot Team
 * @since 0.1.1
 */
public class User {
    
    private Long id;
    
    private String username;
    
    private String name;
    
    private String email;
    
    private Integer age;
    
    private String department;
    
    private String position;

    // 构造函数
    public User() {}
    
    public User(Long id, String username, String name, String email, Integer age, 
                String department, String position) {
        this.id = id;
        this.username = username;
        this.name = name;
        this.email = email;
        this.age = age;
        this.department = department;
        this.position = position;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public Integer getAge() {
        return age;
    }
    
    public void setAge(Integer age) {
        this.age = age;
    }
    
    public String getDepartment() {
        return department;
    }
    
    public void setDepartment(String department) {
        this.department = department;
    }
    
    public String getPosition() {
        return position;
    }
    
    public void setPosition(String position) {
        this.position = position;
    }
    
    @Override
    public String toString() {
        return "User{" +
               "id=" + id +
               ", username='" + username + '\'' +
               ", name='" + name + '\'' +
               ", email='" + email + '\'' +
               ", age=" + age +
               ", department='" + department + '\'' +
               ", position='" + position + '\'' +
               '}';
    }
}
