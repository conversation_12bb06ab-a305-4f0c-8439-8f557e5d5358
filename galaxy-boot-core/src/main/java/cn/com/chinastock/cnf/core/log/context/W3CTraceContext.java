package cn.com.chinastock.cnf.core.log.context;

import org.springframework.util.StringUtils;

import java.util.*;

/**
 * W3C Trace Context实现
 *
 * <AUTHOR>
 */
public class W3CTraceContext implements ITraceContext {

    public static final String TRACE_PARENT_HEADER = "traceparent";
    private static final String VERSION = "00";
    private static final String TRACE_FLAGS = "01";

    private String traceId;
    private String spanId;
    private String parentSpanId;

    public W3CTraceContext() {
        traceId = null;
        spanId = null;
        parentSpanId = null;
    }

    @Override
    public String getTraceId() {
        return traceId;
    }

    @Override
    public String getSpanId() {
        return spanId;
    }

    @Override
    public String getParentSpanId() {
        return parentSpanId;
    }

    @Override
    public void extractTraceContext(Map<String, String> headers) {
        if (null == headers || headers.isEmpty()) {
            initTrace();
            return;
        }

        String traceparent = headers.get(TRACE_PARENT_HEADER);
        if (traceparent == null || traceparent.isEmpty()) {
            initTrace();
            return;
        }

        String[] parts = traceparent.split("-");
        if (parts.length != 4) {
            initTrace();
            return;
        }

        traceId = parts[1];
        parentSpanId = parts[2];
        spanId = generateSpanId();
    }

    @Override
    public Map<String, String> generateTraceHeaders(String traceId, String spanId) {
        if (!StringUtils.hasText(traceId) && !StringUtils.hasText(spanId)) {
            return new HashMap<>();
        }
        HashMap<String, String> headers = new HashMap<>();
        headers.put(TRACE_PARENT_HEADER, String.format("%s-%s-%s-%s", VERSION, traceId, spanId, TRACE_FLAGS));
        return headers;
    }

    /**
     * 初始化新的trace context
     */
    private void initTrace() {
        generateTraceId();
        generateSpanId();
        parentSpanId = null;
    }

    @Override
    public void clear() {
        traceId = null;
        spanId = null;
        parentSpanId = null;
    }

    @Override
    public List<String> getTraceHeaders() {
        return new ArrayList<>() {{
            add(TRACE_PARENT_HEADER);
        }};
    }

    @Override
    public String generateTraceId() {
        traceId = UUID.randomUUID().toString().replace("-", "");
        return traceId;
    }

    @Override
    public String generateSpanId() {
        spanId = UUID.randomUUID().toString().replace("-", "").substring(0, 16);
        return spanId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public void setSpanId(String spanId) {
        this.spanId = spanId;
    }

    public void setParentSpanId(String parentSpanId) {
        this.parentSpanId = parentSpanId;
    }
}