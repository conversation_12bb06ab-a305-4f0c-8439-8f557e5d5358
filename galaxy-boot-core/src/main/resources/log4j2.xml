<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="INFO">
    <Properties>
        <Property name="LOG_PATTERN">
            V1|%d{yyyy-MM-dd'T'HH:mm:ss.SSSZ}|%N|%p|%t|${sys:ip-addr}|%replace{%X{request_method}}{^$}{-}|%replace{%X{request_uri}}{^$}{-}|%replace{%X{traceId}}{^$}{-}|%replace{%X{parentSpanId}}{^$}{-}|%replace{%X{spanId}}{^$}{-}|${sys:system-code}|${sys:service-name}|%logger|%replace{%X{log_category}}{^$}{FRAMEWORK_LOG}|%replace{%message}{[\r\n]+}{ }|-%n
        </Property>
        <Property name="LOG_PATTERN_EXCEPTION">
            V1|%d{yyyy-MM-dd'T'HH:mm:ss.SSSZ}|%N|%p|%t|${sys:ip-addr}|%replace{%X{request_method}}{^$}{-}|%replace{%X{request_uri}}{^$}{-}|%replace{%X{traceId}}{^$}{-}|%replace{%X{parentSpanId}}{^$}{-}|%replace{%X{spanId}}{^$}{-}|${sys:system-code}|${sys:service-name}|%logger|%replace{%X{log_category}}{^$}{FRAMEWORK_LOG}|%replace{%message}{[\r\n]+}{ }|%xThrowable{separator(\n\t)}%n
        </Property>
        <Property name="LOG_PATTERN_PRETTY">
            V1 %d{yyyy-MM-dd'T'HH:mm:ss.SSSZ} %N %p %t ${sys:ip-addr} %replace{%X{request_method}}{^$}{-} %replace{%X{request_uri}}{^$}{-} %replace{%X{traceId}}{^$}{-} %replace{%X{parentSpanId}}{^$}{-} %replace{%X{spanId}}{^$}{-} ${sys:system-code} ${sys:service-name} %logger %replace{%X{log_category}}{^$}{FRAMEWORK_LOG} %message%n%xThrowable{full}
        </Property>
        <Property name="SQL_PATTERN">
            V1|%d{yyyy-MM-dd'T'HH:mm:ss.SSSZ}|%N|%p|%t|${sys:ip-addr}|%replace{%X{request_method}}{^$}{-}|%replace{%X{request_uri}}{^$}{-}|%replace{%X{traceId}}{^$}{-}|%replace{%X{parentSpanId}}{^$}{-}|%replace{%X{spanId}}{^$}{-}|${sys:system-code}|${sys:service-name}|%logger|SQL_LOG|%replace{%message}{[\r\n]+}{ }|-%n
        </Property>
        <Property name="LOG_HOME">${sys:log.path:-logs}</Property>
        <Property name="FILE_NAME">application</Property>
        <Property name="ERROR_FILE_NAME">error</Property>
        <Property name="EXCEPTION_FILE_NAME">exception</Property>
        <Property name="SQL_FILE_NAME">sql</Property>
        <Property name="ROLLING_FILE_NAME">${LOG_HOME}/${FILE_NAME}-%d{yyyy-MM-dd}-%i.log</Property>
        <Property name="ERROR_ROLLING_FILE_NAME">${LOG_HOME}/${ERROR_FILE_NAME}-%d{yyyy-MM-dd}-%i.log</Property>
        <Property name="EXCEPTION_ROLLING_FILE_NAME">${LOG_HOME}/${EXCEPTION_FILE_NAME}-%d{yyyy-MM-dd}-%i.log</Property>
        <Property name="SQL_ROLLING_FILE_NAME">${LOG_HOME}/${SQL_FILE_NAME}-%d{yyyy-MM-dd}-%i.log</Property>
        <Property name="ASYNC_BUFFER_SIZE">262144</Property>
    </Properties>

    <Appenders>
        <!-- 非ERROR级别的日志 -->
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="${LOG_PATTERN}" charset="UTF-8"/>
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="DENY" onMismatch="ACCEPT"/>
            </Filters>
        </Console>

        <!-- ERROR级别且无Throwable的日志 -->
        <Console name="ErrorConsoleNoException" target="SYSTEM_ERR">
            <PatternLayout pattern="${LOG_PATTERN}" charset="UTF-8"/>
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="NEUTRAL" onMismatch="DENY"/>
                <ThrowableFilter onMatch="DENY" onMismatch="ACCEPT"/>
            </Filters>
        </Console>

        <!-- ERROR级别且有Throwable的日志 -->
        <Console name="ErrorConsoleWithException" target="SYSTEM_ERR">
            <PatternLayout pattern="${LOG_PATTERN_EXCEPTION}" charset="UTF-8"/>
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="NEUTRAL" onMismatch="DENY"/>
                <ThrowableFilter onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </Console>

        <!-- ERROR级别且有Throwable，且配置了需要美化输出的日志 -->
        <Console name="ErrorConsolePretty" target="SYSTEM_ERR">
            <PatternLayout pattern="${LOG_PATTERN_PRETTY}" charset="UTF-8"/>
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="NEUTRAL" onMismatch="DENY"/>
                <ThrowablePrettyPrintFilter onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </Console>
        <Console name="SqlConsole" target="SYSTEM_OUT">
            <PatternLayout pattern="${SQL_PATTERN}" charset="UTF-8"/>
        </Console>

        <!-- 非ERROR级别的日志，文件输出 -->
        <RollingRandomAccessFile name="RollingFile" fileName="${LOG_HOME}/${FILE_NAME}.log"
                                 filePattern="${ROLLING_FILE_NAME}"
                                 immediateFlush="false"
                                 append="true"
                                 bufferedIO="true"
                                 bufferSize="${ASYNC_BUFFER_SIZE}">
            <PatternLayout pattern="${LOG_PATTERN}" charset="UTF-8"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="100MB"/>
            </Policies>
            <DefaultRolloverStrategy max="30">
                <Delete basePath="${LOG_HOME}" maxDepth="1">
                    <IfFileName glob="${FILE_NAME}-*.log"/>
                    <IfLastModified age="30d"/>
                </Delete>
            </DefaultRolloverStrategy>
            <ThresholdFilter level="ERROR" onMatch="DENY" onMismatch="ACCEPT"/>
        </RollingRandomAccessFile>

        <!-- ERROR级别且无Throwable的日志，文件输出 -->
        <RollingRandomAccessFile name="ErrorRollingFile" fileName="${LOG_HOME}/${ERROR_FILE_NAME}.log"
                                 filePattern="${ERROR_ROLLING_FILE_NAME}"
                                 immediateFlush="false"
                                 append="true"
                                 bufferedIO="true"
                                 bufferSize="${ASYNC_BUFFER_SIZE}">
            <PatternLayout pattern="${LOG_PATTERN}" charset="UTF-8"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="100MB"/>
            </Policies>
            <DefaultRolloverStrategy max="30">
                <Delete basePath="${LOG_HOME}" maxDepth="1">
                    <IfFileName glob="${ERROR_FILE_NAME}-*.log"/>
                    <IfLastModified age="30d"/>
                </Delete>
            </DefaultRolloverStrategy>
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="NEUTRAL" onMismatch="DENY"/>
                <ThrowableFilter onMatch="DENY" onMismatch="ACCEPT"/>
            </Filters>
        </RollingRandomAccessFile>

        <!-- ERROR级别且有Throwable的日志，文件输出 -->
        <RollingRandomAccessFile name="ErrorWithExceptionRollingFile" fileName="${LOG_HOME}/${EXCEPTION_FILE_NAME}.log"
                                 filePattern="${EXCEPTION_ROLLING_FILE_NAME}"
                                 immediateFlush="false"
                                 append="true"
                                 bufferedIO="true"
                                 bufferSize="${ASYNC_BUFFER_SIZE}">
            <PatternLayout pattern="${LOG_PATTERN_EXCEPTION}" charset="UTF-8"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="100MB"/>
            </Policies>
            <DefaultRolloverStrategy max="30">
                <Delete basePath="${LOG_HOME}" maxDepth="1">
                    <IfFileName glob="${EXCEPTION_FILE_NAME}-*.log"/>
                    <IfLastModified age="30d"/>
                </Delete>
            </DefaultRolloverStrategy>
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="NEUTRAL" onMismatch="DENY"/>
                <ThrowableFilter onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </RollingRandomAccessFile>

        <!-- SQL日志，文件输出 -->
        <RollingRandomAccessFile name="SqlRollingFile"
                                 fileName="${LOG_HOME}/${SQL_FILE_NAME}.log"
                                 filePattern="${SQL_ROLLING_FILE_NAME}"
                                 immediateFlush="false"
                                 append="true"
                                 bufferedIO="true"
                                 bufferSize="${ASYNC_BUFFER_SIZE}">
            <PatternLayout pattern="${SQL_PATTERN}" charset="UTF-8"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="100MB"/>
            </Policies>
            <DefaultRolloverStrategy max="30">
                <Delete basePath="${LOG_HOME}" maxDepth="1">
                    <IfFileName glob="${SQL_FILE_NAME}-*.log"/>
                    <IfLastModified age="30d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
    </Appenders>

    <Loggers>
        <!-- Hibernate SQL日志配置 -->
        <AsyncLogger name="org.hibernate.SQL" level="DEBUG" additivity="false">
            <AppenderRef ref="SqlConsole"/>
            <AppenderRef ref="SqlRollingFile" level="${sys:log.file.enabled:-OFF}"/>
        </AsyncLogger>

        <!-- SQL参数绑定日志 -->
        <AsyncLogger name="org.hibernate.orm.jdbc.bind" level="TRACE" additivity="false">
            <AppenderRef ref="SqlConsole"/>
            <AppenderRef ref="SqlRollingFile" level="${sys:log.file.enabled:-OFF}"/>
        </AsyncLogger>

        <!-- SQL参数绑定日志 -->
        <AsyncLogger name="org.hibernate.type.descriptor.sql.BasicBinder" level="TRACE" additivity="false">
            <AppenderRef ref="SqlConsole"/>
            <AppenderRef ref="SqlRollingFile" level="${sys:log.file.enabled:-OFF}"/>
        </AsyncLogger>

        <!-- MyBatis SQL日志配置，需要配合 mybatis-config.xml 配置，详细配置参见文档 -->
        <AsyncLogger name="SQL" level="DEBUG" additivity="false">
            <AppenderRef ref="SqlConsole"/>
            <AppenderRef ref="SqlRollingFile" level="${sys:log.file.enabled:-OFF}"/>
        </AsyncLogger>

        <AsyncRoot level="INFO">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="ErrorConsoleNoException"/>
            <AppenderRef ref="ErrorConsoleWithException"/>
            <AppenderRef ref="ErrorConsolePretty"/>
            <AppenderRef ref="RollingFile" level="${sys:log.file.enabled:-OFF}"/>
            <AppenderRef ref="ErrorRollingFile" level="${sys:log.file.enabled:-OFF}"/>
            <AppenderRef ref="ErrorWithExceptionRollingFile" level="${sys:log.file.enabled:-OFF}"/>
        </AsyncRoot>
    </Loggers>
</Configuration>