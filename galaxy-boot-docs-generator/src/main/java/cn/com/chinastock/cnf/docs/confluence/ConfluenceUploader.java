package cn.com.chinastock.cnf.docs.confluence;

import okhttp3.*;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;


/**
 * Confluence 上传逻辑，
 * ```json
 * {
 * "196158874": {
 * "pageId": 196158874,
 * "confluenceTitle": "开发框架组件总览",
 * "fileName": "README.md"
 * }
 * }
 * ```
 * 1. 根据 ContentId 获取 `version.number` 和 `content`
 * - API: `<a href="http://pmc.chinastock.com.cn/confluence/rest/api/content/">...</a>{contentId}?body.storage`
 * 2. 根据 body.storage.value 的内容，获取旧的内容，如果内容一致，则不上传
 * 3. 如果内容不一致，校验标题是否一致，则上传新的内容，
 * - API: `http://pmc.chinastock.com.cn/confluence/rest/api/content/{contentId}`
 * - 请求头：`Content-Type: application/json`
 * - Authorization: Basic base64(username:password)
 * - 请求体：`{"version":{"number":2},"space": { "key": "JSJGZL" }  "type":"page","body":{"storage":{"value":"<h2>API</h2>","representation":"storage"}}}`
 */
public class ConfluenceUploader {
    private static final String CONFLUENCE_BASE_URL = "http://pmc.chinastock.com.cn/confluence";
    private static final String SPACE_KEY = "JSJGZL";
    private static final OkHttpClient client = new OkHttpClient();

    private final String USERNAME;
    private final String PASSWORD;

    public ConfluenceUploader(String username, String password) {
        USERNAME = username;
        PASSWORD = password;
    }

    public void upload(HashMap<String, PageMapping> pageInfos) {
        for (Map.Entry<String, PageMapping> entry : pageInfos.entrySet()) {
            syncPage(entry.getValue());
        }
    }

    public void syncPage(PageMapping mapping) {
        String contentId = String.valueOf(mapping.getPageId());
        String fileName = mapping.getFileName().replace("/", File.separator);
        String confluenceTitle = mapping.getConfluenceTitle();

        try {
            String content = Files.readString(Paths.get("docs", fileName), StandardCharsets.UTF_8);
            String convertedConfluenceContent = MarkdownToConfluenceConverter.convert(content);

            System.out.println("准备更新：" + confluenceTitle);
            ConfluenceContent doc = getConfluenceContentAndVersion(contentId);

            if (convertedConfluenceContent.equals(doc.content)) {
                System.out.println(confluenceTitle + ": 内容一致，无需更新。");
            } else {
                System.out.println(confluenceTitle + ": 内容不一致，准备更新。");
                updateConfluenceContent(contentId, confluenceTitle, convertedConfluenceContent, doc.version + 1);
                System.out.println("更新完成。");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private ConfluenceContent getConfluenceContentAndVersion(String contentId) throws IOException {
        String url = CONFLUENCE_BASE_URL + "/rest/api/content/" + contentId + "?expand=version.number,body.storage";

        Request request = new Request.Builder()
                .url(url)
                .header("Authorization", getAuthHeader())
                .header("Accept", "*/*")
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (response.body() == null) {
                throw new IOException("Response body is null");
            }

            String jsonResponse = new String(response.body().bytes(), StandardCharsets.UTF_8);
            JSONObject jsonObject = new JSONObject(jsonResponse);

            String contentValue = jsonObject.getJSONObject("body")
                    .getJSONObject("storage")
                    .getString("value");
            int versionNumber = jsonObject.getJSONObject("version")
                    .getInt("number");

            return new ConfluenceContent(contentValue, versionNumber);
        }
    }

    public record ConfluenceContent(String content, int version) {
    }

    private void updateConfluenceContent(String contentId, String title, String content, int newVersion) throws IOException {
        String url = CONFLUENCE_BASE_URL + "/rest/api/content/" + contentId;

        // 创建请求体
        JSONObject json = new JSONObject();
        json.put("id", contentId);
        json.put("type", "page");
        json.put("title", title);

        // 版本信息
        JSONObject version = new JSONObject();
        version.put("number", newVersion);
        json.put("version", version);

        // 空间信息
        JSONObject space = new JSONObject();
        space.put("key", SPACE_KEY);
        json.put("space", space);

        // 内容
        JSONObject body = new JSONObject();
        JSONObject storage = new JSONObject();
        storage.put("value", content);
        storage.put("representation", "storage");
        body.put("storage", storage);
        json.put("body", body);

        RequestBody requestBody = RequestBody.create(json.toString(), MediaType.parse("application/json; charset=utf-8"));

        Request request = new Request.Builder()
                .url(url)
                .put(requestBody)
                .header("Authorization", getAuthHeader())
                .header("Accept", "*/*")
                .build();

        try (Response response = client.newCall(request).execute()) {
            assert response.body() != null;
            String jsonResponse = new String(response.body().bytes(), StandardCharsets.UTF_8);
            if (response.isSuccessful()) {
                System.out.println("页面更新成功: " + jsonResponse);
            } else {
                System.out.println("页面更新失败：" + jsonResponse);
            }
        }
    }

    private String getAuthHeader() {
        String auth = this.USERNAME + ":" + this.PASSWORD;
        String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes());
        return "Basic " + encodedAuth;
    }
}