package cn.com.chinastock.cnf.docs.template;

import org.apache.maven.model.Model;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.util.Map;
import java.util.Properties;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.com.chinastock.cnf.docs.template.TemplateCompiler.*;

public class TemplateValuePreparer {
    /**
     * prepareProperties 方法用于准备模板所需的属性，该方法接受一个 Map，其中包含路径和模型映射，并返回一个 Properties 对象，其中包含模板所需的所有属性。
     *
     * <p>该方法执行以下步骤：</p>
     *
     * <ol>
     *     <li>调用 collectStarterNames 方法收集所有启动器的名称，并将结果存储在 properties 对象中。</li>
     *     <li>调用 collectStartersReadme 方法收集所有 galaxy-boot-starter-* 模块的 README.md 文件内容，并将结果存储在 properties 对象中。</li>
     *     <li>遍历 pathModelMap 中的每个模型，检查其模块是否为 README.md 文件，如果是，则加载文件内容并转换为 UpperCamelCase 格式，然后存储在 properties 对象中。</li>
     * </ol>
     *
     * @param pathModelMap 包含路径和模型映射的 Map
     * @return 包含模板所需属性的 Properties 对象
     */
    public static Properties prepareProperties(Map<Path, Model> pathModelMap) {
        Properties properties = new Properties();
        String startersList = collectStarterNames(pathModelMap);

        properties.put(VariableNames.STARTER_NAMES.getValue(), startersList);
        properties.put(VariableNames.GALAXY_BOOT_STARTERS.getValue(), collectStartersReadme(pathModelMap));

        pathModelMap.forEach((path, model) -> {
            File readmeFile = new File(path.toFile().getParent(), "README.md");
            if (readmeFile.exists()) {
                String content;
                try {
                    content = loadTemplateContent(readmeFile);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }

                String artifactId = convertToUpperCaseCamelCase(model.getArtifactId());
                properties.put(artifactId, content);
            }
        });

        return properties;
    }

    /**
     * 将输入字符串转换为 UpperCamelCase 格式。
     *
     * <pre>
     *    {@code
     *        String result = TemplateValuePreparer.convertToUpperCaseCamelCase("hello-world");
     *        // result = "HelloWorld"
     *    }
     * </pre>
     *
     * @param input 要转换的字符串
     * @return 转换后的 UpperCamelCase 字符串
     */
    public static String convertToUpperCaseCamelCase(String input) {
        if (input == null || input.isEmpty()) {
            return "";
        }

        String[] words = input.split("-");
        StringBuilder result = new StringBuilder();

        for (String word : words) {
            if (!word.isEmpty()) {
                result.append(word.substring(0, 1).toUpperCase())
                        .append(word.substring(1).toLowerCase());
            }
        }

        return result.toString();
    }

    /**
     * 收集所有启动器的名称，并以换行符分隔的形式返回。
     *
     * <pre>
     *    {@code
     *        Map<Path, Model> pathModelMap = new HashMap<>();
     *        // 添加模型到 pathModelMap
     *        String starterNames = TemplateValuePreparer().collectStarterNames(pathModelMap);
     *        // starterNames 包含所有启动器的名称，每行一个
     *    }
     *    </pre>
     *
     * @param pathModelMap 包含路径和模型映射的 Map
     * @return 所有启动器名称的字符串，每行一个
     */
    private static String collectStarterNames(Map<Path, Model> pathModelMap) {
        return collectStarters(pathModelMap)
                .map(module -> "- [" + module + "](http://pmc.chinastock.com.cn/confluence/display/JSJGZL/" + module + ")")
                .collect(Collectors.joining(System.lineSeparator()));
    }

    private static Stream<String> collectStarters(Map<Path, Model> pathModelMap) {
        return pathModelMap.values().stream()
                .flatMap(model -> model.getModules().stream())
                .filter(module -> module.startsWith("galaxy-boot-starter-"));
    }


    /**
     * 收集所有 galaxy-boot-starter-* 模块的 README.md 文件内容
     *
     * <pre>
     *    {@code
     *        Map<Path, Model> pathModelMap = ... // 初始化 pathModelMap
     *        String readmeContent = TemplateValuePreparer().collectStartersReadme(pathModelMap);
     *        // readmeContent 包含所有 galaxy-boot-starter-* 模块的 README.md 文件内容
     *    }
     *    </pre>
     *
     * @param pathModelMap 路径和模型映射
     * @return 包含所有 galaxy-boot-starter-* 模块的 README.md 文件内容的字符串
     */
    private static Object collectStartersReadme(Map<Path, Model> pathModelMap) {
        return pathModelMap.keySet().stream()
                .filter(model -> {
                    String moduleName;
                    try {
                        moduleName = model.getParent().getFileName().toString();
                    } catch (Exception e) {
                        return false;
                    }

                    return moduleName.startsWith("galaxy-boot-starter-");
                })
                .map(module -> {
                    File readmeFile = new File(module.getParent().toFile(), "README.md");
                    if (readmeFile.exists()) {
                        try {
                            return loadTemplateContent(readmeFile);
                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        }
                    }
                    return "";
                })
                .collect(Collectors.joining(System.lineSeparator()));
    }
}
