package cn.com.chinastock.cnf.docs;

import cn.com.chinastock.cnf.docs.javadoc.JavaCommentParser;
import cn.com.chinastock.cnf.docs.javadoc.PackageComment;
import cn.com.chinastock.cnf.docs.maven.MavenUtil;
import org.apache.maven.model.Model;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;
import java.util.List;
import java.util.Map;

public class ModuleUserManualGenerator {
    public static final String CORE_PREFIX = "galaxy-boot-core-";
    public static final String CORE_MODULE = "galaxy-boot-core";

    public static void generate(File rootDir, Model model) throws Exception {
        Map<Path, Model> pathModelMap = MavenUtil.iterateAllModules(new File("pom.xml"));

        pathModelMap.forEach((path, childModel) -> {
            Path moduleDir = path.getParent();
            if (moduleDir == null) {
                return;
            }

            File file = new File(rootDir, moduleDir.toString());
            List<PackageComment> markdownFiles = JavaCommentParser.processJavaFiles(file);

            String moduleName = childModel.getArtifactId();
            try {
                if (!CORE_MODULE.equals(moduleName)) {
                    handleForNormalModule(file, markdownFiles, rootDir, moduleName);
                } else {
                    handleForCoreModule(file, markdownFiles, rootDir, moduleName);
                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });
    }

    /**
     * Security, Starters, Test, Utils 模块的逻辑
     *
     * <p>1. 读取 {moduleName}/README.md 文件作为前端的 【组件介绍】      </p>
     * <p>2. 读取 {moduleName}/docs/UserManual.md 来生成 【组件实例教程】</p>
     * <p>3.1 读取<strong>类级别</strong>注释来生成 【组件样例文档】        </p>
     * <p>3.2 读取<strong>类-方法级别</strong>注释来生成 【组件 API 列表】  </p>
     *
     * @param file          模块目录
     * @param markdownFiles Java 文件注释列表
     * @param rootDir       项目根目录
     * @param moduleName    模块名称
     */
    private static void handleForNormalModule(File file, List<PackageComment> markdownFiles, File rootDir, String moduleName) throws IOException {
        StringBuilder markdownFilesBuilder = new StringBuilder();

        // 1. 读取 README.md
        File readmeFile = new File(file, "README.md");
        if (!readmeFile.exists()) {
            return;
        }

        String readmeContent = new String(Files.readAllBytes(readmeFile.toPath()));
        markdownFilesBuilder.append(readmeContent);

        // 2. 读取 docs/UserManual.md
        File userManualFile = new File(file, "docs" + File.separatorChar + "UserManual.md");
        if (userManualFile.exists()) {
            String userManualContent = new String(Files.readAllBytes(userManualFile.toPath()));
            markdownFilesBuilder.append(userManualContent);
        }

        // 3. 生成类注释
        if (!markdownFiles.isEmpty()) {
            assembleMarkdownFromComments(markdownFiles, markdownFilesBuilder);
        }

        File outputFile = new File(rootDir, "docs" + File.separatorChar + moduleName + ".md");
        Files.write(outputFile.toPath(), markdownFilesBuilder.toString().getBytes());
    }

    /**
     * Core 模块的逻辑
     * 1. 复制 {moduleName}/README.md 到 docs/galaxy-boot-core.md
     * 2. 复制 {moduleName}/docs/galaxy-boot-core/galaxy-boot-core-{packageSuffix}.md 到 docs/galaxy-boot-core/galaxy-boot-core-{packageSuffix}.md
     * 3. 根据 packageSuffix 过滤出对应的 markdownFiles
     * 4. 生成 {packageSuffix} 对应的类注释，方法注释
     *
     * @param file          模块目录
     * @param markdownFiles Java 文件注释列表
     * @param rootDir       项目根目录
     * @param moduleName    模块名称
     */
    private static void handleForCoreModule(File file, List<PackageComment> markdownFiles, File rootDir, String moduleName) throws IOException {
        copyCoreModuleReadme(file, rootDir, moduleName);

        File[] coreDocFiles = new File(file, "docs").listFiles();
        assert coreDocFiles != null;

        // 复制 {moduleName}/docs/galaxy-boot-core/galaxy-boot-core-{packageSuffix}
        for (File coreDocFile : coreDocFiles) {
            String fileName = coreDocFile.getName();
            if (fileName.startsWith(CORE_PREFIX) && fileName.endsWith(".md")) {
                ///  创建 docs/galaxy-boot-core
                File coreDir = new File(rootDir, "docs" + File.separatorChar + CORE_MODULE);
                if (!coreDir.exists()) {
                    coreDir.mkdirs();
                }

                String packageSuffix = fileName.substring(CORE_PREFIX.length(), fileName.length() - ".md".length());

                File targetFile = overrideOriginalFileToDocs(rootDir, coreDocFile, fileName);

                // 根据 packageSuffix 过滤出对应的 markdownFiles
                List<PackageComment> filteredMarkdownFiles = markdownFiles.stream()
                        .filter(pkgComment -> pkgComment.packageName.endsWith(packageSuffix))
                        .toList();

                // 添加类注释，方法注释
                StringBuilder markdownFilesBuilder = new StringBuilder();
                assembleMarkdownFromComments(filteredMarkdownFiles, markdownFilesBuilder);
                Files.write(targetFile.toPath(), markdownFilesBuilder.toString().getBytes(), StandardOpenOption.APPEND);
            }
        }
    }

    private static File overrideOriginalFileToDocs(File rootDir, File coreDocFile, String fileName) throws IOException {
        File targetFile = new File(rootDir, "docs" + File.separatorChar + CORE_MODULE + File.separatorChar + fileName);
        if (targetFile.exists()) {
            targetFile.delete();
        }
        Files.write(targetFile.toPath(), Files.readAllBytes(coreDocFile.toPath()));
        return targetFile;
    }

    /**
     * 复制 {moduleName}/README.md 到 docs/galaxy-boot-core.md
     *
     * @param file       模块目录
     * @param rootDir    项目根目录
     * @param moduleName 模块名称
     */
    private static void copyCoreModuleReadme(File file, File rootDir, String moduleName) throws IOException {
        File readmeFile = new File(file, "README.md");
        String readmeContent = new String(Files.readAllBytes(readmeFile.toPath()));
        File outputFile = new File(rootDir, "docs" + File.separatorChar + moduleName + ".md");
        Files.write(outputFile.toPath(), readmeContent.getBytes());
    }

    private static void assembleMarkdownFromComments(List<PackageComment> markdownFiles, StringBuilder markdownFilesBuilder) {
        markdownFilesBuilder.append("\n\n### 组件样例文档\n\n");
        markdownFiles.forEach(packageComment -> markdownFilesBuilder.append(packageComment.classComment).append("\n"));

        // 4. 生成方法注释
        markdownFilesBuilder.append("\n\n### 组件 API 列表\n\n");
        markdownFiles.forEach(packageComment -> {
            markdownFilesBuilder.append("#### ").append(packageComment.className).append("\n\n");
            packageComment.methodComments.forEach(methodComment -> markdownFilesBuilder.append(methodComment).append("\n"));
        });
    }
}