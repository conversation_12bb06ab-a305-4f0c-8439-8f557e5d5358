server:
  port: 8080
  # 允许远程访问
  address: 0.0.0.0

spring:
  application:
    name: galaxy-boot-mcp-example
  # 跨域配置
  web:
    cors:
      allowed-origins: "*"
      allowed-methods: GET,POST,PUT,DELETE,OPTIONS
      allowed-headers: "*"
      allow-credentials: false

# Galaxy Boot MCP 配置
galaxy:
  mcp:
    # 启用MCP功能
    enabled: true
    # 服务器信息
    server-name: galaxy-mcp-example-server
    server-version: 1.0.0
    server-description: Galaxy Boot MCP Example Server - 演示如何使用@McpTool注解
    # 自动扫描配置
    auto-scan: true
    scan-packages:
      - cn.com.chinastock.cnf.examples.mcp.controller
    # 工具配置
    tool:
      enabled: true
      timeout-seconds: 30
      # 只暴露public标签的工具（可选）
      # tag-filters:
      #   - public
      # 只暴露特定分组的工具（可选）
      # group-filters:
      #   - user
      #   - math
      # 单个工具的自定义配置
      items:
        getUserById:
          enabled: true
          description: "根据用户ID获取用户详细信息（自定义描述）"
        createUser:
          enabled: true
          response-mime-type: "application/json"
    # 传输配置
    transport:
      type: webmvc
      sse-endpoint: /mcp/sse
      message-endpoint: /mcp/message
      base-url: ""
    # 安全配置（可选）
    security:
      enabled: false
      # api-key: your-api-key
      # allowed-ips:
      #   - 127.0.0.1
      #   - ***********/24
  # Galaxy AI 配置
  ai:
    enabled: true
    base-url: ${GALAXY_AI_BASE_URL:http://copilot.prodgpu.chinastock.com.cn}
    system-id: ${GALAXY_AI_SYSTEM_ID:}
    system-secret: ${GALAXY_AI_SYSTEM_SECRET:}
    account: ${GALAXY_AI_ACCOUNT:}
    app-id: ${GALAXY_AI_APP_ID:}
    connect-timeout: 10000
    read-timeout: 60000
    write-timeout: 60000
    default-model: galaxy-ai
  log:
    exception-pretty-print: true

# 日志配置
logging:
  level:
    cn.com.chinastock.cnf.mcp: INFO
    cn.com.chinastock.cnf.examples.mcp: INFO
    org.springframework.ai.mcp: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
