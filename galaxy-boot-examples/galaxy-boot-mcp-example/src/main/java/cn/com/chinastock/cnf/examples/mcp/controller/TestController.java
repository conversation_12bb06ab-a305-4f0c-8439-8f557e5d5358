package cn.com.chinastock.cnf.examples.mcp.controller;

import cn.com.chinastock.cnf.examples.mcp.config.GalaxyAiProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器，用于验证配置和服务状态
 * 
 * <AUTHOR> Boot Team
 * @since 0.1.1
 */
@RestController
@RequestMapping("/api/test")
public class TestController {
    
    @Autowired
    private GalaxyAiProperties galaxyAiProperties;
    
    /**
     * 获取 Galaxy AI 配置状态
     */
    @GetMapping("/galaxy-ai-config")
    public Map<String, Object> getGalaxyAiConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("enabled", galaxyAiProperties.isEnabled());
        config.put("baseUrl", galaxyAiProperties.getBaseUrl());
        config.put("defaultModel", galaxyAiProperties.getDefaultModel());
        config.put("connectTimeout", galaxyAiProperties.getConnectTimeout());
        config.put("readTimeout", galaxyAiProperties.getReadTimeout());
        config.put("writeTimeout", galaxyAiProperties.getWriteTimeout());
        
        // 不暴露敏感信息
        config.put("systemIdConfigured", galaxyAiProperties.getSystemId() != null && !galaxyAiProperties.getSystemId().trim().isEmpty());
        config.put("systemSecretConfigured", galaxyAiProperties.getSystemSecret() != null && !galaxyAiProperties.getSystemSecret().trim().isEmpty());
        config.put("accountConfigured", galaxyAiProperties.getAccount() != null && !galaxyAiProperties.getAccount().trim().isEmpty());
        config.put("appIdConfigured", galaxyAiProperties.getAppId() != null && !galaxyAiProperties.getAppId().trim().isEmpty());
        
        return config;
    }
    
    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("timestamp", System.currentTimeMillis());
        health.put("service", "galaxy-ai-proxy");
        
        try {
            galaxyAiProperties.validate();
            health.put("galaxyAiConfig", "VALID");
        } catch (Exception e) {
            health.put("galaxyAiConfig", "INVALID");
            health.put("configError", e.getMessage());
        }
        
        return health;
    }
}
