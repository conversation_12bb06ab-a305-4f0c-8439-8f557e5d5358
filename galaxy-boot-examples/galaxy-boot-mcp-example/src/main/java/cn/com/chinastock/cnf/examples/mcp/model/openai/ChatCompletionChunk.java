package cn.com.chinastock.cnf.examples.mcp.model.openai;

import com.alibaba.fastjson2.annotation.JSONField;

import java.util.List;

/**
 * OpenAI Chat Completion 流式响应块模型
 * 
 * <AUTHOR> Boot Team
 * @since 0.1.1
 */
public class ChatCompletionChunk {
    
    /**
     * 响应 ID
     */
    @JSONField(name = "id")
    private String id;
    
    /**
     * 对象类型
     */
    @JSONField(name = "object")
    private String object = "chat.completion.chunk";
    
    /**
     * 创建时间戳
     */
    @JSONField(name = "created")
    private Long created;
    
    /**
     * 模型名称
     */
    @JSONField(name = "model")
    private String model;
    
    /**
     * 系统指纹
     */
    @JSONField(name = "system_fingerprint")
    private String systemFingerprint;
    
    /**
     * 选择列表
     */
    @JSONField(name = "choices")
    private List<ChunkChoice> choices;
    
    public ChatCompletionChunk() {
        this.created = System.currentTimeMillis() / 1000;
    }
    
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getObject() {
        return object;
    }
    
    public void setObject(String object) {
        this.object = object;
    }
    
    public Long getCreated() {
        return created;
    }
    
    public void setCreated(Long created) {
        this.created = created;
    }
    
    public String getModel() {
        return model;
    }
    
    public void setModel(String model) {
        this.model = model;
    }
    
    public String getSystemFingerprint() {
        return systemFingerprint;
    }
    
    public void setSystemFingerprint(String systemFingerprint) {
        this.systemFingerprint = systemFingerprint;
    }
    
    public List<ChunkChoice> getChoices() {
        return choices;
    }
    
    public void setChoices(List<ChunkChoice> choices) {
        this.choices = choices;
    }
    
    /**
     * 流式响应选择项
     */
    public static class ChunkChoice {
        @JSONField(name = "index")
        private Integer index;
        
        @JSONField(name = "delta")
        private Delta delta;
        
        @JSONField(name = "finish_reason")
        private String finishReason;
        
        public ChunkChoice() {
        }
        
        public ChunkChoice(Integer index, Delta delta, String finishReason) {
            this.index = index;
            this.delta = delta;
            this.finishReason = finishReason;
        }
        
        public Integer getIndex() {
            return index;
        }
        
        public void setIndex(Integer index) {
            this.index = index;
        }
        
        public Delta getDelta() {
            return delta;
        }
        
        public void setDelta(Delta delta) {
            this.delta = delta;
        }
        
        public String getFinishReason() {
            return finishReason;
        }
        
        public void setFinishReason(String finishReason) {
            this.finishReason = finishReason;
        }
    }
    
    /**
     * 增量消息
     */
    public static class Delta {
        @JSONField(name = "role")
        private String role;
        
        @JSONField(name = "content")
        private String content;
        
        public Delta() {
        }
        
        public Delta(String role, String content) {
            this.role = role;
            this.content = content;
        }
        
        public String getRole() {
            return role;
        }
        
        public void setRole(String role) {
            this.role = role;
        }
        
        public String getContent() {
            return content;
        }
        
        public void setContent(String content) {
            this.content = content;
        }
    }
}
