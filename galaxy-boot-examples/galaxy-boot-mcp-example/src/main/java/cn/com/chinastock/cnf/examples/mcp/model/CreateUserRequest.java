package cn.com.chinastock.cnf.examples.mcp.model;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;

/**
 * 创建用户请求
 * 
 * <AUTHOR> Boot Team
 * @since 0.1.1
 */
public class CreateUserRequest {
    
    @NotBlank(message = "用户名不能为空")
    private String username;
    
    @NotBlank(message = "姓名不能为空")
    private String name;
    
    @Email(message = "邮箱格式不正确")
    private String email;
    
    @NotNull(message = "年龄不能为空")
    @Min(value = 1, message = "年龄必须大于0")
    private Integer age;
    
    private String department;
    
    private String position;
    
    // 构造函数
    public CreateUserRequest() {}
    
    public CreateUserRequest(String username, String name, String email, Integer age, 
                            String department, String position) {
        this.username = username;
        this.name = name;
        this.email = email;
        this.age = age;
        this.department = department;
        this.position = position;
    }
    
    // Getters and Setters
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public Integer getAge() {
        return age;
    }
    
    public void setAge(Integer age) {
        this.age = age;
    }
    
    public String getDepartment() {
        return department;
    }
    
    public void setDepartment(String department) {
        this.department = department;
    }
    
    public String getPosition() {
        return position;
    }
    
    public void setPosition(String position) {
        this.position = position;
    }
    
    @Override
    public String toString() {
        return "CreateUserRequest{" +
               "username='" + username + '\'' +
               ", name='" + name + '\'' +
               ", email='" + email + '\'' +
               ", age=" + age +
               ", department='" + department + '\'' +
               ", position='" + position + '\'' +
               '}';
    }
}
