package cn.com.chinastock.cnf.examples.mcp.service;

import cn.com.chinastock.cnf.examples.mcp.config.GalaxyAiProperties;
import cn.com.chinastock.cnf.examples.mcp.model.openai.ChatMessage;
import com.alibaba.fastjson2.JSON;
import okhttp3.*;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import okhttp3.sse.EventSources;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import jakarta.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;


/**
 * Galaxy AI 服务类
 * 
 * <AUTHOR> Boot Team
 * @since 0.1.1
 */
@Service
public class GalaxyAiService {
    
    private static final Logger logger = LoggerFactory.getLogger(GalaxyAiService.class);
    
    @Autowired
    private GalaxyAiProperties properties;
    
    private OkHttpClient httpClient;
    private volatile String accessToken;
    private volatile long tokenExpiry;
    
    @PostConstruct
    public void init() {
        // 验证配置
        properties.validate();
        
        if (!properties.isEnabled()) {
            logger.info("Galaxy AI service is disabled");
            return;
        }
        
        // 初始化 HTTP 客户端
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(properties.getConnectTimeout(), TimeUnit.MILLISECONDS)
                .readTimeout(properties.getReadTimeout(), TimeUnit.MILLISECONDS)
                .writeTimeout(properties.getWriteTimeout(), TimeUnit.MILLISECONDS)
                .build();
        
        logger.info("Galaxy AI service initialized with base URL: {}", properties.getBaseUrl());
    }
    
    /**
     * 获取系统 Token
     */
    public String getSystemToken() throws Exception {
        String userNonce = String.valueOf(System.currentTimeMillis());
        String signatureString = properties.getSystemId() + "\t" + properties.getSystemSecret() + "\t" + userNonce;
        String systemToken = md5(signatureString);
        
        Map<String, Object> requestData = new HashMap<>();
        requestData.put("user-nonce", userNonce);
        requestData.put("System-Token", systemToken);
        requestData.put("System-Id", properties.getSystemId());
        requestData.put("account", properties.getAccount());
        
        RequestBody body = RequestBody.create(
                JSON.toJSONString(requestData),
                MediaType.get("application/json; charset=utf-8")
        );
        
        Request request = new Request.Builder()
                .url(properties.getBaseUrl() + "/api/system_token")
                .post(body)
                .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Failed to get system token: " + response.code() + " " + response.message());
            }
            
            String responseBody = response.body().string();
            @SuppressWarnings("unchecked")
            Map<String, Object> responseData = JSON.parseObject(responseBody, Map.class);
            
            String token = (String) responseData.get("access_token");
            if (token == null || token.trim().isEmpty()) {
                throw new IOException("No access token in response: " + responseBody);
            }
            
            this.accessToken = token;
            this.tokenExpiry = System.currentTimeMillis() + properties.getTokenValidityPeriod();
            
            logger.info("Successfully obtained system token");
            return token;
        }
    }
    
    /**
     * 确保 Token 有效
     */
    public void ensureValidToken() throws Exception {
        if (accessToken == null || System.currentTimeMillis() >= tokenExpiry) {
            synchronized (this) {
                if (accessToken == null || System.currentTimeMillis() >= tokenExpiry) {
                    getSystemToken();
                }
            }
        }
    }
    
    /**
     * 发起聊天请求（流式）
     */
    public void chatStream(List<ChatMessage> messages, String conversationId, SseEmitter emitter) throws Exception {
        ensureValidToken();

        final String finalConversationId;
        if (conversationId == null || conversationId.trim().isEmpty()) {
            finalConversationId = "chat-" + System.currentTimeMillis() + "-" + UUID.randomUUID().toString().substring(0, 8);
        } else {
            finalConversationId = conversationId;
        }
        
        Map<String, Object> requestData = new HashMap<>();
        requestData.put("messages", messages);
        requestData.put("conversation_id", finalConversationId);
        requestData.put("stream", true);
        requestData.put("app_id", properties.getAppId());
        requestData.put("tag", List.of("openai-proxy"));
        
        Request request = new Request.Builder()
                .url(properties.getBaseUrl() + "/api/app/xchat")
                .header("Authorization", "Bearer " + accessToken)
                .header("Accept", "text/event-stream")
                .header("Content-Type", "application/json")
                .post(RequestBody.create(
                        JSON.toJSONString(requestData),
                        MediaType.get("application/json; charset=utf-8")
                ))
                .build();
        
        EventSources.createFactory(httpClient).newEventSource(request, new EventSourceListener() {
            @Override
            public void onOpen(EventSource eventSource, Response response) {
                logger.debug("Galaxy AI SSE connection opened");
            }
            
            @Override
            public void onEvent(EventSource eventSource, String id, String type, String data) {
                try {
                    if ("[DONE]".equals(data)) {
                        emitter.send(SseEmitter.event().data("data: [DONE]\n\n"));
                        emitter.complete();
                        return;
                    }
                    
                    if (data != null && !data.trim().isEmpty() && !"event: ping".equals(data)) {
                        // 解析 Galaxy 响应
                        @SuppressWarnings("unchecked")
                        Map<String, Object> galaxyResponse = JSON.parseObject(data, Map.class);

                        // 处理不同类型的响应
                        String objectType = (String) galaxyResponse.get("object");
                        if ("answer".equals(objectType)) {
                            String deltaContent = (String) galaxyResponse.get("delta");
                            String finishReason = (String) galaxyResponse.get("finish_reason");

                            if (deltaContent != null || finishReason != null) {
                                // 转换为 OpenAI 格式
                                String openAiChunk = convertGalaxyToOpenAiChunk(galaxyResponse, finalConversationId);
                                emitter.send(SseEmitter.event().data("data: " + openAiChunk + "\n\n"));
                            }
                        }
                        // 忽略 "thought" 类型的消息，因为 OpenAI 格式中没有对应的概念
                    }
                } catch (Exception e) {
                    logger.error("Error processing SSE event", e);
                    emitter.completeWithError(e);
                }
            }
            
            @Override
            public void onClosed(EventSource eventSource) {
                logger.debug("Galaxy AI SSE connection closed");
                try {
                    emitter.complete();
                } catch (Exception e) {
                    logger.warn("Error completing emitter", e);
                }
            }
            
            @Override
            public void onFailure(EventSource eventSource, Throwable t, Response response) {
                logger.error("Galaxy AI SSE connection failed", t);
                emitter.completeWithError(t);
            }
        });
    }
    
    /**
     * 将 Galaxy AI 响应转换为 OpenAI 格式的 chunk
     */
    private String convertGalaxyToOpenAiChunk(Map<String, Object> galaxyResponse, String conversationId) {
        Map<String, Object> chunk = new HashMap<>();

        // 使用 Galaxy 的 ID 或生成新的
        String galaxyId = (String) galaxyResponse.get("id");
        chunk.put("id", galaxyId != null ? galaxyId : conversationId);
        chunk.put("object", "chat.completion.chunk");

        // 转换时间戳
        String createdStr = (String) galaxyResponse.get("created");
        if (createdStr != null) {
            try {
                // Galaxy 返回的是 ISO 格式时间，需要转换为 Unix 时间戳
                chunk.put("created", System.currentTimeMillis() / 1000);
            } catch (Exception e) {
                chunk.put("created", System.currentTimeMillis() / 1000);
            }
        } else {
            chunk.put("created", System.currentTimeMillis() / 1000);
        }

        chunk.put("model", properties.getDefaultModel());

        // 构建 choices
        Map<String, Object> choice = new HashMap<>();
        choice.put("index", 0);

        // 处理 finish_reason
        String finishReason = (String) galaxyResponse.get("finish_reason");
        choice.put("finish_reason", finishReason);

        // 构建 delta
        Map<String, Object> delta = new HashMap<>();
        String deltaContent = (String) galaxyResponse.get("delta");
        if (deltaContent != null) {
            delta.put("content", deltaContent);
        }

        // 对于第一个有内容的 chunk，需要设置 role
        // 这里简化处理，如果有内容且 finish_reason 为 null，就认为可能需要 role
        if (deltaContent != null && !deltaContent.trim().isEmpty() && finishReason == null) {
            // 在实际应用中，可能需要更复杂的逻辑来判断是否是第一个 chunk
            // 这里为了简化，我们不设置 role，让客户端自己处理
        }

        choice.put("delta", delta);
        chunk.put("choices", List.of(choice));

        return JSON.toJSONString(chunk);
    }
    
    /**
     * MD5 加密
     */
    private String md5(String input) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] digest = md.digest(input.getBytes(StandardCharsets.UTF_8));
        StringBuilder sb = new StringBuilder();
        for (byte b : digest) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }
}
