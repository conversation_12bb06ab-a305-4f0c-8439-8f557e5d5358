package cn.com.chinastock.cnf.examples.mcp.controller;

import cn.com.chinastock.cnf.mcp.service.McpToolService;
import cn.com.chinastock.cnf.mcp.model.McpToolInfo;
import cn.com.chinastock.cnf.mcp.converter.ControllerToolConverter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * MCP 协议端点控制器
 * 
 * 提供 MCP 协议的 HTTP 和 SSE 端点，用于远程客户端连接
 * 
 * <AUTHOR> Boot Team
 * @since 0.1.1
 */
@RestController
@RequestMapping("/mcp")
@CrossOrigin(origins = "*")
public class McpController {
    
    private static final Logger logger = LoggerFactory.getLogger(McpController.class);
    
    @Autowired
    private McpToolService mcpToolService;

    @Autowired
    private ControllerToolConverter toolConverter;

    private final Map<String, SseEmitter> sseClients = new ConcurrentHashMap<>();
    
    /**
     * MCP 消息端点 - 处理 JSON-RPC 请求
     */
    @PostMapping(value = "/message", 
                consumes = MediaType.APPLICATION_JSON_VALUE,
                produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Map<String, Object>> handleMessage(@RequestBody Map<String, Object> request) {
        logger.info("Received MCP request: {}", request);
        
        try {
            String method = (String) request.get("method");
            Object id = request.get("id");
            
            Map<String, Object> response = new HashMap<>();
            response.put("jsonrpc", "2.0");
            response.put("id", id);
            
            switch (method) {
                case "initialize":
                    response.put("result", handleInitialize());
                    break;
                case "tools/list":
                    response.put("result", handleToolsList());
                    break;
                case "tools/call":
                    Map<String, Object> params = (Map<String, Object>) request.get("params");
                    response.put("result", handleToolCall(params));
                    break;
                default:
                    response.put("error", Map.of(
                        "code", -32601,
                        "message", "Method not found: " + method
                    ));
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Error handling MCP request", e);
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("jsonrpc", "2.0");
            errorResponse.put("id", request.get("id"));
            errorResponse.put("error", Map.of(
                "code", -32603,
                "message", "Internal error: " + e.getMessage()
            ));
            
            return ResponseEntity.ok(errorResponse);
        }
    }
    
    /**
     * SSE 端点 - 用于实时通信
     */
    @GetMapping(value = "/sse", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter handleSse(@RequestParam(defaultValue = "default") String clientId) {
        logger.info("New SSE client connected: {}", clientId);
        
        SseEmitter emitter = new SseEmitter(Long.MAX_VALUE);
        sseClients.put(clientId, emitter);
        
        emitter.onCompletion(() -> {
            logger.info("SSE client disconnected: {}", clientId);
            sseClients.remove(clientId);
        });
        
        emitter.onError((ex) -> {
            logger.error("SSE error for client: " + clientId, ex);
            sseClients.remove(clientId);
        });
        
        emitter.onTimeout(() -> {
            logger.warn("SSE timeout for client: {}", clientId);
            sseClients.remove(clientId);
        });
        
        // 发送连接确认
        try {
            emitter.send(SseEmitter.event()
                .name("connected")
                .data(Map.of("message", "Connected to Galaxy MCP Server", "clientId", clientId)));
        } catch (Exception e) {
            logger.error("Error sending SSE welcome message", e);
        }
        
        return emitter;
    }
    
    /**
     * 获取服务器信息
     */
    @GetMapping("/info")
    public ResponseEntity<Map<String, Object>> getServerInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("name", "Galaxy Boot MCP Server");
        info.put("version", "1.0.0");
        info.put("description", "Galaxy Boot MCP Example Server");
        info.put("capabilities", Map.of(
            "tools", true,
            "resources", false,
            "prompts", false
        ));
        
        List<McpToolInfo> tools = mcpToolService.getDiscoveredTools();
        info.put("toolCount", tools.size());
        info.put("connectedClients", sseClients.size());
        
        return ResponseEntity.ok(info);
    }
    
    /**
     * 处理初始化请求
     */
    private Map<String, Object> handleInitialize() {
        Map<String, Object> result = new HashMap<>();
        result.put("protocolVersion", "2024-11-05");
        result.put("capabilities", Map.of(
            "tools", Map.of("listChanged", true),
            "resources", Map.of("subscribe", false, "listChanged", false),
            "prompts", Map.of("listChanged", false)
        ));
        result.put("serverInfo", Map.of(
            "name", "Galaxy Boot MCP Server",
            "version", "1.0.0"
        ));
        
        return result;
    }
    
    /**
     * 处理工具列表请求
     */
    private Map<String, Object> handleToolsList() {
        List<McpToolInfo> discoveredTools = mcpToolService.getDiscoveredTools();
        
        List<Map<String, Object>> tools = discoveredTools.stream()
            .map(tool -> {
                Map<String, Object> toolMap = new HashMap<>();
                toolMap.put("name", tool.getName());
                toolMap.put("description", tool.getDescription());
                
                // 简化的输入 schema
                Map<String, Object> inputSchema = new HashMap<>();
                inputSchema.put("type", "object");
                inputSchema.put("properties", Map.of());
                toolMap.put("inputSchema", inputSchema);
                
                return toolMap;
            })
            .toList();
        
        return Map.of("tools", tools);
    }
    
    /**
     * 处理工具调用请求
     */
    private Map<String, Object> handleToolCall(Map<String, Object> params) {
        String toolName = (String) params.get("name");
        Map<String, Object> arguments = (Map<String, Object>) params.get("arguments");

        logger.info("Calling tool: {} with arguments: {}", toolName, arguments);

        try {
            // 查找对应的工具信息
            List<McpToolInfo> discoveredTools = mcpToolService.getDiscoveredTools();
            McpToolInfo targetTool = discoveredTools.stream()
                .filter(tool -> tool.getName().equals(toolName))
                .findFirst()
                .orElse(null);

            if (targetTool == null) {
                return Map.of(
                    "content", List.of(Map.of(
                        "type", "text",
                        "text", "Tool not found: " + toolName
                    )),
                    "isError", true
                );
            }

            // 使用 ControllerToolConverter 执行实际的方法调用
            Object result = toolConverter.executeToolMethod(targetTool, arguments);

            // 将结果转换为 MCP 响应格式
            String resultText;
            if (result != null) {
                if (result instanceof String) {
                    resultText = (String) result;
                } else if (result instanceof Number) {
                    resultText = result.toString();
                } else {
                    // 对于复杂对象，转换为 JSON 字符串
                    resultText = convertToJsonString(result);
                }
            } else {
                resultText = "null";
            }

            return Map.of(
                "content", List.of(Map.of(
                    "type", "text",
                    "text", resultText
                )),
                "isError", false
            );

        } catch (Exception e) {
            logger.error("Error executing tool: " + toolName, e);
            return Map.of(
                "content", List.of(Map.of(
                    "type", "text",
                    "text", "Error executing tool: " + e.getMessage()
                )),
                "isError", true
            );
        }
    }

    /**
     * 将对象转换为 JSON 字符串
     */
    private String convertToJsonString(Object obj) {
        try {
            // 使用简单的 toString 或者可以集成 Jackson
            if (obj instanceof List || obj instanceof Map) {
                return obj.toString();
            } else {
                return obj.toString();
            }
        } catch (Exception e) {
            return "Error converting result to string: " + e.getMessage();
        }
    }
    
    /**
     * 向所有 SSE 客户端广播消息
     */
    public void broadcastToSseClients(String eventName, Object data) {
        sseClients.entrySet().removeIf(entry -> {
            try {
                entry.getValue().send(SseEmitter.event().name(eventName).data(data));
                return false;
            } catch (Exception e) {
                logger.warn("Failed to send SSE message to client: {}", entry.getKey());
                return true;
            }
        });
    }
}
