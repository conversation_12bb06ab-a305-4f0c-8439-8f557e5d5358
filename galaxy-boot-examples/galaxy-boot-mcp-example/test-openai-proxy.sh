#!/bin/bash

# Galaxy AI OpenAI 代理测试脚本

BASE_URL="http://localhost:8080"

echo "=== Galaxy AI OpenAI 代理测试 ==="
echo

# 1. 健康检查
echo "1. 健康检查..."
curl -s "$BASE_URL/api/test/health" | jq '.' || echo "健康检查失败"
echo
echo

# 2. 配置检查
echo "2. 配置检查..."
curl -s "$BASE_URL/api/test/galaxy-ai-config" | jq '.' || echo "配置检查失败"
echo
echo

# 3. 测试 OpenAI 兼容接口（流式）
echo "3. 测试 OpenAI 兼容接口（流式）..."
echo "发送请求到 /v1/chat/completions..."

curl -X POST "$BASE_URL/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Accept: text/event-stream" \
  -d '{
    "model": "galaxy-ai",
    "messages": [
      {
        "role": "user",
        "content": "你好，请简单介绍一下你自己"
      }
    ],
    "stream": true
  }' \
  --no-buffer \
  -v

echo
echo

# 4. 测试非流式请求（应该返回错误）
echo "4. 测试非流式请求（应该返回错误）..."
curl -X POST "$BASE_URL/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "galaxy-ai",
    "messages": [
      {
        "role": "user",
        "content": "你好"
      }
    ],
    "stream": false
  }' | jq '.' || echo "请求失败"

echo
echo

# 5. 测试无效请求
echo "5. 测试无效请求（缺少 messages）..."
curl -X POST "$BASE_URL/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "galaxy-ai",
    "stream": true
  }' | jq '.' || echo "请求失败"

echo
echo "=== 测试完成 ==="
