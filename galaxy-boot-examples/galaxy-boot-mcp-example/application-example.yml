# Galaxy AI OpenAI 代理配置示例
# 复制此文件为 application-local.yml 并填入实际配置值

server:
  port: 8080

spring:
  application:
    name: galaxy-ai-openai-proxy

# Galaxy Boot MCP 配置（如果需要）
galaxy:
  mcp:
    enabled: true
    server-name: galaxy-ai-openai-proxy
    server-version: 1.0.0
    server-description: Galaxy AI OpenAI Compatible Proxy
    auto-scan: true
    scan-packages:
      - cn.com.chinastock.cnf.examples.mcp.controller
    tool:
      enabled: true
      timeout-seconds: 30
    transport:
      type: webmvc
      sse-endpoint: /mcp/sse
      message-endpoint: /mcp/message
      base-url: ""
    security:
      enabled: false

  # Galaxy AI 配置
  ai:
    enabled: true
    base-url: http://copilot.prodgpu.chinastock.com.cn
    # 以下配置请通过环境变量设置，不要直接写在配置文件中
    system-id: ${GALAXY_AI_SYSTEM_ID:your-system-id}
    system-secret: ${GALAXY_AI_SYSTEM_SECRET:your-system-secret}
    account: ${GALAXY_AI_ACCOUNT:your-account}
    app-id: ${GALAXY_AI_APP_ID:your-app-id}
    connect-timeout: 10000
    read-timeout: 60000
    write-timeout: 60000
    default-model: galaxy-ai

  log:
    exception-pretty-print: true

# 日志配置
logging:
  level:
    cn.com.chinastock.cnf.examples.mcp: INFO
    cn.com.chinastock.cnf.mcp: INFO
    org.springframework.ai.mcp: INFO
    # 调试时可以开启 DEBUG 级别
    # cn.com.chinastock.cnf.examples.mcp.service.GalaxyAiService: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

---
# 环境变量配置说明
# 
# 请设置以下环境变量：
# export GALAXY_AI_SYSTEM_ID="your-actual-system-id"
# export GALAXY_AI_SYSTEM_SECRET="your-actual-system-secret"
# export GALAXY_AI_ACCOUNT="your-actual-account"
# export GALAXY_AI_APP_ID="your-actual-app-id"
#
# 或者在 IDE 中配置运行参数：
# -DGALAXY_AI_SYSTEM_ID=your-actual-system-id
# -DGALAXY_AI_SYSTEM_SECRET=your-actual-system-secret
# -DGALAXY_AI_ACCOUNT=your-actual-account
# -DGALAXY_AI_APP_ID=your-actual-app-id
