#!/usr/bin/env node

/**
 * Galaxy AI OpenAI 代理格式转换测试脚本
 * 用于测试 Galaxy AI 响应格式到 OpenAI 格式的转换
 */

const http = require('http');

const BASE_URL = 'http://localhost:8080';

// 测试用的 OpenAI 格式请求
const openAiRequest = {
    model: 'galaxy-ai',
    messages: [
        {
            role: 'user',
            content: '你好，请简单介绍一下你自己，不超过50字'
        }
    ],
    stream: true,
    temperature: 0.7
};

console.log('=== Galaxy AI OpenAI 代理格式转换测试 ===\n');

// 发送请求并处理流式响应
function testStreamingResponse() {
    const postData = JSON.stringify(openAiRequest);
    
    const options = {
        hostname: 'localhost',
        port: 8080,
        path: '/v1/chat/completions',
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
            'Content-Length': Buffer.byteLength(postData)
        }
    };

    console.log('发送请求到:', `${BASE_URL}/v1/chat/completions`);
    console.log('请求体:', JSON.stringify(openAiRequest, null, 2));
    console.log('\n--- 流式响应 ---');

    const req = http.request(options, (res) => {
        console.log(`状态码: ${res.statusCode}`);
        console.log(`响应头:`, res.headers);
        console.log('\n--- SSE 数据流 ---');

        let buffer = '';
        let chunkCount = 0;
        let totalContent = '';

        res.on('data', (chunk) => {
            buffer += chunk.toString();
            const lines = buffer.split('\n');
            buffer = lines.pop() || ''; // 保留最后一行（可能不完整）

            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    const data = line.slice(6);
                    
                    if (data === '[DONE]') {
                        console.log('\n--- 流结束 ---');
                        console.log(`总共接收到 ${chunkCount} 个数据块`);
                        console.log(`完整内容: "${totalContent}"`);
                        return;
                    }

                    try {
                        const parsed = JSON.parse(data);
                        chunkCount++;
                        
                        console.log(`\n[Chunk ${chunkCount}]`);
                        console.log('原始数据:', JSON.stringify(parsed, null, 2));
                        
                        // 验证 OpenAI 格式
                        if (parsed.object === 'chat.completion.chunk') {
                            console.log('✅ 格式正确: chat.completion.chunk');
                            
                            if (parsed.choices && parsed.choices[0]) {
                                const choice = parsed.choices[0];
                                if (choice.delta && choice.delta.content) {
                                    totalContent += choice.delta.content;
                                    console.log(`📝 内容: "${choice.delta.content}"`);
                                }
                                
                                if (choice.finish_reason) {
                                    console.log(`🏁 结束原因: ${choice.finish_reason}`);
                                }
                            }
                        } else {
                            console.log('❌ 格式错误: 不是 chat.completion.chunk');
                        }
                        
                    } catch (e) {
                        console.log('❌ JSON 解析错误:', e.message);
                        console.log('原始数据:', data);
                    }
                }
            }
        });

        res.on('end', () => {
            console.log('\n--- 响应结束 ---');
        });

        res.on('error', (err) => {
            console.error('响应错误:', err);
        });
    });

    req.on('error', (err) => {
        console.error('请求错误:', err);
    });

    req.write(postData);
    req.end();
}

// 测试配置检查
function testConfig() {
    console.log('1. 检查配置...');
    
    const options = {
        hostname: 'localhost',
        port: 8080,
        path: '/api/test/galaxy-ai-config',
        method: 'GET'
    };

    const req = http.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            try {
                const config = JSON.parse(data);
                console.log('配置状态:', JSON.stringify(config, null, 2));
                
                if (config.enabled && config.systemIdConfigured && 
                    config.systemSecretConfigured && config.accountConfigured && 
                    config.appIdConfigured) {
                    console.log('✅ 配置完整，开始测试流式响应...\n');
                    setTimeout(testStreamingResponse, 1000);
                } else {
                    console.log('❌ 配置不完整，请检查环境变量');
                    console.log('需要设置: GALAXY_AI_SYSTEM_ID, GALAXY_AI_SYSTEM_SECRET, GALAXY_AI_ACCOUNT, GALAXY_AI_APP_ID');
                }
            } catch (e) {
                console.error('配置检查失败:', e.message);
            }
        });
    });

    req.on('error', (err) => {
        console.error('配置检查请求失败:', err);
    });

    req.end();
}

// 开始测试
testConfig();
