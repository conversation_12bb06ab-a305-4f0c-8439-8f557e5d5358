# Galaxy AI OpenAI 代理服务

这个项目实现了一个 OpenAI 兼容的 API 代理，将标准的 OpenAI Chat Completions API 请求转发到内部的 Galaxy AI 服务。

## 功能特性

- **OpenAI 兼容接口**：完全兼容 OpenAI Chat Completions API
- **流式响应支持**：支持 Server-Sent Events (SSE) 流式响应
- **自动认证管理**：自动处理 Galaxy AI 的系统 token 获取和刷新
- **格式转换**：自动将 Galaxy AI 的 `$.delta` 格式转换为 OpenAI 标准格式
- **错误处理**：完善的错误处理和日志记录
- **配置验证**：启动时验证必要的配置项

## 配置说明

在 `application.yml` 中配置 Galaxy AI 服务：

```yaml
galaxy:
  ai:
    enabled: true
    base-url: ${GALAXY_AI_BASE_URL:http://copilot.prodgpu.chinastock.com.cn}
    system-id: ${GALAXY_AI_SYSTEM_ID:your-system-id}
    system-secret: ${GALAXY_AI_SYSTEM_SECRET:your-system-secret}
    account: ${GALAXY_AI_ACCOUNT:your-account}
    app-id: ${GALAXY_AI_APP_ID:your-app-id}
    connect-timeout: 10000
    read-timeout: 60000
    write-timeout: 60000
    default-model: galaxy-ai
```

### 环境变量配置

推荐使用环境变量来配置敏感信息：

```bash
export GALAXY_AI_SYSTEM_ID="your-system-id"
export GALAXY_AI_SYSTEM_SECRET="your-system-secret"
export GALAXY_AI_ACCOUNT="your-account"
export GALAXY_AI_APP_ID="your-app-id"
```

## API 使用

### 端点

- **POST** `/v1/chat/completions` - OpenAI 兼容的聊天完成接口

### 请求示例

#### 流式请求（推荐）

```bash
curl -X POST http://localhost:8080/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "galaxy-ai",
    "messages": [
      {
        "role": "user",
        "content": "你好，请介绍一下你自己"
      }
    ],
    "stream": true
  }'
```

#### JavaScript 示例

```javascript
const response = await fetch('http://localhost:8080/v1/chat/completions', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    model: 'galaxy-ai',
    messages: [
      {
        role: 'user',
        content: '你好，请介绍一下你自己'
      }
    ],
    stream: true
  })
});

const reader = response.body.getReader();
const decoder = new TextDecoder();

while (true) {
  const { done, value } = await reader.read();
  if (done) break;
  
  const chunk = decoder.decode(value);
  const lines = chunk.split('\n');
  
  for (const line of lines) {
    if (line.startsWith('data: ')) {
      const data = line.slice(6);
      if (data === '[DONE]') {
        console.log('Stream completed');
        break;
      }
      
      try {
        const parsed = JSON.parse(data);
        const content = parsed.choices[0]?.delta?.content;
        if (content) {
          console.log(content);
        }
      } catch (e) {
        // 忽略解析错误
      }
    }
  }
}
```

### 响应格式

响应格式完全兼容 OpenAI API：

```json
{
  "id": "chat-**********-abcd1234",
  "object": "chat.completion.chunk",
  "created": **********,
  "model": "galaxy-ai",
  "choices": [
    {
      "index": 0,
      "delta": {
        "content": "你好！"
      },
      "finish_reason": null
    }
  ]
}
```

## 测试和验证

### 配置检查

```bash
# 检查配置状态
curl http://localhost:8080/api/test/galaxy-ai-config

# 健康检查
curl http://localhost:8080/api/test/health
```

### 格式转换测试

使用提供的测试脚本验证格式转换：

```bash
# 基础功能测试
./test-openai-proxy.sh

# 详细格式转换测试
node test-format-conversion.js
```

测试脚本会验证：
- 配置完整性
- OpenAI 格式兼容性
- 流式响应处理
- 错误处理机制

### 日志监控

应用启动时会输出配置验证信息：

```
Galaxy AI service initialized with base URL: http://copilot.prodgpu.chinastock.com.cn
```

## 错误处理

### 常见错误

1. **配置错误**：
   ```json
   {
     "error": {
       "message": "galaxy.ai.system-id is required when Galaxy AI is enabled",
       "type": "invalid_request",
       "code": "invalid_request"
     }
   }
   ```

2. **认证失败**：
   ```json
   {
     "error": {
       "message": "Failed to get system token: 401 Unauthorized",
       "type": "authentication_error",
       "code": "authentication_error"
     }
   }
   ```

3. **不支持非流式请求**：
   ```json
   {
     "error": {
       "message": "Non-streaming requests are not currently supported. Please set 'stream': true",
       "type": "not_supported",
       "code": "non_stream_not_supported"
     }
   }
   ```

## 技术实现

### 核心组件

1. **ChatController**：OpenAI 兼容的 REST 控制器
2. **GalaxyAiService**：Galaxy AI 服务客户端
3. **GalaxyAiProperties**：配置属性管理
4. **OpenAI 模型类**：请求和响应的数据模型

### 关键特性

- **自动 Token 管理**：自动获取和刷新 Galaxy AI 的访问令牌
- **流式处理**：使用 OkHttp SSE 处理流式响应
- **格式转换**：将 Galaxy AI 的响应格式转换为 OpenAI 标准格式
- **错误恢复**：网络错误和认证错误的自动重试机制

### 格式转换详解

#### Galaxy AI 请求格式
```json
{
  "messages": [
    {
      "role": "user",
      "content": "你好呀"
    }
  ],
  "conversation_id": "chat-fzBsPshg2bizKg2dz7kFkE",
  "stream": true,
  "app_id": "app-LQMX9oFMy4TJ36eGUj2sBK",
  "tag": ["openai-proxy"]
}
```

#### Galaxy AI 响应格式
```json
{
  "id": "msg-knnvAJdtt3YGPvRTy8CoQf",
  "object": "answer",
  "created": "2024-01-25T12:48:00.338834",
  "conversation_id": "chat-5uxteR3oDGLvF5MWweoojn",
  "content_type": "text/markdown",
  "finish_reason": null,
  "delta": " 是"
}
```

#### OpenAI 响应格式（转换后）
```json
{
  "id": "msg-knnvAJdtt3YGPvRTy8CoQf",
  "object": "chat.completion.chunk",
  "created": 1706174880,
  "model": "galaxy-ai",
  "choices": [
    {
      "index": 0,
      "delta": {
        "content": " 是"
      },
      "finish_reason": null
    }
  ]
}
```

#### 转换规则

1. **消息过滤**：只处理 `object: "answer"` 的消息，忽略 `object: "thought"` 的思考过程消息
2. **ID 映射**：使用 Galaxy AI 的消息 ID 作为 OpenAI 的 chunk ID
3. **时间戳转换**：将 Galaxy AI 的 ISO 时间格式转换为 Unix 时间戳
4. **内容映射**：将 Galaxy AI 的 `delta` 字段映射到 OpenAI 的 `choices[0].delta.content`
5. **结束标识**：保持 `finish_reason` 的映射关系

## 注意事项

1. **仅支持流式请求**：当前实现仅支持 `stream: true` 的请求
2. **Token 缓存**：系统 token 会缓存 7 天，自动刷新
3. **线程安全**：服务类是线程安全的，支持并发请求
4. **资源管理**：自动管理 HTTP 连接和 SSE 连接的生命周期

## 部署建议

1. **环境变量**：使用环境变量配置敏感信息
2. **日志级别**：生产环境建议设置为 INFO 级别
3. **监控**：监控 `/api/test/health` 端点
4. **负载均衡**：支持多实例部署和负载均衡
