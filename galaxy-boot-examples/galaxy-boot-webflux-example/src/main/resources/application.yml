server:
  port: 8080

spring:
  application:
    name: galaxy-boot-webflux-example

galaxy:
  log:
    request-response:
      # 启用请求响应日志
      enabled: true
      # 启用 WebFlux 时序日志
      webflux-series: true
      # 启用请求头日志
      request-headers: true
      response-headers: true
      # 启用敏感字段掩码
      mask-field: true
    # 启用性能日志
    performance:
      enabled: true
    exception-pretty-print: true

logging:
  level:
    com.ctrip.framework: OFF