package cn.com.chinastock.cnf.kafka.examples.consumer;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * 单个消息消费者
 * 演示单条消息的消费处理
 */
@Component
public class SingleMessageConsumer {

    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(SingleMessageConsumer.class);

    /**
     * 消费单个消息 - 基础模式
     * 监听 single-message-topic 主题
     * @param message 消息内容
     */
    @KafkaListener(topics = "single-message-topic", groupId = "single-message-group")
    public void consumeSingleMessage(String message) {
        logger.info(LogCategory.APP_LOG, "Single message consumer received: {}", message);

        // 模拟业务处理
        processMessage(message, "single-message");
    }

    /**
     * 消费单个消息 - 带元数据模式
     * 监听 single-message-with-metadata-topic 主题
     * @param record 消费记录
     */
    @KafkaListener(topics = "single-message-with-metadata-topic", groupId = "single-metadata-group")
    public void consumeSingleMessageWithMetadata(ConsumerRecord<String, String> record) {
        String idempotencyId = null;
        if (record.headers() != null) {
            org.apache.kafka.common.header.Header idempotencyHeader = record.headers().lastHeader("idempotent_id");
            if (idempotencyHeader != null) {
                idempotencyId = new String(idempotencyHeader.value(), StandardCharsets.UTF_8);
            }
        }

        logger.info(LogCategory.APP_LOG, 
                   "Single message with metadata - Topic: {}, Partition: {}, Offset: {}, Idempotency ID: {}, Key: {}, Value: {}, Timestamp: {}",
                   record.topic(), record.partition(), record.offset(), idempotencyId,
                   record.key(), record.value(), record.timestamp());

        // 模拟业务处理
        processMessage(record.value(), "single-meta");
    }

    /**
     * 消费单个消息 - 带Header信息
     * 监听 single-message-with-headers-topic 主题
     * @param message 消息内容
     * @param topic 主题名称
     * @param partition 分区号
     * @param offset 偏移量
     * @param key 消息键
     * @param timestamp 时间戳
     */
    @KafkaListener(topics = "single-message-with-headers-topic", groupId = "single-headers-group")
    public void consumeSingleMessageWithHeaders(
            @Payload String message,
            @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
            @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
            @Header(KafkaHeaders.OFFSET) long offset,
            @Header(KafkaHeaders.RECEIVED_KEY) String key,
            @Header(KafkaHeaders.RECEIVED_TIMESTAMP) long timestamp) {
        
        logger.info(LogCategory.APP_LOG, 
                   "Single message with headers - Topic: {}, Partition: {}, Offset: {}, Key: {}, Message: {}, Timestamp: {}",
                   topic, partition, offset, key, message, timestamp);
        
        // 模拟业务处理
        processMessage(message, "single-headers");
    }

    /**
     * 消费单个消息 - 手动确认模式
     * 监听 single-message-manual-ack-topic 主题
     * @param record 消费记录
     * @param ack 确认对象
     */
    @KafkaListener(topics = "single-message-manual-ack-topic",
                   groupId = "single-manual-ack-group",
                   containerFactory = "kafkaListenerContainerFactory")
    public void consumeSingleMessageWithManualAck(ConsumerRecord<String, String> record, Acknowledgment ack) {
        logger.info(LogCategory.APP_LOG, 
                   "Single message manual ack - Topic: {}, Partition: {}, Offset: {}, Key: {}, Value: {}",
                   record.topic(), record.partition(), record.offset(), record.key(), record.value());
        
        try {
            // 模拟业务处理
            processMessage(record.value(), "single-manual-ack");
            
            // 手动确认消息
            ack.acknowledge();
            logger.info(LogCategory.APP_LOG, "Message acknowledged manually for offset: {}", record.offset());
            
        } catch (Exception e) {
            logger.error(LogCategory.EXCEPTION_LOG, "Error processing message, will not acknowledge", e);
            // 不调用 ack.acknowledge()，消息将不会被确认
        }
    }

    /**
     * 消费单个消息 - 错误处理演示
     * 监听 single-message-error-topic 主题
     * @param record 消费记录
     */
    @KafkaListener(topics = "single-message-error-topic", groupId = "single-error-group")
    public void consumeSingleMessageWithErrorHandling(ConsumerRecord<String, String> record) {
        logger.info(LogCategory.APP_LOG, 
                   "Single message error handling - Topic: {}, Partition: {}, Offset: {}, Value: {}",
                   record.topic(), record.partition(), record.offset(), record.value());
        
        try {
            // 模拟可能出错的业务处理
            if (record.value().contains("error")) {
                throw new RuntimeException("Simulated processing error for message: " + record.value());
            }
            
            processMessage(record.value(), "single-error-handling");
            
        } catch (Exception e) {
            logger.error(LogCategory.EXCEPTION_LOG, 
                        "Error processing message from topic: {}, partition: {}, offset: {}", 
                        record.topic(), record.partition(), record.offset(), e);
            
            // 在实际应用中，可以将失败的消息发送到死信队列或进行其他错误处理
            handleProcessingError(record, e);
        }
    }

    /**
     * 通用消息处理方法
     * @param message 消息内容
     * @param consumerType 消费者类型
     */
    private void processMessage(String message, String consumerType) {
        try {
            // 模拟业务处理逻辑
            Thread.sleep(10);
            logger.info(LogCategory.APP_LOG, "Message processed successfully by {}: {}", consumerType, message);
        } catch (InterruptedException e) {
            logger.error(LogCategory.EXCEPTION_LOG, "Error in message processing", e);
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 错误处理方法
     * @param record 消费记录
     * @param e 异常信息
     */
    private void handleProcessingError(ConsumerRecord<String, String> record, Exception e) {
        // 记录错误信息
        logger.error(LogCategory.EXCEPTION_LOG, 
                    "Failed to process message - Topic: {}, Partition: {}, Offset: {}, Key: {}, Value: {}, Error: {}",
                    record.topic(), record.partition(), record.offset(), 
                    record.key(), record.value(), e.getMessage());
        
        // 在实际应用中，可以：
        // 1. 发送到死信队列
        // 2. 记录到数据库
        // 3. 发送告警通知
        // 4. 重试机制等
    }
}
