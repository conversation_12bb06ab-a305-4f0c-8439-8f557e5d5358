server:
  port: 8089

spring:
  application:
    name: galaxy-boot-kafka-example-service

# 启用Spring Boot Actuator和Tracing
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  tracing:
    sampling:
      probability: 1.0  # 100%采样率，用于演示
  observations:
    key-values:
      application: galaxy-boot-kafka-example

galaxy:
  system:
    code: KFE

  log:
    request-response:
      enabled: true
      request-headers: true
    performance:
      enabled: true
    default-category: APP_LOG
    exception-pretty-print: true

  kafka:
    enable: true
    server:
      nodes: localhost:9092
    log:
      enabled: true
      max-detail-records-count: 10
    jaas:
      enable: false
    producer:
      acks: all
      retries: 3
      batch.size: 16384
      linger.ms: 5
      buffer.memory: 33554432
      request.timeout: 2000
      compression.type: none
    consumer:
      group.id: kafka-example-group
      auto.offset.reset: earliest
      concurrency: 3
      batch.listener: false
      is.ack: false
      enable.auto.commit: true
      auto.commit.interval.ms: 1000
      session.timeout.ms: 30000
      max.poll.records: 500
      max.poll.interval: 15000
      max.partition-fetch-bytes: 1048576

logging:
  level:
    cn.com.chinastock.cnf.kafka.examples: DEBUG
    org.springframework.kafka: ERROR
    org.apache.kafka: ERROR

app:
  id: galaxy-boot
apollo:
  config-service: http://localhost:8080