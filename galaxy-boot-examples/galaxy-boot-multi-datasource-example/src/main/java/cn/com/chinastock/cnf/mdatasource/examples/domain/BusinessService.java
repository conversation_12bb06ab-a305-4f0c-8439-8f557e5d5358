package cn.com.chinastock.cnf.mdatasource.examples.domain;

import cn.com.chinastock.cnf.mdatasource.examples.domain.user.entity.User;
import cn.com.chinastock.cnf.mdatasource.examples.domain.user.repository.UserRepository;
import cn.com.chinastock.cnf.mdatasource.examples.domain.product.entity.Product;
import cn.com.chinastock.cnf.mdatasource.examples.domain.product.repository.ProductRepository;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
public class BusinessService {

    private static final Logger logger = LoggerFactory.getLogger(BusinessService.class);

    private final ProductRepository productRepository;
    private final UserRepository userRepository;

    // 使用构造函数注入 Repositories
    public BusinessService(ProductRepository productRepository, UserRepository userRepository) {
        this.productRepository = productRepository;
        this.userRepository = userRepository;
    }

    // =================================================================
    // 正常数据写入
    // =================================================================
    @PostConstruct
    public void writeData() {
        writeProductData();
        writeUserData();
    }

    /**
     * 写入Product数据
     * 
     * @throws RuntimeException 如果数据写入失败
     */
    public void writeProductData() {
        try {
            logger.info("----> Writing to Product DataSource...");
            Product product = new Product("Laptop", new BigDecimal("999.99"), "High-performance laptop for developers");
            productRepository.save(product);
            logger.info("----> Product write successful.");
        } catch (Exception e) {
            logger.error("----> Product write failed: {}", e.getMessage());
            throw new RuntimeException("Failed to write product data", e);
        }
    }

    /**
     * 写入User数据
     * 
     * @throws RuntimeException 如果数据写入失败
     */
    public void writeUserData() {
        try {
            logger.info("----> Writing to User DataSource...");
            User user = new User("john_doe", "<EMAIL>", 28);
            userRepository.save(user);
            logger.info("----> User write successful.");
        } catch (Exception e) {
            logger.error("----> User write failed: {}", e.getMessage());
            throw new RuntimeException("Failed to write user data", e);
        }
    }

    // =================================================================
    // 数据验证
    // =================================================================

    /**
     * 验证数据并返回验证结果
     * 
     * @return Map<String, Object> 包含验证结果的映射
     */
    public Map<String, Object> verifyData() {
        logger.info("================== Verifying Initial Data ==================");
        
        List<Product> products = productRepository.findAll();
        List<User> users = userRepository.findAll();
        
        products.forEach(p ->
                logger.info("[Product] Found Product: ID={}, Name={}, Price={}", p.getId(), p.getName(), p.getPrice())
        );
        users.forEach(u ->
                logger.info("[User] Found User: ID={}, Username={}, Email={}, Age={}", u.getId(), u.getUsername(), u.getEmail(), u.getAge())
        );
        
        Map<String, Object> result = new HashMap<>();
        result.put("productCount", products.size());
        result.put("userCount", users.size());
        result.put("products", products);
        result.put("users", users);
        
        logger.info("==========================================================");
        return result;
    }


    /**
     * 尝试写入一条 Product 数据然后抛出异常，以触发事务回滚。
     */
    @Transactional("productTransactionManager")
    public void testProductTransaction() {
        logger.info("----> Attempting to save a 'Product' that should be rolled back.");
        Product productToRollback = new Product("Rollback Product", new BigDecimal("1.00"), "This should not exist");
        productRepository.save(productToRollback);
        logger.info("----> Product saved inside transaction. Now throwing exception...");
        throw new RuntimeException("Intentional exception to trigger Product rollback");
    }

    /**
     * 验证 Product 的回滚是否成功。
     * 
     * @return boolean true表示回滚成功，false表示回滚失败
     */
    public boolean verifyProductRollback() {
        Optional<Product> product = productRepository.findByName("Rollback Product");
        if (product.isPresent()) {
            logger.error("!!!!!! [FAILURE] Product transaction did NOT roll back. Found unexpected data.");
            return false;
        } else {
            logger.info("====== [SUCCESS] Product transaction rolled back successfully. No unexpected data found.");
            return true;
        }
    }

    /**
     * 尝试写入一条 User 数据然后抛出异常，以触发事务回滚。
     */
    @Transactional("userTransactionManager")
    public void testUserTransaction() {
        logger.info("----> Attempting to save a 'User' that should be rolled back.");
        User userToRollback = new User("rollback_user", "<EMAIL>", 25);
        userRepository.save(userToRollback);
        logger.info("----> User saved inside transaction. Now throwing exception...");
        throw new RuntimeException("Intentional exception to trigger User rollback");
    }

    /**
     * 验证 User 的回滚是否成功。
     * 
     * @return boolean true表示回滚成功，false表示回滚失败
     */
    public boolean verifyUserRollback() {
        Optional<User> user = userRepository.findByUsername("rollback_user");
        if (user.isPresent()) {
            logger.error("!!!!!! [FAILURE] User transaction did NOT roll back. Found unexpected data.");
            return false;
        } else {
            logger.info("====== [SUCCESS] User transaction rolled back successfully. No unexpected data found.");
            return true;
        }
    }
}