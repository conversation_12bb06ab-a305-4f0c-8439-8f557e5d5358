package cn.com.chinastock.cnf.mdatasource.examples.controller;

import cn.com.chinastock.cnf.mdatasource.examples.domain.BusinessService;
import cn.com.chinastock.cnf.mdatasource.examples.domain.product.repository.ProductRepository;
import jakarta.persistence.EntityManagerFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.context.ApplicationContext;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 * 提供多数据源测试相关的REST API接口，包括数据验证、事务测试、容器状态调试等功能
 * 
 * <AUTHOR> Boot Team
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/test")
public class TestController {

    private static final Logger logger = LoggerFactory.getLogger(TestController.class);

    private final BusinessService businessService;
    private final ApplicationContext applicationContext;

    /**
     * 构造函数
     * 
     * @param businessService 业务服务对象
     * @param applicationContext Spring应用上下文对象
     */
    public TestController(BusinessService businessService, ApplicationContext applicationContext) {
        this.businessService = businessService;
        this.applicationContext = applicationContext;
    }

    /**
     * 验证初始数据
     * 检查各个数据源中的数据是否正确初始化
     * 
     * @return ResponseEntity<Map<String, Object>> 包含验证结果的响应实体
     */
    @GetMapping("/verify-data")
    public ResponseEntity<Map<String, Object>> verifyData() {
        logger.info("=== API调用：验证数据 ===");
        
        try {
            Map<String, Object> verificationResult = businessService.verifyData();
            
            Map<String, Object> response = new HashMap<>();
            response.put("status", "success");
            response.put("message", "数据验证完成");
            response.put("data", verificationResult);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("数据验证失败", e);
            Map<String, Object> response = new HashMap<>();
            response.put("status", "error");
            response.put("message", "数据验证失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 初始化测试数据
     * 向各个数据源中插入测试数据
     * 
     * @return ResponseEntity<Map<String, Object>> 包含初始化结果的响应实体
     */
    @PostMapping("/init-data")
    public ResponseEntity<Map<String, Object>> initData() {
        logger.info("=== API调用：初始化数据 ===");
        
        Map<String, Object> response = new HashMap<>();
        Map<String, String> results = new HashMap<>();
        
        try {
            businessService.writeProductData();
            results.put("product", "success");
        } catch (Exception e) {
            logger.error("Product数据初始化失败", e);
            results.put("product", "failed: " + e.getMessage());
        }
        
        try {
            businessService.writeUserData();
            results.put("user", "success");
        } catch (Exception e) {
            logger.error("User数据初始化失败", e);
            results.put("user", "failed: " + e.getMessage());
        }
        
        boolean allSuccess = results.values().stream().allMatch(result -> result.equals("success"));
        
        response.put("status", allSuccess ? "success" : "partial_failure");
        response.put("message", allSuccess ? "测试数据初始化完成" : "测试数据初始化部分失败");
        response.put("results", results);
        return ResponseEntity.ok(response);
    }

    /**
     * 执行完整的事务回滚测试
     * 同时测试Product和User数据源的事务回滚功能
     * 
     * @return ResponseEntity<Map<String, Object>> 包含所有测试结果的响应实体
     */
    @PostMapping("/test-transactions")
    public ResponseEntity<Map<String, Object>> testAllTransactions() {
        logger.info("=== API调用：执行完整事务回滚测试 ===");
        
        Map<String, Object> response = new HashMap<>();
        Map<String, Map<String, Object>> results = new HashMap<>();
        
        // 测试Product事务
        Map<String, Object> productResult = new HashMap<>();
        try {
            businessService.testProductTransaction();
            productResult.put("status", "failure");
            productResult.put("message", "事务未回滚");
            productResult.put("rollbackVerified", false);
        } catch (RuntimeException e) {
            boolean rollbackSuccess = businessService.verifyProductRollback();
            productResult.put("status", rollbackSuccess ? "success" : "failure");
            productResult.put("message", rollbackSuccess ? "事务回滚成功" : "事务回滚失败");
            productResult.put("rollbackVerified", rollbackSuccess);
            productResult.put("exception", e.getMessage());
        } catch (Exception e) {
            logger.error("测试Product事务时发生意外异常", e);
            productResult.put("status", "error");
            productResult.put("message", "测试过程中发生意外异常: " + e.getMessage());
            productResult.put("rollbackVerified", false);
        }
        results.put("product", productResult);
        
        // 测试User事务
        Map<String, Object> userResult = new HashMap<>();
        try {
            businessService.testUserTransaction();
            userResult.put("status", "failure");
            userResult.put("message", "事务未回滚");
            userResult.put("rollbackVerified", false);
        } catch (RuntimeException e) {
            boolean rollbackSuccess = businessService.verifyUserRollback();
            userResult.put("status", rollbackSuccess ? "success" : "failure");
            userResult.put("message", rollbackSuccess ? "事务回滚成功" : "事务回滚失败");
            userResult.put("rollbackVerified", rollbackSuccess);
            userResult.put("exception", e.getMessage());
        } catch (Exception e) {
            logger.error("测试User事务时发生意外异常", e);
            userResult.put("status", "error");
            userResult.put("message", "测试过程中发生意外异常: " + e.getMessage());
            userResult.put("rollbackVerified", false);
        }
        results.put("user", userResult);
        
        // 计算整体状态
        boolean allSuccess = results.values().stream()
                .allMatch(result -> "success".equals(result.get("status")));
        
        response.put("status", allSuccess ? "success" : "partial_failure");
        response.put("results", results);
        response.put("message", "所有事务回滚测试完成");
        return ResponseEntity.ok(response);
    }

    /**
     * 容器状态调试
     * 检查Spring容器中多数据源相关组件的状态和配置
     * 
     * @return ResponseEntity<Map<String, Object>> 包含容器状态检查结果的响应实体
     */
    @GetMapping("/check-bean-container")
    public ResponseEntity<Map<String, Object>> debugContainer() {
        logger.info("=== API调用：容器状态调试 ===");
        
        String datasourceKey = "user";
        Map<String, Object> response = new HashMap<>();
        Map<String, String> checks = new HashMap<>();
        
        // 1. 检查 Repository Bean 是否存在
        try {
            ProductRepository repository = applicationContext.getBean(ProductRepository.class);
            checks.put("repository", "ProductRepository Bean 已成功在容器中找到");
            logger.info("[通过] 检查 1/4: ProductRepository Bean 已成功在容器中找到");
            
            // 2. 检查对应的 EntityManagerFactory Bean 是否存在
            String emfBeanName = datasourceKey + "EntityManagerFactory";
            try {
                EntityManagerFactory emf = applicationContext.getBean(emfBeanName, EntityManagerFactory.class);
                checks.put("entityManagerFactory", "" + emfBeanName + " Bean 已成功在容器中找到");
                logger.info("[通过] 检查 2/4: {} Bean 已成功在容器中找到", emfBeanName);
            } catch (NoSuchBeanDefinitionException e) {
                checks.put("entityManagerFactory", "容器中未找到名为 '" + emfBeanName + "' 的 EntityManagerFactory Bean");
                logger.error("[失败] 检查 2/4: 容器中未找到名为 '{}' 的 EntityManagerFactory Bean", emfBeanName);
            }
            
            // 3. 检查对应的 TransactionManager Bean 是否存在
            String tmBeanName = datasourceKey + "TransactionManager";
            try {
                PlatformTransactionManager tm = applicationContext.getBean(tmBeanName, PlatformTransactionManager.class);
                checks.put("transactionManager", "" + tmBeanName + " Bean 已成功在容器中找到");
                logger.info("[通过] 检查 3/4: {} Bean 已成功在容器中找到", tmBeanName);
            } catch (NoSuchBeanDefinitionException e) {
                checks.put("transactionManager", "容器中未找到名为 '" + tmBeanName + "' 的 TransactionManager Bean");
                logger.error("[失败] 检查 3/4: 容器中未找到名为 '{}' 的 TransactionManager Bean", tmBeanName);
            }
            
            // 4. 最终验证 - 尝试执行一个真实的数据库操作
            try {
                long count = repository.count();
                checks.put("databaseOperation", "成功执行 repository.count() 操作，返回计数: " + count);
                logger.info("[通过] 检查 4/4: 成功执行 repository.count() 操作，返回计数: {}", count);
                response.put("conclusion", "ProductRepository 与其对应的 EntityManagerFactory 和 TransactionManager 已被正确关联");
            } catch (Exception e) {
                checks.put("databaseOperation", "执行 repository.count() 操作时发生异常: " + e.getMessage());
                logger.error("[失败] 检查 4/4: 执行 repository.count() 操作时发生异常", e);
                response.put("conclusion", "关联存在问题");
            }
            
        } catch (NoSuchBeanDefinitionException e) {
            checks.put("repository", "容器中未找到 ProductRepository Bean");
            logger.error("[失败] 检查 1/4: 容器中未找到 ProductRepository Bean");
            response.put("conclusion", "基础配置存在问题");
        }
        
        response.put("status", "completed");
        response.put("checks", checks);
        response.put("datasourceKey", datasourceKey);
        return ResponseEntity.ok(response);
    }
} 