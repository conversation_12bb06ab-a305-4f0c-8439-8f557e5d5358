package cn.com.chinastock.cnf.mdatasource.examples.domain.product.repository;

import cn.com.chinastock.cnf.mdatasource.examples.domain.product.entity.Product;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ProductRepository extends JpaRepository<Product, Long> {
    Optional<Product> findByName(String name);
} 