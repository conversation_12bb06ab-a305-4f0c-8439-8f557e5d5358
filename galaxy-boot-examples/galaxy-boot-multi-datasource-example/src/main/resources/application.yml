server:
  port: 8080

spring:
  jpa:
    open-in-view: false
  multi-datasource:
    user:
      primary: true
      # 我们自定义模块所需的注册信息
      packages:
        entity: cn.com.chinastock.cnf.mdatasource.examples.domain.user.entity
        repository: cn.com.chinastock.cnf.mdatasource.examples.domain.user.repository
      # 标准的 Spring Boot DataSource 配置
      datasource:
        url: jdbc:h2:mem:userdb;DB_CLOSE_DELAY=-1
        username: sa
        password:
      # 标准的 Spring Boot JPA 配置 (将覆盖全局的spring.jpa)
      jpa:
        show-sql: true
        properties:
          hibernate.hbm2ddl.auto: create-drop

    product:
      packages:
        entity: cn.com.chinastock.cnf.mdatasource.examples.domain.product.entity
        repository: cn.com.chinastock.cnf.mdatasource.examples.domain.product.repository
      datasource:
        url: jdbc:h2:mem:productdb;DB_CLOSE_DELAY=-1
        username: sa
        password:
      jpa:
        show-sql: true
        properties:
          hibernate.hbm2ddl.auto:  create-drop

logging:
  level:
    com.ctrip.framework: OFF