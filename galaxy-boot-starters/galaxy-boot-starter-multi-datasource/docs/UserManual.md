### 组件使用说明
`Galaxy Boot Starter Multi Datasource` 是一个多数据源管理组件(关系数据库)，基于 `Spring Data JPA` 实现，支持在单个应用中配置和管理多个数据源。每个数据源都有独立的实体包路径、Repository 包路径、JPA 配置和事务管理。

### 组件功能介绍

#### 多数据源配置
支持通过配置文件声明式地配置多个数据源，每个数据源都是完全独立的，包括：
- 独立的数据库连接配置
- 独立的 JPA 配置
- 独立的实体和 Repository 包路径
- 独立的事务管理器

#### 自动化 Bean 注册
组件会自动为每个数据源创建以下 Bean：
- `DataSource` - 数据源
- `EntityManagerFactory` - JPA 实体管理器工厂
- `JpaTransactionManager` - JPA 事务管理器
- `SharedEntityManager` - 共享实体管理器
- `JpaRepositoryFactoryBean` - Repository 工厂 Bean

#### 事务管理
每个数据源都有独立的事务管理器，支持：
- 独立的事务边界
- 事务回滚隔离
- 声明式事务管理

### 使用方式

#### 1. 启用多数据源
在启动类上添加 `@EnableMultiDatasourceJpa` 注解：

```java
@SpringBootApplication
@EnableMultiDatasourceJpa
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

#### 2. 配置多数据源
在 `application.yml` 中配置多个数据源：

```yaml
spring:
  jpa:
    open-in-view: false  # 建议关闭，避免懒加载问题
  multi-datasource:
    # 用户数据源配置
    user:
      primary: true  # 标记为主数据源
      # 包路径配置
      packages:
        entity: com.example.user.entity      # 实体类包路径
        repository: com.example.user.repository  # Repository 包路径
      # 标准的 Spring Boot DataSource 配置
      datasource:
        url: ***********************************
        username: root
        password: password
        driver-class-name: com.mysql.cj.jdbc.Driver
      # 标准的 Spring Boot JPA 配置
      jpa:
        show-sql: true
        properties:
          hibernate.hbm2ddl.auto: none
          hibernate.dialect: org.hibernate.dialect.MySQL8Dialect
    
    # 产品数据源配置
    product:
      packages:
        entity: com.example.product.entity
        repository: com.example.product.repository
      datasource:
        url: **************************************
        username: root
        password: password
        driver-class-name: com.mysql.cj.jdbc.Driver
      jpa:
        show-sql: true
        properties:
          hibernate.hbm2ddl.auto: none
          hibernate.dialect: org.hibernate.dialect.MySQL8Dialect
```

#### 3. 定义实体类
在指定的包路径下创建实体类：

```java
// 用户实体 - 位于 com.example.user.entity 包
@Entity
@Table(name = "t_user")
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    private String username;
    private String email;
    private Integer age;
    
    // 构造函数、getter 和 setter
}

// 产品实体 - 位于 com.example.product.entity 包
@Entity
@Table(name = "t_product")
public class Product {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    private String name;
    private BigDecimal price;
    private String description;
    
    // 构造函数、getter 和 setter
}
```

#### 4. 定义 Repository 接口
在指定的包路径下创建 Repository 接口：

```java
// 用户 Repository - 位于 com.example.user.repository 包
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    Optional<User> findByUsername(String username);
}

// 产品 Repository - 位于 com.example.product.repository 包
@Repository
public interface ProductRepository extends JpaRepository<Product, Long> {
    Optional<Product> findByName(String name);
}
```

#### 5. 使用事务管理
通过指定事务管理器名称来使用特定数据源的事务：

```java
@Service
public class BusinessService {
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private ProductRepository productRepository;
    
    // 使用用户数据源的事务管理器
    @Transactional("userTransactionManager")
    public void saveUser(User user) {
        userRepository.save(user);
        // 如果发生异常，只回滚用户数据源的操作
    }
    
    // 使用产品数据源的事务管理器
    @Transactional("productTransactionManager")
    public void saveProduct(Product product) {
        productRepository.save(product);
        // 如果发生异常，只回滚产品数据源的操作
    }
    
    // 不使用事务注解时，会使用主数据源的事务管理器
    public void saveUserWithDefaultTransaction(User user) {
        userRepository.save(user);
    }
}
```

### 配置说明

#### 数据源配置结构
每个数据源的配置包含以下部分：

```yaml
spring:
  multi-datasource:
    {datasource-name}:  # 数据源名称，用于生成 Bean 名称
      primary: true|false  # 是否为主数据源，最多只能有一个
      packages:
        entity: [实体类包路径列表]
        repository: [Repository包路径列表]
      datasource:
        # 标准的 Spring Boot DataSource 配置
        url: 数据库连接URL
        username: 用户名
        password: 密码
        driver-class-name: 驱动类名
      jpa:
        # 标准的 Spring Boot JPA 配置（可选）
        show-sql: true|false
        properties:
          hibernate.hbm2ddl.auto: create|update|validate|none
          hibernate.dialect: 数据库方言
```

#### Bean 命名规则
组件会根据数据源名称自动生成以下 Bean：

- `{datasource-name}DataSource` - 数据源 Bean
- `{datasource-name}EntityManagerFactory` - 实体管理器工厂 Bean
- `{datasource-name}TransactionManager` - 事务管理器 Bean
- `{datasource-name}SharedEntityManager` - 共享实体管理器 Bean

例如，数据源名称为 `user` 时，会生成：
- `userDataSource`
- `userEntityManagerFactory`
- `userTransactionManager`
- `userSharedEntityManager`

#### 包路径配置
每个数据源需要指定独立的包路径：

```yaml
packages:
  entity: 
    - com.example.user.entity
    - com.example.user.dto.entity  # 支持多个包路径
  repository:
    - com.example.user.repository
    - com.example.user.dao
```

### 最佳实践

#### 1. 包路径隔离
建议为不同的数据源使用完全独立的包路径，避免实体类和 Repository 的混乱：

```
src/main/java/
├── com/example/
│   ├── user/
│   │   ├── entity/
│   │   │   └── User.java
│   │   └── repository/
│   │       └── UserRepository.java
│   └── product/
│       ├── entity/
│       │   └── Product.java
│       └── repository/
│           └── ProductRepository.java
```

#### 2. 事务管理
- 明确指定事务管理器名称，避免使用默认事务管理器
- 对于跨数据源的操作，考虑使用分布式事务或者业务补偿机制
- 避免在同一个事务中操作多个数据源

#### 3. 主数据源配置
- 设置一个主数据源（`primary: true`）
- 主数据源的事务管理器会被标记为 `@Primary`
- 未指定事务管理器的操作会使用主数据源的事务管理器

#### 4. 错误处理
```java
@Service
public class BusinessService {
    
    @Transactional("userTransactionManager")
    public void processUserData() {
        try {
            // 用户数据操作
            userRepository.save(user);
        } catch (Exception e) {
            // 只会回滚用户数据源的操作
            logger.error("用户数据操作失败", e);
            throw e;
        }
    }
}
```

### 注意事项

1. **包路径不能重叠**：不同数据源的实体和 Repository 包路径不能有重叠，否则会导致 Bean 注册冲突。

2. **事务边界**：每个数据源的事务是独立的，跨数据源的操作不会在同一个事务中。

3. **主数据源限制**：只能配置一个主数据源，配置多个会导致启动失败。

4. **JPA 配置继承**：每个数据源的 JPA 配置是独立的，不会继承全局的 `spring.jpa` 配置。

5. **连接池配置**：建议为每个数据源单独配置连接池参数，避免连接池耗尽。

### 常见问题

#### Q: 如何处理跨数据源的关联查询？
A: 组件不支持跨数据源的 JPA 关联查询。建议在业务层进行数据聚合，或者使用分布式查询方案。

#### Q: 可以动态添加数据源吗？
A: 当前版本不支持运行时动态添加数据源。所有数据源都需要在启动时通过配置文件定义。

#### Q: 如何处理数据源连接失败？
A: 组件会在启动时验证所有数据源的连接。如果某个数据源连接失败，应用启动会失败。建议在配置文件中正确配置数据源连接参数。


### 使用示例

完整的使用示例请参考：`galaxy-boot-examples/galaxy-boot-multi-datasource-example`

该示例演示了：
- 双数据源配置
- 实体类和 Repository 定义
- 事务管理和回滚测试
- 数据验证和容器状态检查 