package cn.com.chinastock.cnf.mdatasource.registrar;

import cn.com.chinastock.cnf.mdatasource.factory.EntityManagerFactoryBuilderFactory;
import cn.com.chinastock.cnf.mdatasource.properties.DataSourceDefinition;
import org.springframework.beans.factory.annotation.AnnotatedBeanDefinition;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.boot.context.properties.bind.Bindable;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.ResourceLoaderAware;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.context.annotation.ImportBeanDefinitionRegistrar;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.type.AnnotationMetadata;
import org.springframework.core.type.filter.AssignableTypeFilter;
import org.springframework.data.jpa.repository.support.JpaRepositoryFactoryBean;
import org.springframework.data.repository.Repository;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.SharedEntityManagerCreator;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.sql.DataSource;
import java.util.Collections;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * DynamicJpaModuleRegistrar 类是多数据源JPA模块的动态注册器。
 * 该类实现了 ImportBeanDefinitionRegistrar 接口，负责解析配置文件中的多数据源配置，
 * 并为每个数据源动态创建相应的DataSource、EntityManagerFactory、TransactionManager等Bean。
 *
 * <p>该类的主要功能包括：</p>
 * <ul>
 *     <li>解析 spring.multi-datasource 配置</li>
 *     <li>验证主数据源配置的唯一性</li>
 *     <li>为每个数据源创建完整的JPA基础设施</li>
 *     <li>扫描并注册Repository接口</li>
 * </ul>
 *
 * <AUTHOR>
 */
public class DynamicJpaModuleRegistrar implements ImportBeanDefinitionRegistrar, EnvironmentAware, ResourceLoaderAware {

    private Environment environment;
    private ResourceLoader resourceLoader;

    /**
     * 设置Spring环境对象
     * 
     * @param environment Spring环境对象，用于读取配置属性
     */
    @Override
    public void setEnvironment(Environment environment) {
        this.environment = environment;
    }

    /**
     * 设置资源加载器
     * 
     * @param resourceLoader 资源加载器，用于加载类路径资源
     */
    @Override
    public void setResourceLoader(ResourceLoader resourceLoader) {
        this.resourceLoader = resourceLoader;
    }

    /**
     * 注册Bean定义的主要方法
     * 解析多数据源配置并为每个数据源创建相应的Bean定义
     * 
     * @param importingClassMetadata 导入类的注解元数据
     * @param registry Bean定义注册器
     */
    @Override
    public void registerBeanDefinitions(AnnotationMetadata importingClassMetadata, BeanDefinitionRegistry registry) {
        Bindable<Map<String, DataSourceDefinition>> mapBindable = Bindable.mapOf(String.class, DataSourceDefinition.class);
        Map<String, DataSourceDefinition> datasourcesMap = Binder.get(environment).bind("spring.multi-datasource", mapBindable)
                .orElseGet(Collections::emptyMap);

        verifyPrimaryDatasource(datasourcesMap);

        datasourcesMap.forEach((name, properties) -> {
            registerJpaDataSource(registry, name, properties);
        });
    }

    /**
     * 验证主数据源配置的唯一性
     * 确保最多只有一个数据源被标记为主数据源
     * 
     * @param datasourcesMap 数据源配置映射
     * @throws IllegalStateException 如果配置了多个主数据源
     */
    private static void verifyPrimaryDatasource(Map<String, DataSourceDefinition> datasourcesMap) {
        AtomicInteger primaryCount = new AtomicInteger(0);
        datasourcesMap.values().forEach(props -> {
            if (props.isPrimary()) {
                primaryCount.incrementAndGet();
            }
        });
        Assert.state(primaryCount.get() <= 1, "There must be exactly one or zero primary datasource.");
    }

    /**
     * 为单个数据源注册JPA相关的Bean定义
     * 包括DataSource、EntityManagerFactory、TransactionManager等
     * 
     * @param registry Bean定义注册器
     * @param datasourceName 数据源名称
     * @param properties 数据源配置属性
     */
    private void registerJpaDataSource(BeanDefinitionRegistry registry, String datasourceName, DataSourceDefinition properties) {

        String dataSourceBeanName = datasourceName + "DataSource";
        String entityManagerFactoryBeanName = datasourceName + "EntityManagerFactory";
        String transactionManagerBeanName = datasourceName + "TransactionManager";
        String sharedEntityManagerBeanName = datasourceName + "SharedEntityManager";

        {
            BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(DataSource.class,
                    () -> properties.getDatasource().initializeDataSourceBuilder().build());
            registerBean(dataSourceBeanName, registry, properties, builder);
        }

        {
            BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(EntityManagerFactoryBuilderFactory.class);

            builder.addConstructorArgValue(properties.getJpa());
            builder.addConstructorArgReference(dataSourceBeanName);
            String[] entityPackages = properties.getPackages().getEntity().toArray(new String[0]);
            builder.addConstructorArgValue(entityPackages);
            builder.addConstructorArgValue(datasourceName);

            registerBean(entityManagerFactoryBeanName, registry, properties, builder);
        }

        {
            BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(JpaTransactionManager.class);
            builder.addPropertyReference("entityManagerFactory", entityManagerFactoryBeanName);
            registerBean(transactionManagerBeanName, registry, properties, builder);
        }

        {
            BeanDefinitionBuilder builder = BeanDefinitionBuilder.rootBeanDefinition(SharedEntityManagerCreator.class, "createSharedEntityManager");

            builder.setRole(BeanDefinition.ROLE_INFRASTRUCTURE);
            builder.addConstructorArgReference(entityManagerFactoryBeanName);

            registry.registerBeanDefinition(sharedEntityManagerBeanName, builder.getBeanDefinition());
        }

        registerJpaRepositories(registry, properties, sharedEntityManagerBeanName, transactionManagerBeanName);
    }

    /**
     * 注册Bean定义的通用方法
     * 设置Bean的基础属性，如角色和主要标记
     * 
     * @param beanName Bean名称
     * @param registry Bean定义注册器
     * @param properties 数据源配置属性
     * @param builder Bean定义构建器
     */
    private static void registerBean(String beanName, BeanDefinitionRegistry registry, DataSourceDefinition properties, BeanDefinitionBuilder builder) {
        builder.setRole(BeanDefinition.ROLE_INFRASTRUCTURE);

        if (properties.isPrimary()) {
            builder.setPrimary(true);
        }

        registry.registerBeanDefinition(beanName, builder.getBeanDefinition());
    }

    /**
     * 注册JPA Repository接口
     * 扫描指定包路径下的Repository接口并创建相应的Bean定义
     * 
     * @param registry Bean定义注册器
     * @param properties 数据源配置属性
     * @param sharedEntityManagerBeanName 共享实体管理器Bean名称
     * @param transactionManagerBeanName 事务管理器Bean名称
     */
    private void registerJpaRepositories(BeanDefinitionRegistry registry, DataSourceDefinition properties, String sharedEntityManagerBeanName, String transactionManagerBeanName) {
        ClassPathScanningCandidateComponentProvider scanner = new RepositoryScanner(this.resourceLoader);

        properties.getPackages().getRepository().forEach(basePackage -> {
            scanner.findCandidateComponents(basePackage).forEach(beanDefinition -> {
                Class<?> repositoryClass = classForName(beanDefinition.getBeanClassName());
                String beanName = StringUtils.uncapitalize(repositoryClass.getSimpleName());

                BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(JpaRepositoryFactoryBean.class);
                builder.addConstructorArgValue(repositoryClass);
                builder.addPropertyReference("entityManager", sharedEntityManagerBeanName);
                builder.addPropertyValue("transactionManager", transactionManagerBeanName);

                registry.registerBeanDefinition(beanName, builder.getBeanDefinition());
            });
        });
    }

    /**
     * 根据类名加载Class对象
     * 
     * @param className 类的全限定名
     * @return Class对象
     * @throws IllegalStateException 如果找不到指定的类
     */
    private Class<?> classForName(String className) throws IllegalStateException {
        try {
            return Class.forName(className);
        } catch (ClassNotFoundException e) {
            throw new IllegalStateException("Could not find class: " + className, e);
        }
    }

    /**
     * Repository扫描器内部类
     * 用于扫描指定包路径下的Repository接口
     * 
     * <AUTHOR>
     */
    private class RepositoryScanner extends ClassPathScanningCandidateComponentProvider {
        
        /**
         * 构造函数
         * 
         * @param resourceLoader 资源加载器
         */
        public RepositoryScanner(ResourceLoader resourceLoader) {
            super(false);
            super.setResourceLoader(resourceLoader);
            super.addIncludeFilter(new AssignableTypeFilter(Repository.class));
        }

        /**
         * 判断是否为候选组件
         * 只有接口类型才被认为是候选组件
         * 
         * @param beanDefinition Bean定义
         * @return true表示是候选组件，false表示不是
         */
        @Override
        protected boolean isCandidateComponent(AnnotatedBeanDefinition beanDefinition) {
            return beanDefinition.getMetadata().isInterface();
        }
    }

}