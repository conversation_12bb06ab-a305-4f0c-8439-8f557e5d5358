package cn.com.chinastock.cnf.mdatasource;

import cn.com.chinastock.cnf.mdatasource.registrar.DynamicJpaModuleRegistrar;
import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

/**
 * EnableMultiDatasourceJpa 注解用于启用多数据源JPA功能。
 * 该注解会导入 DynamicJpaModuleRegistrar 类，自动扫描配置文件中的多数据源配置
 *
 * <AUTHOR>
 * @see DynamicJpaModuleRegistrar
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@Import(DynamicJpaModuleRegistrar.class)
public @interface EnableMultiDatasourceJpa {
}