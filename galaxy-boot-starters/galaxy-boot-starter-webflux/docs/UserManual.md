### 组件使用说明
`Galaxy Boot Starter WebFlux` 是一个基于 `Spring Boot WebFlux` 的封装，提供了统一的、规范化的响应式 Web 应用开发功能。
该组件专为高并发、低延迟的响应式应用场景设计，自动配置了响应式日志记录、异常处理、链路追踪等企业级特性。

### 组件功能介绍

#### 响应式日志记录
`Galaxy Boot Starter WebFlux` 提供了专为响应式环境设计的日志记录功能，支持异步非阻塞的日志处理。

- **响应式请求日志**：异步记录HTTP请求信息，包括方法、URI、请求头、请求体等
- **响应式响应日志**：异步记录HTTP响应信息，包括状态码、响应头、响应体等
- **流式日志处理**：支持流式数据的日志记录，适合大数据量场景
- **时序与非时序日志**：支持两种日志记录模式，满足不同业务需求
- **敏感字段掩码**：响应式环境下的敏感字段保护

#### Reactor上下文集成
`Galaxy Boot Starter WebFlux` 与 Reactor 上下文深度集成，确保链路追踪信息在响应式流中正确传播。

- **上下文自动传播**：自动启用 Reactor 上下文传播机制
- **MDC集成**：将链路追踪信息注册到 ContextRegistry，支持跨异步边界传播
- **响应式TraceId管理**：在响应式流中自动管理TraceId和SpanId
- **非阻塞上下文切换**：确保上下文切换不会阻塞响应式流

#### 响应式异常处理
`Galaxy Boot Starter WebFlux` 提供了专为响应式环境设计的全局异常处理机制。

- **Mono异常处理**：处理单值响应式流中的异常
- **Flux异常处理**：处理多值响应式流中的异常
- **响应式错误信号**：将异常转换为响应式错误信号
- **统一异常格式**：返回统一的响应式异常格式

#### 高性能日志过滤器
`Galaxy Boot Starter WebFlux` 采用高性能的WebFilter实现，专为响应式环境优化。

- **非阻塞过滤**：所有日志操作都是非阻塞的
- **流式数据缓存**：智能缓存请求和响应数据，skip大文件缓存
- **内存优化**：采用DataBuffer缓存机制，减少内存占用

### 组件配置说明

#### 响应式日志配置
`Galaxy Boot Starter WebFlux` 支持专为响应式环境设计的日志配置：

```yaml
galaxy:
  log:
    request-response:
      # WebFlux特有配置
      # 是否启用 WebFlux 时序日志开关（先记录请求，再记录响应）
      webflux-series: false
```
日志其他配置与core配置完全兼容，参见 `galaxy-boot-core` 日志介绍

#### Reactor上下文配置
WebFlux环境下的上下文传播配置：

```java
# 自动启用上下文传播（组件内部配置），注册的MDC键值对
# - traceId
# - spanId  
# - parentSpanId
# - request_uri
# - request_method
```

### 组件技术选型

#### 响应式框架对比

| **特性** | **Spring WebFlux** | **Vert.x** | **Akka HTTP** | **RxJava** |
|---------|------------------|-----------|--------------|-----------|
| **编程模型** | 响应式流 | 响应式流 | Actor模型 | 响应式扩展 |
| **性能** | 高 | 非常高 | 高 | 中等 |
| **学习成本** | 中等 | 中等 | 高 | 中等 |
| **生态集成** | 优秀 | 良好 | 良好 | 优秀 |
| **Spring集成** | 原生支持 | 需要适配 | 需要适配 | 需要适配 |

#### 响应式HTTP客户端对比

| **特性** | **WebClient** | **Reactor Netty** | **OkHttp** | **Apache HttpClient** |
|---------|--------------|------------------|-----------|----------------------|
| **响应式支持** | 原生支持 | 原生支持 | 需要适配 | 需要适配 |
| **性能** | 高 | 非常高 | 高 | 中等 |
| **内存占用** | 低 | 低 | 中等 | 高 |
| **背压处理** | 支持 | 支持 | 不支持 | 不支持 |

#### 日志框架在响应式环境中的表现

| **特性** | **Log4j2** | **Logback** | **JUL** |
|---------|-----------|------------|--------|
| **异步性能** | 优秀 | 良好 | 差 |
| **响应式兼容** | 优秀 | 良好 | 差 |
| **上下文传播** | 支持 | 支持 | 不支持 |
| **内存占用** | 低 | 中等 | 高 |

#### 最终选型
基于Spring Boot WebFlux生态和高性能响应式应用需求，本组件的技术选型为：

- **响应式框架**：Spring WebFlux - 与Spring生态完美集成
- **HTTP服务器**：Reactor Netty - 高性能异步IO
- **日志框架**：Log4j2 - 原生异步支持，响应式兼容
- **上下文传播**：Reactor Context + Micrometer Context Propagation
- **异常处理**：WebFlux 全局异常处理 - 响应式异常处理

### 最佳实践

#### 响应式编程最佳实践
1. **避免阻塞操作**：
   ```java
   // 错误：阻塞操作
   public Mono<String> badExample() {
       String result = blockingService.call(); // 阻塞！
       return Mono.just(result);
   }
   
   // 正确：非阻塞操作
   public Mono<String> goodExample() {
       return Mono.fromCallable(() -> blockingService.call()) // 封装阻塞任务
               .subscribeOn(Schedulers.boundedElastic());
   }
   ```

2. **合理使用调度器**：
   ```java
   // CPU密集型任务
   .subscribeOn(Schedulers.parallel())
   
   // IO密集型任务
   .subscribeOn(Schedulers.boundedElastic())
   ```

3. **错误处理**：
   ```java
   return userService.getUser(id)
           .switchIfEmpty(Mono.error(new BusinessException("USER_001", "用户不存在")))
           .onErrorMap(SQLException.class, ex -> new BusinessException("DB_001", "数据库错误"))
           .doOnError(error -> logger.error(LogCategory.EXCEPTION_LOG, "获取用户失败", error));
   ```

4. **优化日志配置**：
   ```yaml
   galaxy:
     log:
      # WebFlux特有配置
      # 禁用时序日志提高性能
      webflux-series: false
   ```
