package cn.com.chinastock.cnf.webflux.log.filter;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.core.log.config.LogProperties;
import cn.com.chinastock.cnf.core.log.context.ITraceContext;
import cn.com.chinastock.cnf.core.log.context.W3CTraceContext;
import cn.com.chinastock.cnf.webflux.log.logger.FluxRequestCache;
import cn.com.chinastock.cnf.webflux.log.logger.FluxResponseCache;
import cn.com.chinastock.cnf.webflux.log.logger.JoinRequestCache;
import cn.com.chinastock.cnf.webflux.log.logger.RequestLogger;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;
import reactor.util.context.Context;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * LogWebfluxFilter 类是WebFlux环境下的日志过滤器。
 * 该过滤器负责处理HTTP请求和响应的日志记录，支持链路追踪、性能监控和敏感字段掩码。
 *
 * <p>过滤器的主要功能包括：</p>
 * <ul>
 *     <li>提取并设置链路追踪上下文信息（traceId、spanId等）</li>
 *     <li>记录HTTP请求和响应日志</li>
 *     <li>记录接口性能日志</li>
 *     <li>支持时序日志和非时序日志两种模式</li>
 *     <li>支持敏感字段掩码处理</li>
 * </ul>
 *
 * <AUTHOR>
 */
@Order(Ordered.HIGHEST_PRECEDENCE)
public class LogWebfluxFilter implements org.springframework.web.server.WebFilter {

    private final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(getClass());

    private LogProperties logProperties;

    /**
     * 构造函数，初始化日志配置
     * 
     * @param logProperties 日志配置属性
     */
    public LogWebfluxFilter(LogProperties logProperties) {
        this.logProperties = logProperties;
    }

    /**
     * 过滤器的主要处理方法
     * 处理HTTP请求和响应的日志记录，包括链路追踪、性能监控等
     * 
     * @param exchange 服务器Web交换对象
     * @param chain Web过滤器链
     * @return 处理结果的Mono对象
     */
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        long requestStartTime = System.currentTimeMillis();

        Mono<Void> filterMono = logRequestResponse(exchange, chain);

        Mono<Void> performanceMono = logPerformance(filterMono, requestStartTime);

        Map<String, Object> context = buildTraceContextFrom(exchange.getRequest());

        if (logProperties.isControllerLogEnabled()) {
            context.put("SERVER_WEB_EXCHANGE", exchange);
        }

        return performanceMono.contextWrite(Context.of(context));
    }

    /**
     * 记录性能日志
     * 
     * @param filterMono 过滤器处理的Mono对象
     * @param requestStartTime 请求开始时间
     * @return 包含性能日志记录的Mono对象
     */
    private Mono<Void> logPerformance(Mono<Void> filterMono, long requestStartTime) {
        if (!logProperties.isPerformanceLogEnabled()) {
            return filterMono;
        }
        return filterMono.doFinally(signal ->
                logger.info(LogCategory.PERFORMANCE_LOG,"cost=" + (System.currentTimeMillis() - requestStartTime) + " unit=ms")
        );
    }

    /**
     * 记录请求和响应日志
     * 
     * @param exchange 服务器Web交换对象
     * @param chain Web过滤器链
     * @return 处理结果的Mono对象
     */
    private Mono<Void> logRequestResponse(ServerWebExchange exchange, WebFilterChain chain) {
        if (!logProperties.isRequestResponseEnabled()) {
            return chain.filter(exchange);
        }

        if (logProperties.isRequestResponseMaskFieldEnabled()) {
            if (exchange.getRequest().getHeaders().getContentLength() > 0) {
                return chain.filter(exchange);
            }
            return chain.filter(exchange).doFirst(() -> {
                RequestLogger.log(exchange.getRequest(), logProperties.isRequestHeadersEnabled());
            });
        }

        if (logProperties.isWebfluxRequestSeriesLogEnabled()) {
            return seriesLogging(exchange, chain);
        }
        return noneSeriesLogging(exchange, chain);
    }

    /**
     * 从HTTP请求构建链路追踪上下文
     * 
     * @param request HTTP请求对象
     * @return 包含链路追踪信息的上下文Map
     */
    private Map<String, Object> buildTraceContextFrom(ServerHttpRequest request) {
        ITraceContext traceContext = new W3CTraceContext();
        traceContext.extractTraceContext(request.getHeaders().toSingleValueMap());

        Map<String, Object> context = new HashMap<>();
        context.put("request_method", request.getMethod().name());
        context.put("request_uri", request.getPath().toString());
        context.put("traceId", traceContext.getTraceId());
        context.put("spanId", traceContext.getSpanId());
        Optional.ofNullable(traceContext.getParentSpanId())
                .ifPresent(id -> context.put("parentSpanId", id));

        logger.debug("reactor context: {}", context);
        return context;
    }

    /**
     * 非时序日志记录
     * 请求和响应日志在请求处理完成后一起记录
     * 
     * @param exchange 服务器Web交换对象
     * @param chain Web过滤器链
     * @return 处理结果的Mono对象
     */
    private Mono<Void> noneSeriesLogging(ServerWebExchange exchange, WebFilterChain chain) {
        FluxRequestCache requestLogger = new FluxRequestCache(exchange.getRequest());
        FluxResponseCache responseLogger = new FluxResponseCache(exchange.getResponse());

        return chain.filter(exchange.mutate().request(requestLogger.requestDecorator()).response(responseLogger.responseDecorator()).build())
                .doFinally(signalType -> {
                    requestLogger.log(logProperties.isRequestHeadersEnabled());
                    responseLogger.log(logProperties.isResponseHeadersEnabled());
                });
    }

    /**
     * 时序日志记录
     * 先记录请求日志，再处理请求，最后记录响应日志
     * 
     * @param exchange 服务器Web交换对象
     * @param chain Web过滤器链
     * @return 处理结果的Mono对象
     */
    private Mono<Void> seriesLogging(ServerWebExchange exchange, WebFilterChain chain) {
        JoinRequestCache requestLogger = new JoinRequestCache(exchange.getRequest(), exchange.getResponse().bufferFactory());
        FluxResponseCache responseLogger = new FluxResponseCache(exchange.getResponse());

        return requestLogger.log(logProperties.isRequestHeadersEnabled())
                .then(
                        chain.filter(exchange.mutate().request(requestLogger.requestDecorator()).response(responseLogger.responseDecorator()).build())
                )
                .doFinally(signalType -> {
                    responseLogger.log(logProperties.isResponseHeadersEnabled());
                });
    }

}