package cn.com.chinastock.cnf.webflux.log.config;

import cn.com.chinastock.cnf.core.log.config.LogProperties;
import cn.com.chinastock.cnf.core.log.config.PropertyConstants;
import cn.com.chinastock.cnf.webflux.log.GalaxyWebfluxLoggerFactory;
import cn.com.chinastock.cnf.webflux.log.aspect.ControllerLogAspect;
import cn.com.chinastock.cnf.webflux.log.filter.LogWebfluxFilter;
import io.micrometer.context.ContextRegistry;
import jakarta.annotation.PostConstruct;
import org.slf4j.MDC;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import reactor.core.publisher.Hooks;

/**
 * LoggingAutoConfiguration 类是WebFlux日志组件的自动配置类。
 * 该类负责配置WebFlux环境下的日志组件，包括过滤器、切面和MDC上下文传播。
 *
 * <p>该类的主要功能包括：</p>
 * <ul>
 *     <li>配置WebFlux日志过滤器</li>
 *     <li>配置控制器日志切面</li>
 *     <li>启用Reactor上下文自动传播</li>
 *     <li>注册MDC键值对的上下文访问器</li>
 * </ul>
 *
 * <AUTHOR>
 */
@Configuration
@EnableConfigurationProperties(LogProperties.class)
public class LoggingAutoConfiguration {

    /**
     * 构造函数，初始化日志配置
     * 
     * @param logProperties 日志配置属性
     */
    public LoggingAutoConfiguration(LogProperties logProperties) {
        GalaxyWebfluxLoggerFactory.setLogProperties(logProperties);
    }

    /**
     * 创建WebFlux日志过滤器Bean
     * 
     * @param logProperties 日志配置属性
     * @return LogWebfluxFilter实例
     */
    @Bean
    public LogWebfluxFilter logWebfluxFilter(LogProperties logProperties) {
        return new LogWebfluxFilter(logProperties);
    }

    /**
     * 创建控制器日志切面Bean
     * 仅在启用字段掩码功能时创建
     * 
     * @param logProperties 日志配置属性
     * @return ControllerLogAspect实例
     */
    @Bean
    @ConditionalOnProperty(name = PropertyConstants.GALAXY_LOG_REQUEST_RESPONSE_MASK_FIELD, havingValue = "true")
    public ControllerLogAspect controllerLogAspect(LogProperties logProperties) {
        return new ControllerLogAspect(logProperties);
    }

    /**
     * 初始化日志配置
     * 启用Reactor自动上下文传播，并注册MDC键值对的上下文访问器
     */
    @PostConstruct
    public void initLoggingConfiguration() {
        Hooks.enableAutomaticContextPropagation();

        ContextRegistry registry = ContextRegistry.getInstance();

        registerMdc(registry, "traceId");
        registerMdc(registry, "spanId");
        registerMdc(registry, "parentSpanId");
        registerMdc(registry, "request_uri");
        registerMdc(registry, "request_method");
    }

    /**
     * 注册MDC键值对的上下文访问器
     * 
     * @param registry 上下文注册器
     * @param key MDC键名
     */
    private void registerMdc(ContextRegistry registry, String key) {
        registry.registerThreadLocalAccessor(
                key,
                () -> MDC.get(key),
                value -> MDC.put(key, value),
                () -> MDC.remove(key)
        );
    }
}
