## Galaxy Boot OceanBase Starter

> Galaxy Boot OceanBase 数据库 Starter 是一个为 OceanBase 数据库提供的 Spring Boot Starter，用于简化应用的开发。

### 依赖配置

如果要在您的项目中使用 Oceanbase 来实现配置管理，只需要引入对应 starter 即可：使用 group ID 为 `cn.com.chinastock` 和 artifact ID 为
`galaxy-boot-starter-data-oceanbase` 的 starter。

```xml
<dependency>
    <groupId>cn.com.chinastock</groupId>
    <artifactId>galaxy-boot-starter-data-oceanbase</artifactId>
</dependency>
```
