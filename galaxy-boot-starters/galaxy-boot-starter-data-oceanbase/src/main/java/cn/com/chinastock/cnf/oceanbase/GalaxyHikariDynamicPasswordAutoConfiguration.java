package cn.com.chinastock.cnf.oceanbase;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import com.ctrip.framework.apollo.core.dto.ApolloConfig;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariConfigMXBean;
import com.zaxxer.hikari.HikariDataSource;
import com.zaxxer.hikari.HikariPoolMXBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jmx.export.MBeanExporter;

import javax.sql.DataSource;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * OceanBase 数据源动态密码刷新自动配置类
 * <p>
 * 实现机制：
 * 1. 监听 Apollo 配置变更，当密码配置发生变化时触发刷新
 * 2. 创建新的 HikariDataSource 实例，使用新密码和原有配置
 * 3. 通过 GalaxyDynamicDataSource 进行数据源切换，确保新连接使用新数据源
 * 4. 优雅关闭旧数据源，等待所有连接关闭后释放资源
 * <p>
 * 使用条件：
 * 1. 需要 Apollo 配置中心支持
 * 2. 需要 HikariCP 连接池
 * 3. 配置项 galaxy.data.dynamic-refresh-username-password=true
 */
@Configuration
@EnableConfigurationProperties(GalaxyDataSourceProperties.class)
@ConditionalOnClass({ApolloConfig.class, HikariDataSource.class})
@ConditionalOnProperty(prefix = GalaxyDataSourceProperties.CONFIG_PREFIX, name = "dynamic-refresh-username-password", havingValue = "true")
public class GalaxyHikariDynamicPasswordAutoConfiguration {
    // 安全扫描会把 PASSWORD 的变量名当作密码的 key，值为密码的值，导致误报
    public static final String SPRING_DATASOURCE_PWD_KEY = "spring.datasource.password";
    private HikariDataSource dataSource;
    private GalaxyDynamicDataSource dynamicDataSource;

    @Value("${spring.datasource.hikari.minimum-idle:5}")
    private int minIdle;
    @Value("${spring.datasource.hikari.maximum-pool-size:10}")
    private int maxPoolSize;
    @Value("${spring.datasource.hikari.connection-timeout:30000}")
    private int connectionTimeout;
    @Value("${spring.datasource.hikari.connection-test-query:SELECT 1}")
    private String connectionTestQuery;
    @Value("${spring.datasource.hikari.keepalive-time:120000}")
    private long keepaliveTime;
    @Value("${spring.datasource.hikari.max-lifetime:1800000}")
    private long maxLifetime;
    @Value("${spring.datasource.hikari.idle-timeout:600000}")
    private long idleTimeout;
    @Value("${spring.datasource.hikari.initialization-fail-timeout:1}")
    private long initializationFailTimeout;

    @Value("${spring.datasource.url}")
    private String url;
    @Value("${spring.datasource.username}")
    private String username;
    @Value("${spring.datasource.password}")
    private String password;
    @Value("${spring.datasource.driver-class-name}")
    private String driverClassName;

    @Value("${spring.datasource.hikari.auto-commit:true}")
    private boolean autoCommit;
    @Value("${spring.datasource.hikari.isolate-internal-queries:true}")
    private boolean isolateInternalQueries;

    public GalaxyHikariDynamicPasswordAutoConfiguration() {
        GalaxyLogger.info(LogCategory.FRAMEWORK_LOG, "GalaxyDynamicPasswordAutoConfiguration initialized");
    }

    @Bean("galaxyDynamicDataSource")
    @Primary
    public DataSource dataSource() {
        HikariDataSource defaultDataSource = galaxyDataSource();
        this.dataSource = defaultDataSource;
        this.dynamicDataSource = new GalaxyDynamicDataSource(defaultDataSource);
        return this.dynamicDataSource;
    }

    @Bean("galaxyDataSource")
    public HikariDataSource galaxyDataSource() {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl(url);
        config.setUsername(username);
        config.setPassword(password);
        if (!driverClassName.isEmpty()) {
            config.setDriverClassName(driverClassName);
        }

        config.setMaximumPoolSize(maxPoolSize);             // 对应 maxActive
        config.setMinimumIdle(minIdle);                     // 对应 minIdle
        config.setConnectionTimeout(connectionTimeout);     // 对应 maxWait
        config.setInitializationFailTimeout(initializationFailTimeout);          // 初始化失败超时
        config.setConnectionTestQuery(connectionTestQuery);   // 对应 validationQuery
        config.setKeepaliveTime(keepaliveTime);              // 空闲连接存活测试
        config.setMaxLifetime(maxLifetime);
        config.setIdleTimeout(idleTimeout);
        config.setAutoCommit(autoCommit);
        config.setIsolateInternalQueries(isolateInternalQueries);
        config.setRegisterMbeans(true);
        config.setPoolName(getPoolName());

        return new HikariDataSource(config);
    }

    private static String getPoolName() {
        return "HikariPool-" + System.currentTimeMillis();
    }

    /**
     * 如果启用了 GalaxyHikariMetricAutoConfiguration，会自动在 JMX 中注册数据源的指标。
     * 由于 {@link #galaxyDataSource()} 也会被注册到 JMX 中，这可能导致重复注册的问题。
     * 因此，在这里需要忽略 galaxyDataSource 的暴露，避免重复注册。
     *
     * @return MBeanExporter 实例
     */
    @Bean
    public MBeanExporter exporter() {
        final MBeanExporter exporter = new MBeanExporter();
        exporter.setAutodetect(false);
        exporter.setExcludedBeans("galaxyDataSource");
        return exporter;
    }

    @ApolloConfigChangeListener
    private void onChange(ConfigChangeEvent changeEvent) {
        if (changeEvent.isChanged(SPRING_DATASOURCE_PWD_KEY)) {
            String newPassword = changeEvent.getChange(SPRING_DATASOURCE_PWD_KEY).getNewValue();
            try {
                createNewDataSource(newPassword);
            } catch (Exception e) {
                GalaxyLogger.error("Failed to refresh password: " + e.getMessage(), e);
            }
        }
    }

    public void createNewDataSource(String newPassword) {
        try {
            HikariDataSource oldDataSource = this.dataSource;
            // 记录当前配置
            HikariConfig newConfig = new HikariConfig();
            newConfig.setJdbcUrl(dataSource.getJdbcUrl());
            newConfig.setUsername(dataSource.getUsername());
            newConfig.setPassword(newPassword);
            newConfig.setDriverClassName(dataSource.getDriverClassName());

            // 设置唯一的连接池名称
            String poolName = getPoolName();
            newConfig.setPoolName(poolName);

            // 复制其他必要的配置
            newConfig.setMaximumPoolSize(dataSource.getMaximumPoolSize());
            newConfig.setMinimumIdle(dataSource.getMinimumIdle());
            newConfig.setIdleTimeout(dataSource.getIdleTimeout());
            newConfig.setMaxLifetime(dataSource.getMaxLifetime());
            newConfig.setConnectionTimeout(dataSource.getConnectionTimeout());
            newConfig.setAllowPoolSuspension(dataSource.isAllowPoolSuspension());
            newConfig.setConnectionTestQuery(dataSource.getConnectionTestQuery());
            newConfig.setKeepaliveTime(dataSource.getKeepaliveTime());
            newConfig.setAutoCommit(dataSource.isAutoCommit());
            newConfig.setIsolateInternalQueries(dataSource.isIsolateInternalQueries());
            newConfig.setInitializationFailTimeout(dataSource.getInitializationFailTimeout());

            // 创建新的数据源实例，有可能会遇到密码错误导致创建失败
            HikariDataSource newDataSource;
            try {
                newDataSource = new HikariDataSource(newConfig);
            } catch (Exception e) {
                GalaxyLogger.error("Failed to create new datasource: " + e.getMessage(), e);
                throw new RuntimeException("Failed to create new datasource", e);
            }

            dynamicDataSource.updateDataSource(newDataSource, poolName);
            this.dataSource = newDataSource;

            if (oldDataSource != null) {
                gracefulShutdown(oldDataSource);
            }

            GalaxyLogger.info("Successfully updated datasource with new password");
        } catch (Exception e) {
            GalaxyLogger.error("Failed to create new datasource: " + e.getMessage(), e);
            throw new RuntimeException("Failed to update database password", e);
        }
    }

    private void gracefulShutdown(HikariDataSource oldDataSource) {
        CompletableFuture.runAsync(() -> {
            try {
                HikariPoolMXBean poolMXBean = oldDataSource.getHikariPoolMXBean();
                HikariConfigMXBean configMXBean = oldDataSource.getHikariConfigMXBean();

                // 1. 设置最小空闲连接为 0，停止创建新的空闲连接
                configMXBean.setMinimumIdle(0);

                // 2. 设置最大连接数为当前活跃连接数，防止新建连接
                int currentActive = poolMXBean.getActiveConnections();
                if (currentActive >= 1) {
                    configMXBean.setMaximumPoolSize(currentActive);
                }

                // 3. 关闭空闲连接
                poolMXBean.softEvictConnections();

                GalaxyLogger.info("开始等待连接关闭，当前活跃连接数: " + currentActive);
                ScheduledExecutorService executor;
                try {
                    executor = Executors.newSingleThreadScheduledExecutor();
                    executor.scheduleAtFixedRate(() -> {
                        try {
                            int activeConnections = poolMXBean.getActiveConnections();
                            if (activeConnections == 0) {
                                oldDataSource.close();
                                GalaxyLogger.info("所有连接已关闭，数据源成功关闭");
                                executor.shutdown();
                            }
                        } catch (Exception e) {
                            GalaxyLogger.error("检查连接状态时发生错误: " + e.getMessage(), e);
                            executor.shutdownNow();
                        }
                    }, 0, 1, TimeUnit.SECONDS);
                } catch (Exception e) {
                    GalaxyLogger.error("创建定时任务执行器失败: " + e.getMessage(), e);
                }
            } catch (Exception e) {
                GalaxyLogger.error("优雅关闭过程中发生错误: " + e.getMessage(), e);
            }
        }).exceptionally(throwable -> {
            GalaxyLogger.error("数据源关闭过程中发生异常: " + throwable.getMessage(), throwable);
            return null;
        });
    }
}
