### 组件介绍

> GalaxyBoot Oceanbase Starter 是一个为 Oceanbase 数据库提供的 Spring Boot Starter，用于简化应用的开发。

GalaxyBoot Oceanbase Starter 默认使用 HikariCP 作为连接池，因为 `HikariCP` 是 `Spring Boot 2.x` 和 `Spring Boot 3.x`
的[默认连接池](https://docs.spring.io/spring-boot/reference/data/sql.html#data.sql.datasource.connection-pool)
，性能优秀，适合高并发场景。

- 当你使用了 `spring-boot-starter-jdbc` 或 `spring-boot-starter-data-jpa` 依赖时，就会自动包含 `HikariCP` 的依赖。

当前 GalaxyBoot Oceanbase Starter 支持的配置项：

```yaml
galaxy:
  datasource:
    dynamic-maximum-pool-size: true         # 通过 Apollo 配置中心动态更新连接池配置
    dynamic-refresh-username-password: true # 动态修改数据库密码（开发中）
```

#### 动态数据库连接池配置

到当前，计划支持两种方式动态更新 `HikariCP` 连接池配置：

1. 通过 Apollo 配置中心动态更新连接池配置（当前方式）
2. Nacos 配置中心动态更新连接池配置（未来）
3. ~~通过 AutoConfiguration + Servlet 的方式动态更新连接池配置~~（不合适、不使用）

#### 动态修改数据库密码

当前支持通过 Apollo 配置中心动态修改数据库密码

### 组件实例教程

使用前，请确保项目中引入了 apollo-client 依赖以及配置了 Apollo 配置中心的地址。在配置了 `spring-boot-starter-parent`
的情况下，只需要在项目的 pom.xml 文件中添加如下依赖即可：

```xml

<dependency>
    <groupId>com.ctrip.framework.apollo</groupId>
    <artifactId>apollo-client</artifactId>
</dependency>
```

确保在 `application.yaml` 中配置了 Apollo 配置中心的地址，如：

```yaml
app:
  id: datasource                 # 修改为自己的应用 ID
apollo:
  meta: http://localhost:8080    # Apollo 配置中心地址
```

启动后检查是否成功连接到 Apollo 配置中心，可以在日志中看到类似如下的信息：

```
V1|........|Located meta services from apollo.meta configuration: http://localhost:8080!|-
V1|........|Located meta server address http://localhost:8080 for env UNKNOWN from com.ctrip.framework.apollo.internals.DefaultMetaServerProvider|-
```

#### 修改数据库连接池配置

注意：Galaxy Datasource 仅用于提供动态配置等的支持，其它配置项和 Spring Datasource 一致，如：

```yaml
app:
  id: datasource                 # 修改为自己的应用 ID
apollo:
  meta: http://localhost:8080    # Apollo 配置中心地址

spring:
  datasource:
    # 其它配置项 
    # 请根据实际情况修改以下配置
    hikari:
      maximum-pool-size: 10

galaxy:
  datasource:
    dynamic-maximum-pool-size: true         # 通过 Apollo 配置中心动态更新连接池配置
```

使用：

1. 在项目中引入 `galaxy-boot-starter-data-oceanbase` 依赖，并按需配置 `application.yaml` 文件即可使用。
2. 通过 Apollo 配置中心动态更新连接池配置，对应的 Key 为 `galaxy.datasource.dynamic-maximum-pool-size`。
3. 查看连接池配置是否生效，可以通过日志或者 `http://localhost:8080/actuator/prometheus` 端点查看 `HikariCP` 的监控数据。

修改完成功的日志示例：

```
V1|...|com.ctrip.framework.apollo.spring.property.AutoUpdateConfigChangeListener|-|Auto update apollo changed value successfully, new value: 50, key: spring.datasource.hikari.maximum-pool-size, beanName: cn.com.chinastock.cnf.oceanbase.GalaxyHikariDynamicPasswordAutoConfiguration, field: cn.com.chinastock.cnf.oceanbase.GalaxyHikariDynamicPasswordAutoConfiguration$$SpringCGLIB$$0.maxActive|-
V1|...|cn.com.chinastock.cnf.oceanbase.GalaxyHikariDynamicPoolSizeAutoConfiguration|FRAMEWORK_LOG|Updating maximum pool size from 10 to 50|-
```

#### 动态修改数据库密码

确保在 `application.yaml` 中配置了 Apollo 配置中心的地址，并启用动态密码刷新功能：

```yaml
app:
  id: datasource                 # 修改为自己的应用 ID
apollo:
  meta: http://localhost:8080    # Apollo 配置中心地址

galaxy:
  datasource:
    dynamic-refresh-username-password: true  # 启用动态密码刷新
```

通过 Apollo 配置中心动态修改数据库密码的使用步骤：

1. 在 Apollo 配置中心修改密码配置项：
    - 配置 Key：`spring.datasource.password`
    - 配置 Value：新的数据库密码
2. 密码更新流程：
    - 系统检测到 Apollo 配置变更时会自动触发密码更新
    - 创建新的数据源连接池，使用新密码进行验证
    - 验证成功后，系统会平滑切换到新的数据源
    - 旧的数据源会等待现有连接自然关闭后再释放资源
3. 可以通过系统日志查看密码更新状态：
    - 更新成功：输出 "Successfully updated datasource with new password"
    - 更新失败：输出具体的错误信息

注意事项：

- 密码更新过程是异步的，不会影响现有业务连接
- 确保新密码正确，错误的密码会导致新连接池创建失败，系统会自动回滚到旧的数据源。
- 建议在业务低峰期进行密码更新操作

修改成功后的日志示例：

```
V1|...|Apollo-Config-5|-|-|-|-|-|-|-|com.ctrip.framework.apollo.spring.property.AutoUpdateConfigChangeListener|-|Auto update apollo changed value successfully, new value: f5GGWuGxxxxxxxx, key: spring.datasource.password, beanName: cn.com.chinastock.cnf.oceanbase.GalaxyHikariDynamicPasswordAutoConfiguration, field: cn.com.chinastock.cnf.oceanbase.GalaxyHikariDynamicPasswordAutoConfiguration$$SpringCGLIB$$0.password|-
V1|...|Apollo-Config-6|-|-|-|-|-|-|-|com.zaxxer.hikari.HikariDataSource|-|HikariPool-1736324339473 - Starting...|-
V1|...|Apollo-Config-6|-|-|-|-|-|-|-|com.zaxxer.hikari.pool.HikariPool|-|HikariPool-1736324339473 - Added connection com.mysql.cj.jdbc.ConnectionImpl@276fe793|-
V1|...|Apollo-Config-6|-|-|-|-|-|-|-|com.zaxxer.hikari.HikariDataSource|-|HikariPool-1736324339473 - Start completed.|-
V1|...|Apollo-Config-6|-|-|-|-|-|-|-|cn.com.chinastock.cnf.oceanbase.GalaxyHikariDynamicPasswordAutoConfiguration|FRAMEWORK_LOG|Successfully updated datasource with new password|-
V1|...|ForkJoinPool.commonPool-worker-1|-|-|-|-|-|-|-|cn.com.chinastock.cnf.oceanbase.GalaxyHikariDynamicPasswordAutoConfiguration|FRAMEWORK_LOG|开始等待连接关闭，当前活跃连接数: 0|-
V1|...|pool-2-thread-1|-|-|-|-|-|-|-|com.zaxxer.hikari.HikariDataSource|-|HikariPool-1736323799576 - Shutdown initiated...|-
V1|...|pool-2-thread-1|-|-|-|-|-|-|-|com.zaxxer.hikari.HikariDataSource|-|HikariPool-1736323799576 - Shutdown completed.|-
V1|...|pool-2-thread-1|-|-|-|-|-|-|-|cn.com.chinastock.cnf.oceanbase.GalaxyHikariDynamicPasswordAutoConfiguration|FRAMEWORK_LOG|所有连接已关闭，数据源成功关闭|-
```

### Oceanbase 最佳实践

Oceanbase
官方文档：《[Java 应用与 OceanBase 数据库连接配置最佳实践](https://www.oceanbase.com/docs/common-best-practices-1000000001489649)》

在进行应用程序与 OceanBase 数据库交互时，合适的驱动配置能极大提高系统的稳定性与性能。以下是一些主要的配置项：

- **连接超时设置**：设置客户端连接数据库的等待时间，例如配置 `connectTimeout` 和 `socketTimeout`
  。同时，要实施快速失败重试机制，确保在连接失败时能迅速重试，并设定连接间隔时间，以减少网络阻塞和系统负载。
- **日志记录**：在程序运行期间，应记录连接数据库的 OceanBase 错误码及连接信息（如 IP、PORT、用户名），以便 DBA 快速诊断和解决问题。
- **版本兼容性**：确保客户端库（如 `.so` 或 `.jar` 包）与数据库服务器版本兼容，以保证各组件间的正确协作。
- **切换数据库方法**：推荐使用 `Connection.setCatalog(dbname)` 接口，而非 SQL 命令 `use <dbname>`，以提高代码的可读性和维护性。
- **Session 状态变量设置**：通过 JDBC 接口（如 `setAutoCommit`、`setReadOnly`、`setTransactionIsolation`）来设置 Session
  状态变量，以减少 SQL 使用频率，降低数据库交互次数并提升性能。
- **事务处理**：在执行单个事务（无论是单条 SQL 还是多条 SQL）之前，重新获取数据库连接（`getConnection`），事务执行完毕后，关闭连接（
  `closeConnection`）。确保每个事务独立处理，以防止连接复用导致的状态污染，确保数据的一致性和隔离性。

通过合理配置以上项，您将能够显著提升 OceanBase 数据库与应用程序之间的交互效率和稳定性。
