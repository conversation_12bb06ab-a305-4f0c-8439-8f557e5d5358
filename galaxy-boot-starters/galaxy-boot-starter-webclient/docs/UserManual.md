### 组件使用说明
`Galaxy Boot Starter WebClient` 是一个基于 `Spring WebFlux` 的响应式 HTTP 客户端封装，提供了统一的、规范化的服务调用功能。
更多使用方式请参考 [Spring WebFlux 官方文档](https://docs.spring.io/spring-framework/reference/web/webflux-webclient.html)。

### 组件功能介绍

#### 声明式客户端接口
`Galaxy Boot Starter WebClient` 提供了声明式的客户端接口定义方式，通过注解即可完成HTTP服务调用的配置。

使用 `@WebClientExchange` 注解定义客户端接口：
```java
@WebClientExchange(name = "user-service")
public interface UserClient {
    
    @GetExchange("/api/users/{id}")
    Mono<User> getUserById(@PathVariable String id);
    
    @PostExchange("/api/users")
    Mono<User> createUser(@RequestBody User user);
    
    @PutExchange("/api/users/{id}")
    Mono<User> updateUser(@PathVariable String id, @RequestBody User user);
    
    @DeleteExchange("/api/users/{id}")
    Mono<Void> deleteUser(@PathVariable String id);
}
```

在启动类上添加 `@EnableWebClients` 注解：
```java
@SpringBootApplication
@EnableWebClients
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

#### 传递TraceContext
`Galaxy Boot Starter WebClient` 会自动传递当前请求上下文的 `trace context` 到远程服务，以实现分布式链路追踪。

#### ESB认证信息传递
`Galaxy Boot Starter WebClient` 在识别到当前请求为ESB请求时，会自动传递ESB认证信息到远程服务。
- 识别ESB请求的方式：通过请求头中的 `Function-No` 字段，如果该字段存在则认为当前请求为ESB请求。
- 如果识别为ESB请求，则会自动从配置中获取ESB认证信息，并传递到远程服务。
- ESB认证信息的内容包括以下请求头信息：
  ```java
    public static final String CONTENT_TYPE_HEADER = "Content-Type";
    public static final String USER_HEADER = "User";
    public static final String CREATED_HEADER = "Created";
    public static final String NONCE_HEADER = "Nonce";
    public static final String PWD_DIGEST_HEADER = "Password-Digest";
    public static final String CALLER_SYSTEM_CODE_HEADER = "Caller-System-Code";
  ```
  其中`User`和`Password`需要在`application.yml`中配置：
  ```yaml
  galaxy:
    webclient:
      esb:
        user: user
        password: password
  ```
- ESB认证信息的生成是通过ESB提供的SDK来实现：
  ```xml
    <dependency>
        <groupId>com.chinastock.esb</groupId>
        <artifactId>auth</artifactId>
    </dependency>
  ```

#### 异常处理
在`Galaxy Boot Core`中定义了统一的异常处理，远端服务在返回非200的HTTP状态码时，可能会在Body中返回Meta信息。
`Galaxy Boot Starter WebClient` 会自动解析Meta信息，并抛出`GalaxyWebClientException`异常。
开发者可以捕获该异常，并获取Meta信息，以便进行后续处理。

```java
@GetExchange("/api/users/{id}")
Mono<User> getUserById(@PathVariable String id) {
    return userClient.getUserById(id)
        .onErrorResume(throwable -> {
            if (throwable instanceof GalaxyWebClientException e) {
                // 处理业务异常
                Meta meta = e.getMeta();
                logger.error("业务异常: {}", meta.getMessage());
                return Mono.empty();
            }
            return Mono.error(throwable);
        });
}
```

### 组件配置说明
`Galaxy Boot Starter WebClient` 基于 `Spring WebFlux` 和 `Reactor Netty`，提供响应式的HTTP客户端能力。

#### HTTP客户端配置

#### 负载均衡配置
`Galaxy Boot Starter WebClient` 集成了Spring Cloud LoadBalancer，提供服务负载均衡能力。

- 基础配置
```yaml
spring:
  cloud:
    loadbalancer:
      enabled: true        # 启用负载均衡，默认false
      retry:
        enabled: false     # 是否启用重试机制，默认true
```

- 服务实例配置
在不使用注册中心的情况下，可以通过以下配置手动指定服务实例：
```yaml
spring:
  cloud:
    discovery:
      client:
        simple:
          instances:
            service-name:                # 服务名称
              - uri: http://host1:port1  # 实例1地址
              - uri: http://host2:port2  # 实例2地址
```

例如配置两个服务实例：
```yaml
spring:
  cloud:
    discovery:
      client:
        simple:
          instances:
            user-service:
              - uri: http://localhost:8081
              - uri: http://localhost:8082
```

#### 客户端配置
- 通过 `@WebClientExchange` 注解配置客户端
```java
@WebClientExchange(
    name = "user-service",           // 服务名称，用于负载均衡
    url = "http://localhost:8081",   // 服务URL，可以包含占位符
    path = "/api/v1"                 // 路径前缀
)
public interface UserClient {
    // 接口方法定义
}
```

- 支持占位符配置
```yaml
# application.yml
services:
  user-service:
    url: http://localhost:8081
    path: /api/v1
```

```java
@WebClientExchange(
    name = "user-service",
    url = "${services.user-service.url}",
    path = "${services.user-service.path}"
)
public interface UserClient {
    // 接口方法定义
}
```

### 使用示例

#### 基本使用
```java
// 1. 定义客户端接口
@WebClientExchange(name = "user-service")
public interface UserClient {
    
    @GetExchange("/api/users/{id}")
    Mono<User> getUserById(@PathVariable String id);
    
    @PostExchange("/api/users")
    Mono<User> createUser(@RequestBody User user);
}

// 2. 在启动类启用WebClient扫描
@SpringBootApplication
@EnableWebClients
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}

// 3. 注入并使用客户端
@RestController
public class UserController {
    
    @Autowired
    private UserClient userClient;
    
    @GetMapping("/users/{id}")
    public Mono<User> getUser(@PathVariable String id) {
        return userClient.getUserById(id);
    }
}
```

#### 配置文件
```yaml
spring:
  cloud:
    loadbalancer:
      enabled: true
    discovery:
      client:
        simple:
          instances:
            user-service:
              - uri: http://localhost:8081
              - uri: http://localhost:8082

galaxy:
  webclient:
    esb:
      user: esb-user
      password: esb-password
```

完整的使用示例请参考：`galaxy-boot-examples/galaxy-boot-webclient-example`
