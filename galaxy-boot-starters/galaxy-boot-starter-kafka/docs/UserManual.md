
### 配置说明

#### 基础配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `galaxy.kafka.enable` | Boolean | false | 是否启用Kafka自动配置 |
| `galaxy.kafka.server.nodes` | String | localhost | Kafka服务器节点地址，多个节点用逗号分隔 |

#### 日志拦截器配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `galaxy.kafka.log.enabled` | Boolean | true | 是否启用Kafka日志拦截器 |
| `galaxy.kafka.log.max-detail-records-count` | Integer | 5 | 消费者详细日志记录的最大记录数 |

#### JAAS认证配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `galaxy.kafka.jaas.enable` | Boolean | true | 是否启用JAAS认证 |
| `galaxy.kafka.username` | String | ctccenter | JAAS认证用户名 |
| `galaxy.kafka.password` | String | ctccenter | JAAS认证密码 |
| `sasl.mechanism` | String | PLAIN | SASL机制 |
| `security.protocol` | String | SASL_PLAINTEXT | 安全协议 |
| `loginModule` | String | org.apache.kafka.common.security.plain.PlainLoginModule | 登录模块 |

#### Producer配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `galaxy.kafka.producer.acks` | String | all | 生产者确认配置 |
| `galaxy.kafka.producer.retries` | String | 3 | 重试次数 |
| `galaxy.kafka.producer.batch.size` | Integer | 16384 | 批次大小（字节） |
| `galaxy.kafka.producer.linger.ms` | Integer | 0 | 批次延迟时间（毫秒） |
| `galaxy.kafka.producer.buffer.memory` | Integer | 33554432 | 缓冲区内存大小（字节） |
| `galaxy.kafka.producer.request.timeout` | Integer | 2000 | 请求超时时间（毫秒） |
| `galaxy.kafka.max.block.ms` | Integer | 1000 | 最大阻塞时间（毫秒） |
| `galaxy.kafka.compression.type` | String | none | 压缩类型 |

#### Consumer配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `galaxy.kafka.consumer.group.id` | String | test | 消费者组ID |
| `galaxy.kafka.consumer.enable.auto.commit` | String | true | 是否启用自动提交 |
| `galaxy.kafka.consumer.auto.commit.interval.ms` | String | 1000 | 自动提交间隔（毫秒） |
| `galaxy.kafka.consumer.batch.size` | Integer | 10 | 批次大小 |
| `galaxy.kafka.consumer.auto.offset.reset` | String | earliest | 偏移量重置策略 |
| `galaxy.kafka.consumer.session.timeout.ms` | String | 30000 | 会话超时时间（毫秒） |
| `galaxy.kafka.consumer.concurrency` | Integer | 3 | 并发级别 |
| `galaxy.kafka.consumer.batch.listener` | Boolean | true | 是否启用批量监听器 |
| `galaxy.kafka.consumer.poll.timeout` | Integer | 1500 | 轮询超时时间（毫秒） |
| `galaxy.kafka.consumer.max.poll.records` | String | 3100 | 单次轮询最大记录数 |
| `galaxy.kafka.consumer.max.poll.interval` | String | 15000 | 最大轮询间隔（毫秒） |
| `galaxy.kafka.consumer.max.partition-fetch-bytes` | String | 15728640 | 单个分区最大获取字节数 |
| `galaxy.kafka.consumer.is.ack` | Boolean | false | 是否启用手动确认 |

#### 日志拦截器功能

Galaxy Boot Starter Kafka 提供了强大的日志拦截器功能，可以自动记录Kafka消息的发送、接收、确认和提交等操作，帮助开发者监控和调试Kafka应用。

##### 配置示例

```yaml
galaxy:
  kafka:
    enable: true
    server:
      nodes: localhost:9092
    log:
      enabled: true                 # 启用Kafka日志拦截器
      max-detail-records-count: 5   # 消费者批量消费时，详细日志记录的最大记录数（避免大批量数据导致日志输出过多）
```

##### 日志格式说明

###### Producer发送日志
```
V1|2025-07-01T14:32:15.882+0800|285630181031125|INFO|main|${sys:ip-addr}|-|-|-|-|-|${sys:system-code}|${sys:service-name}|cn.com.chinastock.cnf.kafka.interceptor.GalaxyKafkaProducerInterceptor|FRAMEWORK_LOG|Kafka Producer Send: action=send topic=test-topic timestamp=null key=test-key value=test-value|-
```

**字段说明**:
- `action=send`: 操作类型为发送
- `topic=test-topic`: 目标主题
- `timestamp=null`: 消息时间戳（可选）
- `key=test-key`: 消息键（可选）
- `value=test-value`: 消息值（可选）
- `partition=0`: 分区号（可选）

###### Producer确认日志
```
V1|2025-07-01T14:32:15.901+0800|285630199912708|INFO|main|${sys:ip-addr}|-|-|-|-|-|${sys:system-code}|${sys:service-name}|cn.com.chinastock.cnf.kafka.interceptor.GalaxyKafkaProducerInterceptor|FRAMEWORK_LOG|Kafka Producer Ack: action=ack topic=test-topic partition=0 offset=0 status=success timestamp=0|-
```

**字段说明**:
- `action=ack`: 操作类型为确认
- `topic=test-topic`: 主题名称
- `partition=0`: 分区号
- `offset=0`: 消息偏移量
- `status=success`: 确认状态（success/failed）
- `timestamp=0`: 确认时间戳

###### Producer确认失败日志
```
V1|2025-07-01T14:32:15.905+0800|285630203424375|ERROR|main|${sys:ip-addr}|-|-|-|-|-|${sys:system-code}|${sys:service-name}|cn.com.chinastock.cnf.kafka.interceptor.GalaxyKafkaProducerInterceptor|FRAMEWORK_LOG|Kafka Producer Ack Failed: action=ack status=failed error=Connection timeout|-
```

**字段说明**:
- `action=ack`: 操作类型为确认
- `status=failed`: 确认状态为失败
- `error=Connection timeout`: 错误信息

###### Consumer批量接收日志
```
V1|2025-07-01T14:32:14.833+0800|285629131499875|INFO|main|${sys:ip-addr}|-|-|-|-|-|${sys:system-code}|${sys:service-name}|cn.com.chinastock.cnf.kafka.interceptor.GalaxyKafkaConsumerInterceptor|FRAMEWORK_LOG|Kafka Consumer Receive: action=consume recordCount=2 topic1-0=1 topic2-1=1|-
```

**字段说明**:
- `action=consume`: 操作类型为消费
- `recordCount=2`: 总记录数
- `topic1-0=1`: 主题topic1分区0的记录数
- `topic2-1=1`: 主题topic2分区1的记录数

###### Consumer详细记录日志
当消费的记录数量不超过`max-detail-records-count`配置值时，系统会为每条记录输出详细日志：

```
V1|2025-07-01T14:32:14.835+0800|285629133256250|INFO|main|${sys:ip-addr}|-|-|-|-|-|${sys:system-code}|${sys:service-name}|cn.com.chinastock.cnf.kafka.interceptor.GalaxyKafkaConsumerInterceptor|FRAMEWORK_LOG|Kafka Consumer Detail: action=consume_detail topic=test-topic partition=0 offset=100 timestamp=1640995200000 key=test-key value=test-message|-
```

**字段说明**:
- `action=consume_detail`: 操作类型为详细消费记录
- `topic=test-topic`: 消息主题
- `partition=0`: 分区号
- `offset=100`: 消息偏移量
- `timestamp=1640995200000`: 消息时间戳
- `key=test-key`: 消息键（可选，如果为null则不显示）
- `value=test-message`: 消息值（可选，如果为null则不显示）

**注意**: 只有当单次消费的记录总数不超过`galaxy.kafka.log.max-detail-records-count`配置值时，才会输出每条记录的详细日志。这样设计是为了避免在高吞吐量场景下产生过多的日志。

###### Consumer提交日志
```
V1|2025-07-01T14:32:14.842+0800|285629141139750|INFO|main|${sys:ip-addr}|-|-|-|-|-|${sys:system-code}|${sys:service-name}|cn.com.chinastock.cnf.kafka.interceptor.GalaxyKafkaConsumerInterceptor|FRAMEWORK_LOG|Kafka Consumer Commit: action=commit partitionCount=1 test-topic-0=100|-
```

**字段说明**:
- `action=commit`: 操作类型为提交
- `partitionCount=1`: 分区数量
- `test-topic-0=100`: 主题test-topic分区0的偏移量为100

### 使用示例

#### Producer示例

##### 1. 基础Producer使用

```java
@RestController
public class MessageController {

    @Autowired
    private KafkaTemplate<Integer, String> kafkaTemplate;

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplateForString;

    @PostMapping("/send")
    public String sendMessage(@RequestParam String message) {
        // 使用Integer作为key的KafkaTemplate
        kafkaTemplate.send("test-topic", 1, message);

        // 使用String作为key的KafkaTemplate
        kafkaTemplateForString.send("test-topic", "key1", message);

        return "Message sent successfully";
    }

    @PostMapping("/send-with-callback")
    public String sendMessageWithCallback(@RequestParam String message) {
        // 发送消息并处理回调
        kafkaTemplateForString.send("test-topic", "key1", message)
            .addCallback(
                result -> {
                    // 发送成功回调
                    RecordMetadata metadata = result.getRecordMetadata();
                    System.out.println("Message sent successfully to topic: " +
                        metadata.topic() + ", partition: " + metadata.partition() +
                        ", offset: " + metadata.offset());
                },
                failure -> {
                    // 发送失败回调
                    System.err.println("Failed to send message: " + failure.getMessage());
                }
            );

        return "Message sent with callback";
    }
}
```

#### Consumer示例

##### 1. 基础Consumer使用

```java
@Component
public class MessageConsumer {

    @KafkaListener(topics = "test-topic", groupId = "test-group")
    public void listen(String message) {
        System.out.println("Received message: " + message);
    }

    @KafkaListener(topics = "test-topic", groupId = "test-group")
    public void listenWithMetadata(ConsumerRecord<String, String> record) {
        System.out.println("Received message: " + record.value() +
            " from topic: " + record.topic() +
            ", partition: " + record.partition() +
            ", offset: " + record.offset());
    }
}
```

##### 2. 批量消费

```java
@Component
public class BatchMessageConsumer {

    @KafkaListener(topics = "test-topic", groupId = "batch-group")
    public void listenBatch(List<ConsumerRecord<String, String>> records) {
        System.out.println("Received batch of " + records.size() + " messages");

        for (ConsumerRecord<String, String> record : records) {
            System.out.println("Processing message: " + record.value() +
                " from partition: " + record.partition() +
                ", offset: " + record.offset());
        }
    }
}
```

#### 配置文件示例

```yaml
# application.yml
spring:
  application:
    name: kafka-demo-app

galaxy:
  kafka:
    enable: true
    server:
      nodes: localhost:9092
    log:
      enabled: true
      max-detail-records-count: 10
    jaas:
      enable: false
    producer:
      acks: all
      retries: 3
      batch.size: 16384
    consumer:
      group.id: demo-group
      auto.offset.reset: earliest
      concurrency: 3
      batch.listener: true
      is.ack: true
```

### Apollo动态配置支持

Galaxy Boot Starter Kafka 支持通过Apollo配置中心进行动态配置更新：

#### 支持的动态配置项
- `galaxy.kafka.log.enabled`: 动态启用/禁用日志拦截器
- `galaxy.kafka.log.max-detail-records-count`: 动态调整详细日志记录数

### 最佳实践

#### 安全配置

##### SASL/PLAIN认证
```yaml
galaxy:
  kafka:
    jaas:
      enable: true
      username: your-username
      password: your-password
    server:
      nodes: secure-kafka-cluster:9093

# 系统属性配置
sasl.mechanism: PLAIN
security.protocol: SASL_PLAINTEXT
```

##### SSL配置
```yaml
galaxy:
  kafka:
    server:
      nodes: ssl-kafka-cluster:9094

# 额外的SSL配置
security.protocol: SSL
ssl.truststore.location: /path/to/kafka.client.truststore.jks
ssl.truststore.password: truststore-password
ssl.keystore.location: /path/to/kafka.client.keystore.jks
ssl.keystore.password: keystore-password
ssl.key.password: key-password
```

### 故障排查

#### 常见问题

##### 1. 连接问题
**症状**: 无法连接到Kafka集群
**解决方案**:
- 检查`galaxy.kafka.server.nodes`配置是否正确
- 验证网络连通性
- 检查防火墙设置
- 确认Kafka服务是否正常运行

##### 2. 认证失败
**症状**: SASL认证失败
**解决方案**:
- 检查用户名密码是否正确
- 验证JAAS配置
- 确认Kafka集群的认证配置

##### 3. 消息丢失
**症状**: 发送的消息没有被消费到
**解决方案**:
- 检查Producer的`acks`配置
- 验证Consumer的`auto.offset.reset`配置
- 检查消费者组状态
- 查看Kafka日志

##### 4. 性能问题
**症状**: 消息处理延迟高
**解决方案**:
- 调整Consumer并发度
- 优化批处理配置
- 检查网络延迟
- 监控JVM性能

##### 5. 日志拦截器不工作
**症状**: 没有看到Kafka相关日志输出
**解决方案**:
- 检查`galaxy.kafka.log.enabled`配置
- 验证日志级别设置
- 确认拦截器是否正确配置
- 检查动态配置是否生效

```
