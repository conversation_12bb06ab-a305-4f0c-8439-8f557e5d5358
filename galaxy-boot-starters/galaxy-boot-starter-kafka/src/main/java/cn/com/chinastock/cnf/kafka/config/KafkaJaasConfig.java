package cn.com.chinastock.cnf.kafka.config;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.security.jaas.KafkaJaasLoginModuleInitializer;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Configuration
public class KafkaJaasConfig {

    @Value("${galaxy.kafka.username:ctccenter}")
    private String userName;

    @Value("${galaxy.kafka.password:ctccenter}")
    private String password;

    @Value("${loginModule:org.apache.kafka.common.security.plain.PlainLoginModule}")
    private String loginModule;

    IGalaxyLogger galaxyLogger = GalaxyLoggerFactory.getLogger(GalaxyKafkaAutoConfiguration.class);

    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnExpression("${galaxy.kafka.jaas.enable:true}")
    public KafkaJaasLoginModuleInitializer kafkaJaasInitializer() throws IOException {
        galaxyLogger.info("GalaxyBoot: Initializing KafkaJaasLoginModuleInitializer");

        KafkaJaasLoginModuleInitializer jaas = new KafkaJaasLoginModuleInitializer();
        jaas.setLoginModule(loginModule);
        Map<String, String> options = new HashMap<>();
        options.put("username", userName);
        options.put("password", password);
        jaas.setOptions(options);
        return jaas;
    }
}
