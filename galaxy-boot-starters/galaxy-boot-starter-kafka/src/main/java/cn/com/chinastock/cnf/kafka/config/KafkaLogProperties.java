package cn.com.chinastock.cnf.kafka.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;


/**
 * Kafka日志配置属性
 * <p>
 * 注意：当前实现不支持Apollo动态更新，因为Kafka拦截器配置在应用启动时就固化了。
 * 如需支持动态更新，需要实现动态拦截器管理机制。
 *
 * <AUTHOR>
 */
@ConfigurationProperties(prefix = "galaxy.kafka.log")
public class KafkaLogProperties {

    public static final int DEFAULT_MAX_DETAIL_RECORDS_COUNT = 5;

    /**
     * 是否启用Kafka日志拦截器
     */
    @Value("${galaxy.kafka.log.enabled:true}")
    private boolean enabled = true;

    /**
     * 消费者详细日志记录的最大记录数
     */
    @Value("${galaxy.kafka.log.max-detail-records-count:5}")
    private int maxDetailRecordsCount = DEFAULT_MAX_DETAIL_RECORDS_COUNT;

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public int getMaxDetailRecordsCount() {
        return maxDetailRecordsCount;
    }

    public void setMaxDetailRecordsCount(int maxDetailRecordsCount) {
        this.maxDetailRecordsCount = maxDetailRecordsCount;
    }
}
