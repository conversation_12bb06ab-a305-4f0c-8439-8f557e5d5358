package cn.com.chinastock.cnf.kafka.interceptor;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.kafka.config.DynamicKafkaLogManager;
import cn.com.chinastock.cnf.kafka.utils.KafkaLogUtils;
import org.apache.kafka.clients.consumer.ConsumerInterceptor;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.OffsetAndMetadata;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.header.Header;
import org.slf4j.MDC;

import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * Galaxy Kafka Consumer拦截器
 * 用于拦截Kafka消息消费事件并记录日志
 * 支持通过DynamicKafkaLogManager进行动态配置更新
 *
 * 新增功能：
 * 1. 从 traceparent header 中解析 trace 信息并重新生成 spanId
 * 2. 提取幂等性 ID 用于业务重试和避免重复消费
 *
 * @param <K> 消息键的类型
 * @param <V> 消息值的类型
 * <AUTHOR>
 */
public class GalaxyKafkaConsumerInterceptor<K, V> implements ConsumerInterceptor<K, V> {

    private static final IGalaxyLogger galaxyLogger = GalaxyLoggerFactory.getLogger(GalaxyKafkaConsumerInterceptor.class);

    // 幂等性 ID 的 header key
    private static final String IDEMPOTENCY_ID_HEADER = "idempotent_id";

    @Override
    public ConsumerRecords<K, V> onConsume(ConsumerRecords<K, V> records) {
        // 优先使用动态配置，如果动态配置不可用则使用静态配置
        if (DynamicKafkaLogManager.isLogDisabled()) {
            return records;
        }

        // 提取第一条消息的 trace 信息和幂等性 ID 并设置到 MDC
        extractAndSetTraceInfo(records);
        extractAndLogIdempotencyInfo(records);

        try {
            if (records != null && !records.isEmpty()) {
                logConsumerRecords(records);
            }
        } catch (Exception e) {
            galaxyLogger.error(LogCategory.EXCEPTION_LOG, "Failed to log Kafka consumer receive event", e);
        }

        return records;
    }

    private void logConsumerRecords(ConsumerRecords<K, V> records) {
        StringBuilder logMessage = KafkaLogUtils.constructConsumerLogMessage(records);
        galaxyLogger.info(LogCategory.FRAMEWORK_LOG, "Kafka Consumer Receive: {}", logMessage.toString());

        // 使用动态配置的最大详细记录数
        int currentMaxDetailRecords = DynamicKafkaLogManager.getMaxDetailRecordsCount();
        if (records.count() <= currentMaxDetailRecords) {
            for (ConsumerRecord<K, V> record : records) {
                logConsumerRecordDetails(record);
            }
        }
    }


    private void logConsumerRecordDetails(ConsumerRecord<K, V> record) {
        try {
            StringBuilder logMessage = KafkaLogUtils.constructConsumerDetailLogMessage(record);
            galaxyLogger.info(LogCategory.FRAMEWORK_LOG, "Kafka Consumer Record: {}", logMessage.toString());
            if (record.headers() != null) {
                String formattedHeaders = formatHeaders(record.headers());
                galaxyLogger.info(LogCategory.FRAMEWORK_LOG, "Kafka Consumer Record Headers: {}", formattedHeaders);
            }
        } catch (Exception e) {
            galaxyLogger.error(LogCategory.EXCEPTION_LOG, "Failed to log Kafka consumer record detail", e);
        }
    }

    /**
     * 格式化 Kafka headers 为可读的字符串格式
     * 将字节数组转换为字符串，避免输出字节码
     *
     * @param headers Kafka headers
     * @return 格式化后的字符串
     */
    private String formatHeaders(Iterable<Header> headers) {
        if (headers == null) {
            return "null";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("RecordHeaders(headers = [");

        boolean first = true;
        for (Header header : headers) {
            if (!first) {
                sb.append(", ");
            }
            first = false;

            sb.append("RecordHeader(key = ").append(header.key()).append(", value = ");

            if (header.value() == null) {
                sb.append("null");
            } else {
                try {
                    // 尝试将字节数组转换为 UTF-8 字符串
                    String value = new String(header.value(), StandardCharsets.UTF_8);
                    // 检查是否包含不可打印字符，如果是则显示字节数组长度
                    if (isPrintableString(value)) {
                        sb.append("\"").append(value).append("\"");
                    } else {
                        sb.append("[").append(header.value().length).append(" bytes]");
                    }
                } catch (Exception e) {
                    sb.append("[").append(header.value().length).append(" bytes]");
                }
            }

            sb.append(")");
        }

        sb.append("], isReadOnly = false)");
        return sb.toString();
    }

    /**
     * 检查字符串是否为可打印字符串
     *
     * @param str 要检查的字符串
     * @return 如果字符串可打印则返回 true
     */
    private boolean isPrintableString(String str) {
        if (str == null || str.isEmpty()) {
            return true;
        }

        for (char c : str.toCharArray()) {
            // 检查是否为可打印的 ASCII 字符或常见的 Unicode 字符
            if (c < 32 && c != '\t' && c != '\n' && c != '\r') {
                return false;
            }
            if (c == 127) { // DEL 字符
                return false;
            }
        }
        return true;
    }

    @Override
    public void onCommit(Map<TopicPartition, OffsetAndMetadata> offsets) {
        // 优先使用动态配置，如果动态配置不可用则使用静态配置
        if (DynamicKafkaLogManager.isLogDisabled()) {
            return;
        }
        try {
            if (offsets != null && !offsets.isEmpty()) {
                StringBuilder logMessage = KafkaLogUtils.constructConsumerCommitLogMessage(offsets);
                galaxyLogger.debug(LogCategory.FRAMEWORK_LOG, "Kafka Consumer Commit: {}", logMessage.toString());
            }
        } catch (Exception e) {
            // 记录日志异常，但不影响偏移量提交
            galaxyLogger.error(LogCategory.EXCEPTION_LOG, "Failed to log Kafka consumer commit event", e);
        }
    }

    /**
     * 从 Kafka 消息 headers 中提取 trace 信息并设置到 MDC
     * 如果无法获取 traceparent 信息，则生成新的 trace 信息
     *
     * @param records Kafka 消息记录
     */
    private void extractAndSetTraceInfo(ConsumerRecords<K, V> records) {
        if (records == null || records.isEmpty()) {
            return;
        }

        // 获取第一条消息的 trace 信息
        ConsumerRecord<K, V> firstRecord = records.iterator().next();
        boolean traceInfoFound = false;

        if (firstRecord.headers() != null) {
            Header traceparentHeader = firstRecord.headers().lastHeader("traceparent");
            if (traceparentHeader != null) {
                String traceparent = new String(traceparentHeader.value(), StandardCharsets.UTF_8);
                parseAndSetTraceInfo(traceparent);
                traceInfoFound = true;
            }
        }

        // 如果无法从 header 中获取 traceparent 信息，生成新的 trace 信息
        if (!traceInfoFound) {
            generateAndSetNewTraceInfo();
        }
    }

    /**
     * 解析 W3C traceparent 格式并设置到 MDC
     * 格式: version-trace-id-parent-id-trace-flags
     * 例如: 00-4bf92f3577b34da6a3ce929d0e0e4736-00f067aa0ba902b7-01
     *
     * @param traceparent W3C traceparent 字符串
     */
    private void parseAndSetTraceInfo(String traceparent) {
        if (traceparent == null || traceparent.trim().isEmpty()) {
            return;
        }

        try {
            String[] parts = traceparent.split("-");
            if (parts.length >= 4) {
                String traceId = parts[1];
                String parentSpanId = parts[2];

                // 设置到 MDC，使用与 Micrometer Tracing 一致的 key 名称
                MDC.put("traceId", traceId);
                MDC.put("parentSpanId", parentSpanId);
                // 为 Consumer 生成新的 spanId（简化实现，实际应该使用 Micrometer）
                MDC.put("spanId", generateSimpleSpanId());
            }
        } catch (Exception e) {
            galaxyLogger.debug(LogCategory.FRAMEWORK_LOG, "Failed to parse traceparent: {}", traceparent);
        }
    }

    /**
     * 生成新的 trace 信息并设置到 MDC
     * 当无法从消息 header 中获取 traceparent 信息时调用
     */
    private void generateAndSetNewTraceInfo() {
        try {
            // 生成新的 traceId（32位十六进制）
            String traceId = generateTraceId();
            // 生成新的 spanId（16位十六进制）
            String spanId = generateSimpleSpanId();

            // 设置到 MDC
            MDC.put("traceId", traceId);
            MDC.put("spanId", spanId);
            // 没有 parentSpanId，因为这是一个新的 trace

            galaxyLogger.debug(LogCategory.FRAMEWORK_LOG,
                "Generated new trace info for Kafka consumer: traceId={}, spanId={}", traceId, spanId);
        } catch (Exception e) {
            galaxyLogger.debug(LogCategory.FRAMEWORK_LOG, "Failed to generate new trace info", e);
        }
    }

    /**
     * 生成新的 traceId（32位十六进制）
     *
     * @return 32位十六进制的 traceId
     */
    private String generateTraceId() {
        // 生成128位的 traceId，分为两个64位的部分
        long high = System.currentTimeMillis() * 1000000L + (System.nanoTime() % 1000000L);
        long low = System.nanoTime();
        return String.format("%016x%016x", high, low);
    }

    /**
     * 生成简单的 spanId（16位十六进制）
     * 注意：这是简化实现，实际应该使用 Micrometer Tracing
     *
     * @return 16位十六进制的 spanId
     */
    private String generateSimpleSpanId() {
        return String.format("%016x", System.nanoTime() & 0xFFFFFFFFFFFFFFFFL);
    }

    /**
     * 提取并记录幂等性信息
     *
     * @param records Kafka 消息记录
     */
    private void extractAndLogIdempotencyInfo(ConsumerRecords<K, V> records) {
        if (records == null || records.isEmpty()) {
            return;
        }

        try {
            for (ConsumerRecord<K, V> record : records) {
                if (record.headers() != null) {
                    Header idempotencyHeader = record.headers().lastHeader(IDEMPOTENCY_ID_HEADER);
                    if (idempotencyHeader != null) {
                        String idempotencyId = new String(idempotencyHeader.value(), StandardCharsets.UTF_8);

                        // 将幂等性 ID 设置到 MDC 中，供业务代码使用
                        MDC.put("idempotencyId", idempotencyId);

                        // 记录幂等性 ID 信息，便于追踪和调试
                        galaxyLogger.debug(LogCategory.FRAMEWORK_LOG,
                            "Kafka Consumer received message with idempotency ID: {} for topic: {}, partition: {}, offset: {}",
                            idempotencyId, record.topic(), record.partition(), record.offset());

                        // 只处理第一条消息的幂等性 ID
                        break;
                    }
                }
            }
        } catch (Exception e) {
            galaxyLogger.debug(LogCategory.FRAMEWORK_LOG, "Failed to extract idempotency info", e);
        }
    }

    @Override
    public void close() {
        // 清理 MDC 中的幂等性 ID
        try {
            MDC.remove("idempotencyId");
        } catch (Exception e) {
            galaxyLogger.debug(LogCategory.FRAMEWORK_LOG, "Failed to clear idempotency ID from MDC", e);
        }
    }

    @Override
    public void configure(Map<String, ?> configs) {
    }
}
