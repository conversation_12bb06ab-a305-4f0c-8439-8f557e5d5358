package cn.com.chinastock.cnf.kafka.config;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 动态Kafka日志管理器
 * 提供运行时动态控制Kafka日志拦截器的能力
 * <p>
 * 注意：由于Kafka客户端的限制，真正的动态更新需要重新创建Consumer/Producer实例。
 * 这个类提供了一个全局的配置状态管理，拦截器可以实时读取最新配置。
 *
 * <AUTHOR>
 */
@Component
public class DynamicKafkaLogManager {

    private static final AtomicBoolean GLOBAL_LOG_ENABLED = new AtomicBoolean(true);
    private static final AtomicInteger GLOBAL_MAX_DETAIL_RECORDS = new AtomicInteger(5);

    IGalaxyLogger galaxyLogger = GalaxyLoggerFactory.getLogger(DynamicKafkaLogManager.class);

    private final KafkaLogProperties kafkaLogProperties;

    public DynamicKafkaLogManager(KafkaLogProperties kafkaLogProperties) {
        // 默认构造函数，用于Spring自动装配
        this.kafkaLogProperties = kafkaLogProperties;
        syncFromProperties();
        galaxyLogger.info("DynamicKafkaLogManager init, GLOBAL_LOG_ENABLED={}, GLOBAL_MAX_DETAIL_RECORDS={}",
                GLOBAL_LOG_ENABLED, GLOBAL_MAX_DETAIL_RECORDS);
    }

    /**
     * 获取当前日志启用状态
     * 优先使用全局动态配置，如果未设置则使用配置文件值
     *
     * @return 当前日志启用状态
     */
    public static boolean isLogDisabled() {
        return !GLOBAL_LOG_ENABLED.get();
    }

    /**
     * 获取当前最大详细记录数
     * 优先使用全局动态配置，如果未设置则使用配置文件值
     *
     * @return 当前最大详细记录数
     */
    public static int getMaxDetailRecordsCount() {
        return GLOBAL_MAX_DETAIL_RECORDS.get();
    }

    /**
     * 动态更新日志启用状态
     * 这个方法可以被Apollo配置变更监听器调用
     *
     * @param enabled 是否启用日志
     */
    public void updateLogEnabled(boolean enabled) {
        GLOBAL_LOG_ENABLED.set(enabled);
    }

    /**
     * 动态更新最大详细记录数
     * 这个方法可以被Apollo配置变更监听器调用
     *
     * @param count 最大详细记录数
     */
    public void updateMaxDetailRecordsCount(int count) {
        GLOBAL_MAX_DETAIL_RECORDS.set(count);
    }

    /**
     * 从配置属性同步到全局状态
     * 在应用启动时和配置刷新时调用
     */
    public void syncFromProperties() {
        if (kafkaLogProperties != null) {
            GLOBAL_LOG_ENABLED.set(kafkaLogProperties.isEnabled());
            GLOBAL_MAX_DETAIL_RECORDS.set(kafkaLogProperties.getMaxDetailRecordsCount());
        }
    }

    /**
     * 获取当前配置状态的字符串表示
     *
     * @return 配置状态字符串
     */
    public String getConfigStatus() {
        return String.format("KafkaLog[enabled=%s, maxDetailRecords=%d]",
                GLOBAL_LOG_ENABLED.get(), GLOBAL_MAX_DETAIL_RECORDS.get());
    }
}
