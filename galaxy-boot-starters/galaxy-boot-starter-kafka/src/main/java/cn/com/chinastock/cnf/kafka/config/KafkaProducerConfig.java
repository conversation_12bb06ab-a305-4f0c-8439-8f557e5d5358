package cn.com.chinastock.cnf.kafka.config;


import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.kafka.interceptor.GalaxyKafkaProducerInterceptor;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.IntegerSerializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaAdmin;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class KafkaProducerConfig extends AbstractConfig {

    @Value("${galaxy.kafka.server.nodes:localhost}")
    private String kafkaNodes;

    @Value("${galaxy.kafka.producer.acks:all}")
    private String acks;

    @Value("${galaxy.kafka.producer.retries:3}")
    private String retries;

    @Value("${galaxy.kafka.producer.batch.size:16384}")
    private int batchSize;

    @Value("${galaxy.kafka.producer.linger.ms:0}")
    private int lingerMs;

    @Value("${galaxy.kafka.producer.buffer.memory:33554432}")
    private int bufferMemory;

    @Value("${galaxy.kafka.producer.request.timeout:2000}")
    private int requestTimeout;

    @Value("${galaxy.kafka.max.block.ms:1000}")
    private int maxBlockMs;

    @Value("${galaxy.kafka.compression.type:none}")
    private String compressionType;

    private final KafkaLogProperties kafkaLogProperties;

    IGalaxyLogger galaxyLogger = GalaxyLoggerFactory.getLogger(GalaxyKafkaAutoConfiguration.class);
    
    public KafkaProducerConfig(KafkaLogProperties kafkaLogProperties) {
        this.kafkaLogProperties = kafkaLogProperties;
    }

    // topic config Topic的配置
    @Bean
    public KafkaAdmin admin() {
        Map<String, Object> configs = new HashMap<>();
        configs.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaNodes);
        return new KafkaAdmin(configs);
    }

    // 配置生产者Factort及Template
    @Bean
    @ConditionalOnMissingBean(name = "producerFactory")
    public ProducerFactory<Integer, String> producerFactory() {
        Map<String, Object> configs = producerConfigs();
        galaxyLogger.info("GalaxyBoot: Initializing producerFactory with Config: {}", configs);
        return new DefaultKafkaProducerFactory<>(configs);
    }

    @Bean
    @ConditionalOnMissingBean(name = "producerStringKeyFactory")
    public ProducerFactory<String, String> producerStringKeyFactory() {
        Map<String, Object> configs = producerStringKeyConfigs();
        galaxyLogger.info("GalaxyBoot: Initializing producerStringKeyFactory with Config: {}", configs);
        return new DefaultKafkaProducerFactory<>(configs);
    }

    @Bean
    @ConditionalOnMissingBean(name = "producerConfigs")
    public Map<String, Object> producerConfigs() {
        Map<String, Object> props = buildProducerConfigs();
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, IntegerSerializer.class);

        // 添加拦截器配置
        if (kafkaLogProperties != null && kafkaLogProperties.isEnabled()) {
            props.put(ProducerConfig.INTERCEPTOR_CLASSES_CONFIG,
                    GalaxyKafkaProducerInterceptor.class.getName());
        }

        jaas(props);
        return props;
    }

    @Bean
    @ConditionalOnMissingBean(name = "producerStringKeyConfigs")
    public Map<String, Object> producerStringKeyConfigs() {
        Map<String, Object> props = buildProducerConfigs();
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);

        // 添加拦截器配置
        if (kafkaLogProperties != null && kafkaLogProperties.isEnabled()) {
            props.put(ProducerConfig.INTERCEPTOR_CLASSES_CONFIG,
                    GalaxyKafkaProducerInterceptor.class.getName());
        }

        jaas(props);
        return props;
    }

    private Map<String, Object> buildProducerConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaNodes);
        props.put(ProducerConfig.ACKS_CONFIG, acks);
        props.put(ProducerConfig.RETRIES_CONFIG, retries);
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, batchSize);
        props.put(ProducerConfig.LINGER_MS_CONFIG, lingerMs);
        props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, bufferMemory);
        props.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, requestTimeout);
        props.put(ProducerConfig.MAX_BLOCK_MS_CONFIG, maxBlockMs);
        props.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, compressionType);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        return props;
    }

    @Bean
    @ConditionalOnMissingBean(name = "kafkaTemplate")
    public KafkaTemplate<Integer, String> kafkaTemplate() {
        galaxyLogger.info("GalaxyBoot: Initializing KafkaTemplate");
        KafkaTemplate<Integer, String> integerStringKafkaTemplate = new KafkaTemplate<>(producerFactory());
        integerStringKafkaTemplate.setObservationEnabled(true);
        return integerStringKafkaTemplate;
    }

    @Bean
    @ConditionalOnMissingBean(name = "kafkaTemplateForString")
    public KafkaTemplate<String, String> kafkaTemplateForString() {
        galaxyLogger.info("GalaxyBoot: Initializing KafkaTemplateForString");
        KafkaTemplate<String, String> stringStringKafkaTemplate = new KafkaTemplate<>(producerStringKeyFactory());
        stringStringKafkaTemplate.setObservationEnabled(true);
        return stringStringKafkaTemplate;
    }
}
