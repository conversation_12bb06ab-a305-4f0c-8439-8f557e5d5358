package cn.com.chinastock.cnf.kafka.config;

import org.springframework.beans.factory.annotation.Value;

import java.util.Map;

public class AbstractConfig {

    @Value("${sasl.mechanism:PLAIN}")
    protected String saslMechanism;

    @Value("${security.protocol:SASL_PLAINTEXT}")
    protected String securityProtocol;

    @Value("${galaxy.kafka.jaas.enable:true}")
    protected Boolean jaas;

    protected void jaas(Map<String, Object> props) {
        if (jaas) {
            props.put("security.protocol", securityProtocol);
            props.put("sasl.mechanism", saslMechanism);
        }
    }
}
