package cn.com.chinastock.cnf.kafka.config;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.kafka.annotation.EnableKafka;

/**
 * Galaxy Kafka 自动配置类
 *
 * <AUTHOR>
 */
@Configuration
@ConditionalOnProperty(prefix = "galaxy.kafka", name = "enable", havingValue = "true")
@EnableKafka
@EnableConfigurationProperties(KafkaLogProperties.class)
@Import({KafkaJaasConfig.class, KafkaProducerConfig.class, KafkaConsumerConfig.class,
         DynamicKafkaLogManager.class, KafkaLogConfigChangeListener.class})
public class GalaxyKafkaAutoConfiguration {

    IGalaxyLogger galaxyLogger = GalaxyLoggerFactory.getLogger(GalaxyKafkaAutoConfiguration.class);

    public GalaxyKafkaAutoConfiguration() {
        galaxyLogger.info(LogCategory.FRAMEWORK_LOG, "GalaxyBoot: Initializing Kafka AutoConfiguration");
    }
}