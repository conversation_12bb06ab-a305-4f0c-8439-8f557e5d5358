package cn.com.chinastock.cnf.kafka.utils;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.slf4j.MDC;

import java.nio.charset.StandardCharsets;
import java.util.Optional;

/**
 * 幂等性工具类
 * 提供幂等性 ID 的提取、验证和处理功能
 * 
 * <AUTHOR>
 */
public class IdempotencyUtils {

    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(IdempotencyUtils.class);
    
    // 幂等性 ID 的 header key
    private static final String IDEMPOTENCY_ID_HEADER = "idempotent_id";
    
    // MDC 中幂等性 ID 的 key
    private static final String MDC_IDEMPOTENCY_ID_KEY = "idempotencyId";

    /**
     * 从 ConsumerRecord 中提取幂等性 ID
     *
     * @param record Kafka 消费记录
     * @return 幂等性 ID，如果不存在则返回 Optional.empty()
     */
    public static Optional<String> extractIdempotencyId(ConsumerRecord<?, ?> record) {
        if (record == null || record.headers() == null) {
            return Optional.empty();
        }

        try {
            Header idempotencyHeader = record.headers().lastHeader(IDEMPOTENCY_ID_HEADER);
            if (idempotencyHeader != null) {
                String idempotencyId = new String(idempotencyHeader.value(), StandardCharsets.UTF_8);
                return Optional.of(idempotencyId);
            }
        } catch (Exception e) {
            logger.debug(LogCategory.FRAMEWORK_LOG, "Failed to extract idempotency ID from record", e);
        }

        return Optional.empty();
    }

    /**
     * 从 MDC 中获取当前的幂等性 ID
     *
     * @return 幂等性 ID，如果不存在则返回 Optional.empty()
     */
    public static Optional<String> getCurrentIdempotencyId() {
        try {
            String idempotencyId = MDC.get(MDC_IDEMPOTENCY_ID_KEY);
            return Optional.ofNullable(idempotencyId);
        } catch (Exception e) {
            logger.debug(LogCategory.FRAMEWORK_LOG, "Failed to get idempotency ID from MDC", e);
            return Optional.empty();
        }
    }

    /**
     * 设置幂等性 ID 到 MDC
     *
     * @param idempotencyId 幂等性 ID
     */
    public static void setIdempotencyId(String idempotencyId) {
        if (idempotencyId != null && !idempotencyId.trim().isEmpty()) {
            try {
                MDC.put(MDC_IDEMPOTENCY_ID_KEY, idempotencyId);
            } catch (Exception e) {
                logger.debug(LogCategory.FRAMEWORK_LOG, "Failed to set idempotency ID to MDC", e);
            }
        }
    }

    /**
     * 清理 MDC 中的幂等性 ID
     */
    public static void clearIdempotencyId() {
        try {
            MDC.remove(MDC_IDEMPOTENCY_ID_KEY);
        } catch (Exception e) {
            logger.debug(LogCategory.FRAMEWORK_LOG, "Failed to clear idempotency ID from MDC", e);
        }
    }

    /**
     * 检查消息是否包含幂等性 ID
     *
     * @param record Kafka 消费记录
     * @return 如果包含幂等性 ID 则返回 true
     */
    public static boolean hasIdempotencyId(ConsumerRecord<?, ?> record) {
        return extractIdempotencyId(record).isPresent();
    }

    /**
     * 记录幂等性处理日志
     *
     * @param idempotencyId 幂等性 ID
     * @param action 执行的动作（如 "processed", "skipped", "retried"）
     * @param details 详细信息
     */
    public static void logIdempotencyAction(String idempotencyId, String action, String details) {
        if (idempotencyId != null && action != null) {
            logger.info(LogCategory.FRAMEWORK_LOG, 
                "Idempotency action: {} for ID: {}, details: {}", 
                action, idempotencyId, details != null ? details : "");
        }
    }

    /**
     * 记录幂等性处理日志（简化版本）
     *
     * @param action 执行的动作
     * @param details 详细信息
     */
    public static void logIdempotencyAction(String action, String details) {
        getCurrentIdempotencyId().ifPresent(id -> 
            logIdempotencyAction(id, action, details)
        );
    }

    /**
     * 获取幂等性 ID 的 header key
     *
     * @return header key
     */
    public static String getIdempotencyIdHeaderKey() {
        return IDEMPOTENCY_ID_HEADER;
    }

    /**
     * 获取 MDC 中幂等性 ID 的 key
     *
     * @return MDC key
     */
    public static String getMdcIdempotencyIdKey() {
        return MDC_IDEMPOTENCY_ID_KEY;
    }
}
