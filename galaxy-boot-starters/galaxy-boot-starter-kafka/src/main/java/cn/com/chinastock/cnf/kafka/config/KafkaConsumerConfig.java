package cn.com.chinastock.cnf.kafka.config;


import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.kafka.interceptor.GalaxyKafkaConsumerInterceptor;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import org.springframework.kafka.listener.ContainerProperties.AckMode;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class KafkaConsumerConfig extends AbstractConfig {

    @Value("${galaxy.kafka.server.nodes:localhost}")
    private String kafkaNodes;

    @Value("${galaxy.kafka.consumer.group.id:test}")
    private String groupId;

//	@Value("${kafka.consumer.num.partitions:3}")
//	private int numPartitions;

    @Value("${galaxy.kafka.consumer.enable.auto.commit:true}")
    private String enableAutoCommit;

    @Value("${galaxy.kafka.consumer.auto.commit.interval.ms:1000}")
    private String autoCommitIntervalMs;

    @Value("${galaxy.kafka.consumer.batch.size:10}")
    private Integer batchSize;

    @Value("${galaxy.kafka.consumer.auto.offset.reset:earliest}")
    private String autoOffsetReset;

    @Value("${galaxy.kafka.consumer.session.timeout.ms:30000}")
    private String sessionTimeoutMs;

    @Value("${galaxy.kafka.consumer.concurrency:3}")
    private int concurrency;

    @Value("${galaxy.kafka.consumer.batch.listener:true}")
    private boolean batchListner;

    @Value("${galaxy.kafka.consumer.poll.timeout:1500}")
    private int pollTimeout;

    @Value("${galaxy.kafka.consumer.max.poll.records:3100}")
    private String maxPollRecords;

    @Value("${galaxy.kafka.consumer.max.poll.interval:15000}")
    private String maxPollInterval;

    @Value("${galaxy.kafka.consumer.max.partition-fetch-bytes:15728640}")
    private String maxPartitionFetchBytes;

    @Value("${galaxy.kafka.consumer.is.ack:false}")
    private boolean isAck;

    private final KafkaLogProperties kafkaLogProperties;

    IGalaxyLogger galaxyLogger = GalaxyLoggerFactory.getLogger(GalaxyKafkaAutoConfiguration.class);

    public KafkaConsumerConfig(KafkaLogProperties kafkaLogProperties) {
        this.kafkaLogProperties = kafkaLogProperties;
    }

    @Bean
    @ConditionalOnMissingBean
    public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, String>> kafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory());
        factory.setConcurrency(concurrency);
        factory.setBatchListener(batchListner);
        factory.getContainerProperties().setPollTimeout(pollTimeout);
        factory.getContainerProperties().setObservationEnabled(true);
        if (isAck) {
            factory.getContainerProperties().setAckMode(AckMode.MANUAL_IMMEDIATE);
        }

        galaxyLogger.info("GalaxyBoot: Initializing ConcurrentKafkaListenerContainerFactory with Config: {}", factory.getContainerProperties());
        return factory;
    }

    @Bean
    @ConditionalOnMissingBean
    public ConsumerFactory<String, String> consumerFactory() {
        Map<String, Object> configs = consumerConfigs();
        galaxyLogger.info("GalaxyBoot: Initializing ConsumerFactory with Config: {}", configs);
        return new DefaultKafkaConsumerFactory<>(configs);
    }

    @Bean
    @ConditionalOnMissingBean(name = "consumerConfigs")
    public Map<String, Object> consumerConfigs() {
        HashMap<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaNodes);
        props.put(ConsumerConfig.GROUP_ID_CONFIG, groupId);
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, enableAutoCommit);
        props.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, autoCommitIntervalMs);
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, autoOffsetReset);
        props.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, sessionTimeoutMs);
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, maxPollRecords);
        props.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, maxPollInterval);
        props.put(ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG, maxPartitionFetchBytes);

        // 添加拦截器配置
        if (kafkaLogProperties != null && kafkaLogProperties.isEnabled()) {
            props.put(ConsumerConfig.INTERCEPTOR_CLASSES_CONFIG,
                    GalaxyKafkaConsumerInterceptor.class.getName());
        }

        jaas(props);
        return props;
    }
}
