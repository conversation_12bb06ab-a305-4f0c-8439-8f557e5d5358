package cn.com.chinastock.cnf.kafka.config;

import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.AutoConfigurations;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.security.jaas.KafkaJaasLoginModuleInitializer;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * GalaxyKafkaAutoConfiguration测试类
 */
class GalaxyKafkaAutoConfigurationTest {

    private final ApplicationContextRunner contextRunner = new ApplicationContextRunner()
            .withConfiguration(AutoConfigurations.of(GalaxyKafkaAutoConfiguration.class));

    @Test
    void testAutoConfigurationDisabledByDefault() {
        contextRunner.run(context -> {
            assertThat(context).doesNotHaveBean(KafkaTemplate.class);
            assertThat(context).doesNotHaveBean(ProducerFactory.class);
            assertThat(context).doesNotHaveBean(ConsumerFactory.class);
            assertThat(context).doesNotHaveBean(KafkaListenerContainerFactory.class);
        });
    }

    @Test
    void shouldCreateKafkaBeansWhenKafkaIsEnabled() {
        contextRunner
                .withPropertyValues("galaxy.kafka.enable=true")
                .run(context -> {
                    // 验证核心Bean被创建
                    assertThat(context).hasBean("kafkaTemplate");
                    assertThat(context).hasBean("kafkaTemplateForString");

                    assertThat(context).hasSingleBean(KafkaLogProperties.class);
                    // 验证至少有一个ProducerFactory被创建
                    assertThat(context.getBeansOfType(ProducerFactory.class)).isNotEmpty();
                    assertThat(context).hasSingleBean(ConsumerFactory.class);
                    assertThat(context).hasSingleBean(KafkaListenerContainerFactory.class);

                    // 验证KafkaTemplate类型的Bean数量
                    assertThat(context.getBeansOfType(KafkaTemplate.class)).isNotEmpty();
                });
    }

    @Test
    void testJaasConfigurationEnabled() {
        contextRunner
                .withPropertyValues(
                        "galaxy.kafka.enable=true",
                        "galaxy.kafka.jaas.enable=true"
                )
                .run(context -> assertThat(context).hasSingleBean(KafkaJaasLoginModuleInitializer.class));
    }

    @Test
    void testJaasConfigurationDisabled() {
        contextRunner
                .withPropertyValues(
                        "galaxy.kafka.enable=true",
                        "galaxy.kafka.jaas.enable=false"
                )
                .run(context -> assertThat(context).doesNotHaveBean(KafkaJaasLoginModuleInitializer.class));
    }

    @Test
    void testLogPropertiesConfiguration() {
        contextRunner
                .withPropertyValues(
                        "galaxy.kafka.enable=true",
                        "galaxy.kafka.log.enabled=false",
                        "galaxy.kafka.log.max-detail-records-count=10"
                )
                .run(context -> {
                    assertThat(context).hasSingleBean(KafkaLogProperties.class);
                    KafkaLogProperties properties = context.getBean(KafkaLogProperties.class);
                    assertThat(properties.isEnabled()).isFalse();
                    assertThat(properties.getMaxDetailRecordsCount()).isEqualTo(10);
                });
    }

    @Test
    void shouldOverrideKafkaTemplateWhenCustomBeanIsProvided() {
        // 创建一个简单的测试，验证自定义Bean可以覆盖默认Bean
        contextRunner
                .withPropertyValues("galaxy.kafka.enable=true")
                .withBean("kafkaTemplate", KafkaTemplate.class, () -> {
                    // 创建一个简单的模拟ProducerFactory用于测试
                    ProducerFactory<Object, Object> mockProducerFactory =
                            new org.springframework.kafka.core.DefaultKafkaProducerFactory<>(
                                    java.util.Map.of("bootstrap.servers", "localhost:9092")
                            );
                    return new KafkaTemplate<>(mockProducerFactory);
                })
                .run(context -> {
                    // 验证自定义的kafkaTemplate存在
                    assertThat(context).hasBean("kafkaTemplate");
                    assertThat(context).hasBean("kafkaTemplateForString");

                    // 验证使用的是自定义的Bean
                    assertThat(context.getBean("kafkaTemplate")).isNotNull();

                    // 验证KafkaTemplate类型的Bean存在
                    assertThat(context.getBeansOfType(KafkaTemplate.class)).isNotEmpty();
                });
    }
}
