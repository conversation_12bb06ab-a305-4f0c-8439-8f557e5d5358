package cn.com.chinastock.cnf.kafka.config;

import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.AutoConfigurations;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;
import org.springframework.context.annotation.Configuration;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Kafka配置集成测试
 * 通过启动Spring上下文来验证Bean配置是否正确以及配置项实际生效
 * 使用IntegrationTest方法验证配置实际生效的逻辑
 * <p>
 * 注意：由于Kafka版本兼容性问题，此测试专注于配置属性和基础Bean的验证，
 * 不测试需要完整Kafka客户端的功能
 */
class KafkaConfigurationIntegrationTest {

    @Configuration
    @EnableConfigurationProperties(KafkaLogProperties.class)
    static class TestConfiguration {
        // 测试配置类，用于启用配置属性绑定
    }

    // 创建一个简化的上下文运行器，专注于配置属性测试
    private final ApplicationContextRunner contextRunner = new ApplicationContextRunner()
            .withUserConfiguration(TestConfiguration.class);

    @Test
    void shouldNotCreateBeansWhenNoConfigurationIsProvided() {
        new ApplicationContextRunner().run(context -> {
            // 当没有任何配置时，不应该创建任何Bean
            assertThat(context).doesNotHaveBean(KafkaLogProperties.class);
            assertThat(context).doesNotHaveBean(DynamicKafkaLogManager.class);
            assertThat(context).doesNotHaveBean(KafkaLogConfigChangeListener.class);
        });
    }

    @Test
    void shouldConfigureLogPropertiesWhenCustomValuesAreProvided() {
        contextRunner
                .withPropertyValues(
                        "galaxy.kafka.log.enabled=false",
                        "galaxy.kafka.log.max-detail-records-count=15"
                )
                .run(context -> {
                    assertThat(context).hasSingleBean(KafkaLogProperties.class);

                    KafkaLogProperties properties = context.getBean(KafkaLogProperties.class);
                    assertThat(properties.isEnabled()).isFalse();
                    assertThat(properties.getMaxDetailRecordsCount()).isEqualTo(15);
                });
    }

    @Test
    void shouldConfigureLogPropertiesWithDefaultValues() {
        contextRunner
                .run(context -> {
                    assertThat(context).hasSingleBean(KafkaLogProperties.class);

                    KafkaLogProperties properties = context.getBean(KafkaLogProperties.class);
                    assertThat(properties.isEnabled()).isTrue(); // 默认值
                    assertThat(properties.getMaxDetailRecordsCount()).isEqualTo(KafkaLogProperties.DEFAULT_MAX_DETAIL_RECORDS_COUNT); // 默认值
                });
    }

    @Test
    void shouldSyncDynamicManagerWithPropertiesWhenContextStarts() {
        contextRunner
                .withPropertyValues(
                        "galaxy.kafka.log.enabled=false",
                        "galaxy.kafka.log.max-detail-records-count=25"
                )
                .withConfiguration(AutoConfigurations.of(DynamicKafkaLogManager.class))
                .run(context -> {
                    assertThat(context).hasSingleBean(DynamicKafkaLogManager.class);
                    assertThat(context).hasSingleBean(KafkaLogProperties.class);

                    DynamicKafkaLogManager dynamicManager = context.getBean(DynamicKafkaLogManager.class);
                    KafkaLogProperties properties = context.getBean(KafkaLogProperties.class);

                    // 手动同步配置（模拟启动时的同步）
                    dynamicManager.syncFromProperties();

                    // 验证动态管理器的状态与配置属性一致
                    assertThat(DynamicKafkaLogManager.isLogDisabled()).isEqualTo(!properties.isEnabled());
                    assertThat(DynamicKafkaLogManager.getMaxDetailRecordsCount()).isEqualTo(properties.getMaxDetailRecordsCount());
                });
    }

    @Test
    void shouldConfigureDynamicKafkaLogManagerWithDefaultValues() {
        contextRunner
                .withConfiguration(AutoConfigurations.of(DynamicKafkaLogManager.class))
                .run(context -> {
                    assertThat(context).hasSingleBean(DynamicKafkaLogManager.class);
                    assertThat(context).hasSingleBean(KafkaLogProperties.class);

                    DynamicKafkaLogManager dynamicManager = context.getBean(DynamicKafkaLogManager.class);

                    // 手动同步配置
                    dynamicManager.syncFromProperties();

                    // 验证默认值
                    assertThat(DynamicKafkaLogManager.isLogDisabled()).isFalse();
                    assertThat(DynamicKafkaLogManager.getMaxDetailRecordsCount()).isEqualTo(KafkaLogProperties.DEFAULT_MAX_DETAIL_RECORDS_COUNT);

                    // 测试动态更新功能
                    dynamicManager.updateLogEnabled(false);
                    dynamicManager.updateMaxDetailRecordsCount(30);

                    assertThat(DynamicKafkaLogManager.isLogDisabled()).isTrue();
                    assertThat(DynamicKafkaLogManager.getMaxDetailRecordsCount()).isEqualTo(30);

                    // 验证配置状态字符串
                    String configStatus = dynamicManager.getConfigStatus();
                    assertThat(configStatus).contains("enabled=false");
                    assertThat(configStatus).contains("maxDetailRecords=30");
                });
    }

    @Test
    void shouldConfigureKafkaLogConfigChangeListenerWhenApolloIsAvailable() {
        // 注意：KafkaLogConfigChangeListener需要Apollo依赖
        // 这个测试验证在有Apollo的情况下会创建该Bean
        // galaxy-boot-core引用了Apollo，所以测试环境中Apollo类是存在的
        contextRunner
                .withConfiguration(AutoConfigurations.of(
                        DynamicKafkaLogManager.class,
                        KafkaLogConfigChangeListener.class
                ))
                .run(context -> {
                    assertThat(context).hasSingleBean(DynamicKafkaLogManager.class);

                    // 由于@ConditionalOnClass(name = "com.ctrip.framework.apollo.ConfigChangeListener")
                    // 在测试环境中Apollo类存在，所以KafkaLogConfigChangeListener会被创建
                    assertThat(context).hasSingleBean(KafkaLogConfigChangeListener.class);
                });
    }

    @Test
    void shouldTestDynamicKafkaLogManagerDirectly() {
        contextRunner
                .withConfiguration(AutoConfigurations.of(DynamicKafkaLogManager.class))
                .run(context -> {
                    assertThat(context).hasSingleBean(DynamicKafkaLogManager.class);

                    DynamicKafkaLogManager dynamicManager = context.getBean(DynamicKafkaLogManager.class);

                    // 测试动态设置功能（不依赖KafkaLogConfigChangeListener）
                    dynamicManager.updateLogEnabled(false);
                    assertThat(DynamicKafkaLogManager.isLogDisabled()).isTrue();

                    dynamicManager.updateMaxDetailRecordsCount(50);
                    assertThat(DynamicKafkaLogManager.getMaxDetailRecordsCount()).isEqualTo(50);

                    // 验证配置状态字符串
                    String configStatus = dynamicManager.getConfigStatus();
                    assertThat(configStatus).contains("enabled=false");
                    assertThat(configStatus).contains("maxDetailRecords=50");
                });
    }

    @Test
    void shouldHandlePartiallyInvalidConfigurationValues() {
        // 测试只有一个无效值的情况
        contextRunner
                .withPropertyValues(
                        "galaxy.kafka.log.enabled=true",
                        "galaxy.kafka.log.max-detail-records-count=invalid"
                )
                .run(context -> {
                    // 验证上下文启动失败（因为int值无效）
                    assertThat(context).hasFailed();

                    // 验证失败原因包含类型转换错误信息
                    Throwable failure = context.getStartupFailure();
                    assertThat(failure.getMessage())
                            .satisfiesAnyOf(
                                    msg -> assertThat(msg).contains("Failed to convert value"),
                                    msg -> assertThat(msg).contains("For input string: \"invalid\""),
                                    msg -> assertThat(msg).contains("max-detail-records-count")
                            );
                });
    }
}
