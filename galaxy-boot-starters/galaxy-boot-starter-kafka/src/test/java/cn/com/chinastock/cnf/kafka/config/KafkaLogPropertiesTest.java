package cn.com.chinastock.cnf.kafka.config;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * KafkaLogProperties测试类
 */
class KafkaLogPropertiesTest {

    @Test
    void shouldReturnTrueWhenDefaultValueIsUsed() {
        KafkaLogProperties properties = new KafkaLogProperties();
        assertTrue(properties.isEnabled()); // 默认值是true
    }

    @Test
    void shouldReturnTrueWhenEnabledIsSetToTrue() {
        KafkaLogProperties properties = new KafkaLogProperties();
        properties.setEnabled(true);
        assertTrue(properties.isEnabled());
    }

    @Test
    void shouldReturnFalseWhenEnabledIsSetToFalse() {
        KafkaLogProperties properties = new KafkaLogProperties();
        properties.setEnabled(false);
        assertFalse(properties.isEnabled());
    }

    @Test
    void shouldReturnCorrectValueWhenMaxDetailRecordsCountIsSet() {
        KafkaLogProperties properties = new KafkaLogProperties();
        assertEquals(KafkaLogProperties.DEFAULT_MAX_DETAIL_RECORDS_COUNT, properties.getMaxDetailRecordsCount()); // 默认值是5

        properties.setMaxDetailRecordsCount(10);
        assertEquals(10, properties.getMaxDetailRecordsCount());
    }
}
