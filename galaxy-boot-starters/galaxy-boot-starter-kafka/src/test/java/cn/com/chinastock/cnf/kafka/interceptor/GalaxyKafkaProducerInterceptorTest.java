package cn.com.chinastock.cnf.kafka.interceptor;

import cn.com.chinastock.cnf.core.log.context.ITraceContext;
import cn.com.chinastock.cnf.kafka.config.DynamicKafkaLogManager;
import cn.com.chinastock.cnf.kafka.config.KafkaLogProperties;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.header.Header;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.MDC;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Galaxy Kafka Producer拦截器测试类
 * 测试Producer拦截器的各种功能和配置场景
 */
@ExtendWith(MockitoExtension.class)
class GalaxyKafkaProducerInterceptorTest {

    private GalaxyKafkaProducerInterceptor<String, String> producerInterceptor;
    private DynamicKafkaLogManager dynamicKafkaLogManager;

    @Mock
    private KafkaLogProperties kafkaLogProperties;

    @Mock
    private ITraceContext mockTraceContext;

    @BeforeEach
    void setUp() {
        // 清理 MDC
        MDC.clear();

        // 创建动态配置管理器实例
        dynamicKafkaLogManager = new DynamicKafkaLogManager(kafkaLogProperties);

        // 重置动态配置管理器状态
        dynamicKafkaLogManager.updateLogEnabled(true);
        dynamicKafkaLogManager.updateMaxDetailRecordsCount(5);

        // 注意：由于删除了 KafkaTraceContextInjector，这里不再设置全局 TraceContext

        producerInterceptor = new GalaxyKafkaProducerInterceptor<>();
        Map<String, Object> producerConfigs = new HashMap<>();
        producerConfigs.put("galaxy.kafka.log.enabled", true);
        producerConfigs.put("galaxy.trace.context", mockTraceContext);
        producerInterceptor.configure(producerConfigs);
    }

    @Test
    void shouldReturnModifiedRecordWhenInterceptorIsEnabled() {
        // 测试拦截器启用时的行为
        ProducerRecord<String, String> record = new ProducerRecord<>("test-topic", "test-key", "test-value");
        ProducerRecord<String, String> result = producerInterceptor.onSend(record);

        assertNotNull(result);
        assertEquals("test-topic", result.topic());
        assertEquals("test-key", result.key());
        assertEquals("test-value", result.value());
        assertNotSame(record, result); // 应该返回新的记录（添加了 headers）

        // 检查是否添加了幂等性 ID header
        Header idempotencyHeader = result.headers().lastHeader("idempotent_id");
        assertNotNull(idempotencyHeader);

        String idempotencyId = new String(idempotencyHeader.value(), StandardCharsets.UTF_8);
        // 验证是 UUID 格式
        assertNotNull(UUID.fromString(idempotencyId));
    }

    @Test
    void shouldReturnModifiedRecordWhenInterceptorIsDisabled() {
        // 创建禁用日志的拦截器
        GalaxyKafkaProducerInterceptor<String, String> disabledInterceptor =
                new GalaxyKafkaProducerInterceptor<>();
        Map<String, Object> disabledConfigs = new HashMap<>();
        disabledConfigs.put("galaxy.kafka.log.enabled", false);
        disabledInterceptor.configure(disabledConfigs);

        // 测试拦截器禁用时的行为
        ProducerRecord<String, String> record = new ProducerRecord<>("test-topic", "test-key", "test-value");
        ProducerRecord<String, String> result = disabledInterceptor.onSend(record);

        assertNotNull(result);
        assertEquals("test-topic", result.topic());
        assertEquals("test-key", result.key());
        assertEquals("test-value", result.value());
        assertNotSame(record, result); // 应该返回新的记录（添加了幂等性 ID）

        // 检查是否添加了幂等性 ID header
        Header idempotencyHeader = result.headers().lastHeader("idempotent_id");
        assertNotNull(idempotencyHeader);

        String idempotencyId = new String(idempotencyHeader.value(), StandardCharsets.UTF_8);
        // 验证是 UUID 格式
        assertNotNull(UUID.fromString(idempotencyId));
    }

    @Test
    void shouldHandleNullRecordGracefully() {
        // 测试处理null记录的情况 - 拦截器会记录异常但返回null
        ProducerRecord<String, String> result = producerInterceptor.onSend(null);
        assertNull(result);
    }

    @Test
    void shouldHandleRecordWithNullValues() {
        // 测试处理包含null值的记录
        ProducerRecord<String, String> recordWithNullKey = new ProducerRecord<>("test-topic", null, "test-value");
        ProducerRecord<String, String> result1 = producerInterceptor.onSend(recordWithNullKey);
        assertNotNull(result1);
        assertNull(result1.key());
        assertEquals("test-value", result1.value());

        ProducerRecord<String, String> recordWithNullValue = new ProducerRecord<>("test-topic", "test-key", null);
        ProducerRecord<String, String> result2 = producerInterceptor.onSend(recordWithNullValue);
        assertNotNull(result2);
        assertEquals("test-key", result2.key());
        assertNull(result2.value());
    }

    @Test
    void shouldNotThrowExceptionWhenOnAcknowledgementWithSuccess() {
        TopicPartition topicPartition = new TopicPartition("test-topic", 0);
        RecordMetadata metadata = new RecordMetadata(topicPartition, 0L, 0, 0L, 0, 0);

        // 测试成功确认不抛出异常
        assertDoesNotThrow(() -> producerInterceptor.onAcknowledgement(metadata, null));
    }

    @Test
    void shouldNotThrowExceptionWhenOnAcknowledgementWithFailure() {
        // 测试失败确认不抛出异常
        Exception exception = new RuntimeException("Test exception");
        assertDoesNotThrow(() -> producerInterceptor.onAcknowledgement(null, exception));
    }

    @Test
    void shouldNotThrowExceptionWhenOnAcknowledgementWithNullMetadata() {
        // 测试null metadata的情况
        assertDoesNotThrow(() -> producerInterceptor.onAcknowledgement(null, null));
    }

    @Test
    void shouldConfigureCorrectlyWhenConfigurationIsProvided() {
        GalaxyKafkaProducerInterceptor<String, String> interceptor = new GalaxyKafkaProducerInterceptor<>();

        // 测试配置启用
        Map<String, Object> enabledConfigs = new HashMap<>();
        enabledConfigs.put("galaxy.kafka.log.enabled", "true");
        assertDoesNotThrow(() -> interceptor.configure(enabledConfigs));

        ProducerRecord<String, String> record = new ProducerRecord<>("test-topic", "test-key", "test-value");
        ProducerRecord<String, String> result = interceptor.onSend(record);
        assertNotSame(record, result); // 应该返回新的记录（添加了幂等性 ID）

        // 检查是否添加了幂等性 ID header
        Header idempotencyHeader = result.headers().lastHeader("idempotent_id");
        assertNotNull(idempotencyHeader);
    }

    @Test
    void shouldHandleEmptyConfiguration() {
        GalaxyKafkaProducerInterceptor<String, String> interceptor = new GalaxyKafkaProducerInterceptor<>();

        // 测试空配置
        Map<String, Object> emptyConfigs = new HashMap<>();
        assertDoesNotThrow(() -> interceptor.configure(emptyConfigs));

        ProducerRecord<String, String> record = new ProducerRecord<>("test-topic", "test-key", "test-value");
        ProducerRecord<String, String> result = interceptor.onSend(record);
        assertNotSame(record, result); // 应该返回新的记录（添加了幂等性 ID）

        // 检查是否添加了幂等性 ID header
        Header idempotencyHeader = result.headers().lastHeader("idempotent_id");
        assertNotNull(idempotencyHeader);
    }

    @Test
    void shouldHandleNullConfiguration() {
        GalaxyKafkaProducerInterceptor<String, String> interceptor = new GalaxyKafkaProducerInterceptor<>();

        // 测试null配置 - 拦截器能够处理null配置
        assertDoesNotThrow(() -> interceptor.configure(null));

        ProducerRecord<String, String> record = new ProducerRecord<>("test-topic", "test-key", "test-value");
        ProducerRecord<String, String> result = interceptor.onSend(record);
        assertNotSame(record, result); // 应该返回新的记录（添加了幂等性 ID）

        // 检查是否添加了幂等性 ID header
        Header idempotencyHeader = result.headers().lastHeader("idempotent_id");
        assertNotNull(idempotencyHeader);
    }

    @Test
    void shouldNotThrowExceptionWhenClose() {
        // 测试关闭方法不抛出异常
        assertDoesNotThrow(() -> producerInterceptor.close());
        
        // 测试关闭后仍能正常工作
        ProducerRecord<String, String> record = new ProducerRecord<>("test-topic", "test-key", "test-value");
        assertDoesNotThrow(() -> producerInterceptor.onSend(record));
    }

    @Test
    void shouldRespectDynamicConfigurationChanges() {
        // 测试动态配置变更的响应
        ProducerRecord<String, String> record = new ProducerRecord<>("test-topic", "test-key", "test-value");

        // 启用日志
        dynamicKafkaLogManager.updateLogEnabled(true);
        ProducerRecord<String, String> result1 = producerInterceptor.onSend(record);
        assertNotSame(record, result1); // 应该返回新的记录（添加了幂等性 ID）

        // 禁用日志
        dynamicKafkaLogManager.updateLogEnabled(false);
        ProducerRecord<String, String> result2 = producerInterceptor.onSend(record);
        assertNotSame(record, result2); // 仍然应该返回新的记录（添加了幂等性 ID）
    }

    @Test
    void shouldHandleComplexRecordStructures() {
        // 测试复杂的记录结构
        ProducerRecord<String, String> recordWithHeaders = new ProducerRecord<>(
                "test-topic", 
                0, // partition
                System.currentTimeMillis(), // timestamp
                "test-key", 
                "test-value"
        );
        
        ProducerRecord<String, String> result = producerInterceptor.onSend(recordWithHeaders);
        assertNotNull(result);
        assertEquals("test-topic", result.topic());
        assertEquals(Integer.valueOf(0), result.partition());
        assertEquals("test-key", result.key());
        assertEquals("test-value", result.value());
        assertNotNull(result.timestamp());
    }

    @Test
    void shouldAddTraceparentHeaderFromMDC() {
        // Given
        MDC.put("traceId", "test-trace-id");
        MDC.put("spanId", "test-span-id");

        ProducerRecord<String, String> originalRecord = new ProducerRecord<>(
            "test-topic", "test-key", "test-value"
        );

        // When
        ProducerRecord<String, String> result = producerInterceptor.onSend(originalRecord);

        // Then
        Header traceparentHeader = result.headers().lastHeader("traceparent");
        assertNotNull(traceparentHeader);

        String traceparent = new String(traceparentHeader.value(), StandardCharsets.UTF_8);
        assertEquals("00-test-trace-id-test-span-id-01", traceparent);

        // 清理 MDC
        MDC.clear();
    }

    @Test
    void shouldAddIdempotencyIdHeaderWhenOnSend() {
        // Given
        ProducerRecord<String, String> originalRecord = new ProducerRecord<>(
            "test-topic", "test-key", "test-value"
        );

        // When
        ProducerRecord<String, String> result = producerInterceptor.onSend(originalRecord);

        // Then
        assertNotNull(result);

        // 检查是否添加了幂等性 ID header
        Header idempotencyHeader = result.headers().lastHeader("idempotent_id");
        assertNotNull(idempotencyHeader);

        String idempotencyId = new String(idempotencyHeader.value(), StandardCharsets.UTF_8);
        // 验证是 UUID 格式
        assertNotNull(UUID.fromString(idempotencyId));
    }

    @Test
    void shouldAddTraceHeadersFromMDC() {
        // Given
        MDC.put("traceId", "test-trace-id");
        MDC.put("spanId", "test-span-id");

        ProducerRecord<String, String> originalRecord = new ProducerRecord<>(
            "test-topic", "test-key", "test-value"
        );

        // When
        ProducerRecord<String, String> result = producerInterceptor.onSend(originalRecord);

        // Then
        Header traceparentHeader = result.headers().lastHeader("traceparent");
        assertNotNull(traceparentHeader);

        String traceparent = new String(traceparentHeader.value(), StandardCharsets.UTF_8);
        assertEquals("00-test-trace-id-test-span-id-01", traceparent);

        // 清理 MDC
        MDC.clear();
    }

    @Test
    void shouldAddTraceHeadersFromMDCOnly() {
        // Given - 设置 MDC 中的 trace 信息
        MDC.put("traceId", "mdc-trace-id");
        MDC.put("spanId", "mdc-span-id");

        ProducerRecord<String, String> originalRecord = new ProducerRecord<>(
            "test-topic", "test-key", "test-value"
        );

        // When
        ProducerRecord<String, String> result = producerInterceptor.onSend(originalRecord);

        // Then
        Header traceparentHeader = result.headers().lastHeader("traceparent");
        assertNotNull(traceparentHeader);

        String traceparent = new String(traceparentHeader.value(), StandardCharsets.UTF_8);
        assertEquals("00-mdc-trace-id-mdc-span-id-01", traceparent);

        // 清理 MDC
        MDC.clear();
    }

    @Test
    void shouldHandleTraceInfoInAcknowledgement() {
        // Given
        MDC.put("traceId", "test-trace-id");
        MDC.put("spanId", "test-span-id");
        MDC.put("parentSpanId", "test-parent-span-id");

        ProducerRecord<String, String> record = new ProducerRecord<>(
            "test-topic", "test-key", "test-value"
        );

        // 先调用 onSend 保存 trace 信息
        ProducerRecord<String, String> sentRecord = producerInterceptor.onSend(record);

        // 清理 MDC 模拟异步回调场景
        MDC.clear();

        // 创建 RecordMetadata
        RecordMetadata metadata = new RecordMetadata(
            new TopicPartition("test-topic", 0), 0, 0, 0, 0L, 0, 0
        );

        // When & Then - 验证方法不会抛出异常
        assertDoesNotThrow(() -> producerInterceptor.onAcknowledgement(metadata, null));
    }

    @Test
    void shouldCleanupTraceInfoOnClose() {
        // Given
        ProducerRecord<String, String> record = new ProducerRecord<>(
            "test-topic", "test-key", "test-value"
        );
        producerInterceptor.onSend(record);

        // When & Then
        assertDoesNotThrow(() -> producerInterceptor.close());
    }

    @Test
    void shouldNotDuplicateTraceparentHeader() {
        // Given - 设置 MDC 中的 trace 信息
        MDC.put("traceId", "4bf92f3577b34da6a3ce929d0e0e4736");
        MDC.put("spanId", "00f067aa0ba902b7");

        // 创建已经包含 traceparent header 的记录
        ProducerRecord<String, String> record = new ProducerRecord<>(
            "test-topic", "test-key", "test-value"
        );

        // 手动添加一个 traceparent header
        record.headers().add("traceparent", "00-old-trace-id-old-span-id-01".getBytes(StandardCharsets.UTF_8));

        // When - 调用 onSend
        ProducerRecord<String, String> result = producerInterceptor.onSend(record);

        // Then - 验证只有一个 traceparent header，且是新的值
        assertNotNull(result);

        // 获取所有 traceparent headers
        Iterable<Header> traceparentHeaders = result.headers().headers("traceparent");
        int headerCount = 0;
        String lastTraceparentValue = null;

        for (Header header : traceparentHeaders) {
            headerCount++;
            lastTraceparentValue = new String(header.value(), StandardCharsets.UTF_8);
        }

        // 验证只有一个 traceparent header
        assertEquals(1, headerCount, "Should have exactly one traceparent header");

        // 验证 traceparent 的值是新生成的，不是旧的
        assertNotNull(lastTraceparentValue);
        assertNotEquals("00-old-trace-id-old-span-id-01", lastTraceparentValue);
        assertTrue(lastTraceparentValue.startsWith("00-4bf92f3577b34da6a3ce929d0e0e4736-00f067aa0ba902b7-01"));
    }

    @Test
    void shouldReplaceExistingTraceparentHeader() {
        // Given - 设置 MDC 中的 trace 信息
        MDC.put("traceId", "new-trace-id-123456789abcdef0");
        MDC.put("spanId", "new-span-id-12345");

        // 创建已经包含多个 traceparent headers 的记录
        ProducerRecord<String, String> record = new ProducerRecord<>(
            "test-topic", "test-key", "test-value"
        );

        // 添加多个 traceparent headers 模拟重复问题
        record.headers().add("traceparent", "00-first-trace-id-first-span-id-01".getBytes(StandardCharsets.UTF_8));
        record.headers().add("traceparent", "00-second-trace-id-second-span-id-01".getBytes(StandardCharsets.UTF_8));
        record.headers().add("other-header", "other-value".getBytes(StandardCharsets.UTF_8));

        // When - 调用 onSend
        ProducerRecord<String, String> result = producerInterceptor.onSend(record);

        // Then - 验证所有旧的 traceparent headers 被移除，只有一个新的
        assertNotNull(result);

        // 验证 traceparent headers
        Iterable<Header> traceparentHeaders = result.headers().headers("traceparent");
        int traceparentCount = 0;
        String traceparentValue = null;

        for (Header header : traceparentHeaders) {
            traceparentCount++;
            traceparentValue = new String(header.value(), StandardCharsets.UTF_8);
        }

        assertEquals(1, traceparentCount, "Should have exactly one traceparent header after replacement");
        assertNotNull(traceparentValue);
        assertTrue(traceparentValue.startsWith("00-new-trace-id-123456789abcdef0-new-span-id-12345-01"));

        // 验证其他 headers 保持不变
        Header otherHeader = result.headers().lastHeader("other-header");
        assertNotNull(otherHeader);
        assertEquals("other-value", new String(otherHeader.value(), StandardCharsets.UTF_8));
    }

    @Test
    void shouldPreserveOtherHeadersWhenReplacingTraceparent() {
        // Given - 设置 MDC 中的 trace 信息
        MDC.put("traceId", "test-trace-id-abcdef0123456789");
        MDC.put("spanId", "test-span-id-123");

        // 创建包含多种 headers 的记录
        ProducerRecord<String, String> record = new ProducerRecord<>(
            "test-topic", "test-key", "test-value"
        );

        // 添加各种 headers
        record.headers().add("traceparent", "00-old-trace-old-span-01".getBytes(StandardCharsets.UTF_8));
        record.headers().add("idempotent_id", "existing-id".getBytes(StandardCharsets.UTF_8));
        record.headers().add("custom-header", "custom-value".getBytes(StandardCharsets.UTF_8));
        record.headers().add("binary-header", new byte[]{0x01, 0x02, 0x03});

        // When - 调用 onSend
        ProducerRecord<String, String> result = producerInterceptor.onSend(record);

        // Then - 验证 traceparent 被替换，其他 headers 保持不变
        assertNotNull(result);

        // 验证 traceparent 被正确替换
        Header traceparentHeader = result.headers().lastHeader("traceparent");
        assertNotNull(traceparentHeader);
        String traceparentValue = new String(traceparentHeader.value(), StandardCharsets.UTF_8);
        assertTrue(traceparentValue.startsWith("00-test-trace-id-abcdef0123456789-test-span-id-123-01"));

        // 验证新的 idempotent_id 被添加（应该有两个）
        Iterable<Header> idempotentHeaders = result.headers().headers("idempotent_id");
        int idempotentCount = 0;
        for (Header header : idempotentHeaders) {
            idempotentCount++;
        }
        assertEquals(2, idempotentCount, "Should have original and new idempotent_id headers");

        // 验证其他 headers 保持不变
        Header customHeader = result.headers().lastHeader("custom-header");
        assertNotNull(customHeader);
        assertEquals("custom-value", new String(customHeader.value(), StandardCharsets.UTF_8));

        Header binaryHeader = result.headers().lastHeader("binary-header");
        assertNotNull(binaryHeader);
        assertArrayEquals(new byte[]{0x01, 0x02, 0x03}, binaryHeader.value());
    }
}
