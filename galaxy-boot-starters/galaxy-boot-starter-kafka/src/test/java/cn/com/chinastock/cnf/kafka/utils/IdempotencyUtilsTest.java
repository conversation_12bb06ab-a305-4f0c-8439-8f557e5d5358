package cn.com.chinastock.cnf.kafka.utils;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.internals.RecordHeaders;
import org.apache.kafka.common.record.TimestampType;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.MDC;

import java.nio.charset.StandardCharsets;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * IdempotencyUtils 测试类
 * 
 * <AUTHOR>
 */
class IdempotencyUtilsTest {

    @BeforeEach
    void setUp() {
        // 清理 MDC
        MDC.clear();
    }

    @AfterEach
    void tearDown() {
        // 清理 MDC
        MDC.clear();
    }

    @Test
    void shouldExtractIdempotencyIdFromRecord() {
        // Given
        String expectedId = "galaxy_idem_123_456789";
        RecordHeaders headers = new RecordHeaders();
        headers.add("idempotent_id", expectedId.getBytes(StandardCharsets.UTF_8));
        
        ConsumerRecord<String, String> record = new ConsumerRecord<>(
            "test-topic", 0, 0, 0L, TimestampType.CREATE_TIME, 0L, 0, 0, "key", "value", headers, null
        );

        // When
        Optional<String> result = IdempotencyUtils.extractIdempotencyId(record);

        // Then
        assertTrue(result.isPresent());
        assertEquals(expectedId, result.get());
    }

    @Test
    void shouldReturnEmptyWhenNoIdempotencyIdInRecord() {
        // Given
        ConsumerRecord<String, String> record = new ConsumerRecord<>(
            "test-topic", 0, 0, 0L, TimestampType.CREATE_TIME, 0L, 0, 0, "key", "value", new RecordHeaders(), null
        );

        // When
        Optional<String> result = IdempotencyUtils.extractIdempotencyId(record);

        // Then
        assertFalse(result.isPresent());
    }

    @Test
    void shouldReturnEmptyWhenRecordIsNull() {
        // When
        Optional<String> result = IdempotencyUtils.extractIdempotencyId(null);

        // Then
        assertFalse(result.isPresent());
    }

    @Test
    void shouldSetAndGetIdempotencyIdFromMDC() {
        // Given
        String expectedId = "test-idempotency-id";

        // When
        IdempotencyUtils.setIdempotencyId(expectedId);
        Optional<String> result = IdempotencyUtils.getCurrentIdempotencyId();

        // Then
        assertTrue(result.isPresent());
        assertEquals(expectedId, result.get());
    }

    @Test
    void shouldReturnEmptyWhenNoIdempotencyIdInMDC() {
        // When
        Optional<String> result = IdempotencyUtils.getCurrentIdempotencyId();

        // Then
        assertFalse(result.isPresent());
    }

    @Test
    void shouldClearIdempotencyIdFromMDC() {
        // Given
        String idempotencyId = "test-idempotency-id";
        IdempotencyUtils.setIdempotencyId(idempotencyId);
        
        // Verify it's set
        assertTrue(IdempotencyUtils.getCurrentIdempotencyId().isPresent());

        // When
        IdempotencyUtils.clearIdempotencyId();

        // Then
        assertFalse(IdempotencyUtils.getCurrentIdempotencyId().isPresent());
    }

    @Test
    void shouldNotSetEmptyOrNullIdempotencyId() {
        // When & Then
        IdempotencyUtils.setIdempotencyId(null);
        assertFalse(IdempotencyUtils.getCurrentIdempotencyId().isPresent());

        IdempotencyUtils.setIdempotencyId("");
        assertFalse(IdempotencyUtils.getCurrentIdempotencyId().isPresent());

        IdempotencyUtils.setIdempotencyId("   ");
        assertFalse(IdempotencyUtils.getCurrentIdempotencyId().isPresent());
    }

    @Test
    void shouldCheckIfRecordHasIdempotencyId() {
        // Given
        RecordHeaders headersWithId = new RecordHeaders();
        headersWithId.add("idempotent_id", "test-id".getBytes(StandardCharsets.UTF_8));
        
        ConsumerRecord<String, String> recordWithId = new ConsumerRecord<>(
            "test-topic", 0, 0, 0L, TimestampType.CREATE_TIME, 0L, 0, 0, "key", "value", headersWithId, null
        );

        ConsumerRecord<String, String> recordWithoutId = new ConsumerRecord<>(
            "test-topic", 0, 0, 0L, TimestampType.CREATE_TIME, 0L, 0, 0, "key", "value", new RecordHeaders(), null
        );

        // When & Then
        assertTrue(IdempotencyUtils.hasIdempotencyId(recordWithId));
        assertFalse(IdempotencyUtils.hasIdempotencyId(recordWithoutId));
        assertFalse(IdempotencyUtils.hasIdempotencyId(null));
    }

    @Test
    void shouldLogIdempotencyActionWithId() {
        // Given
        String idempotencyId = "test-id";
        String action = "processed";
        String details = "Successfully processed message";

        // When & Then - 验证方法不会抛出异常
        assertDoesNotThrow(() -> 
            IdempotencyUtils.logIdempotencyAction(idempotencyId, action, details)
        );
    }

    @Test
    void shouldLogIdempotencyActionFromMDC() {
        // Given
        String idempotencyId = "test-id";
        String action = "processed";
        String details = "Successfully processed message";
        
        IdempotencyUtils.setIdempotencyId(idempotencyId);

        // When & Then - 验证方法不会抛出异常
        assertDoesNotThrow(() -> 
            IdempotencyUtils.logIdempotencyAction(action, details)
        );
    }

    @Test
    void shouldReturnCorrectHeaderKey() {
        // When
        String headerKey = IdempotencyUtils.getIdempotencyIdHeaderKey();

        // Then
        assertEquals("idempotent_id", headerKey);
    }

    @Test
    void shouldReturnCorrectMDCKey() {
        // When
        String mdcKey = IdempotencyUtils.getMdcIdempotencyIdKey();

        // Then
        assertEquals("idempotencyId", mdcKey);
    }
}
