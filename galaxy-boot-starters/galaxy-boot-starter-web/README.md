## Galaxy Boot Starter Web

> Galaxy Boot Starter Web 是基于 Spring Boot Web 的封装，提供了统一的、规范化的 Web 应用开发功能，包括日志记录、异常处理、链路追踪等企业级特性。

### 依赖配置

如果要在您的项目中使用 `Galaxy Boot Starter Web`，只需要引入对应 `starter` 即可：使用 group ID 为 `cn.com.chinastock` 和 `artifact ID` 为
`galaxy-boot-starter-web` 的 starter。

```xml
<dependency>
    <groupId>cn.com.chinastock</groupId>
    <artifactId>galaxy-boot-starter-web</artifactId>
</dependency>
``` 