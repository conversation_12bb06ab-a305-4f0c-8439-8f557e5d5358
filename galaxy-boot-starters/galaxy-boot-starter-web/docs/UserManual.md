### 组件使用说明
`Galaxy Boot Starter Web` 是一个基于 `Spring Boot Web` 的封装，提供了统一的、规范化的 Web 应用开发功能。
该组件自动配置了日志记录、异常处理、链路追踪等企业级特性，无需额外配置即可使用。

### 组件功能介绍

#### 统一日志记录
`Galaxy Boot Starter Web` 提供了统一的日志记录功能，自动记录HTTP请求和响应信息。

- **请求日志记录**：自动记录HTTP请求的方法、URI、请求头、请求体等信息
- **响应日志记录**：自动记录HTTP响应的状态码、响应头、响应体等信息
- **性能日志记录**：自动记录接口执行时间，便于性能监控和优化
- **敏感字段掩码**：支持对敏感字段进行掩码处理，保护数据安全

#### 链路追踪支持
`Galaxy Boot Starter Web` 集成了分布式链路追踪功能，支持 W3C Trace Context 标准和 Micrometer Tracing。

- **自动TraceId生成**：如果请求头中没有TraceId，系统会自动生成
- **SpanId管理**：自动生成和管理SpanId，支持父子Span关系
- **上下文传递**：自动将链路追踪信息传递到下游服务
- **MDC集成**：将链路追踪信息存储到MDC中，方便日志查询

#### 全局异常处理
`Galaxy Boot Starter Web` 提供了统一的全局异常处理机制，将各种异常转换为标准的响应格式。

- **业务异常处理**：处理`BusinessException`，返回业务错误信息
- **认证异常处理**：处理`UnauthorizedException`，返回401状态码
- **权限异常处理**：处理`ForbiddenException`，返回403状态码
- **服务器异常处理**：处理`ServerErrorException`和其他系统异常，返回500状态码
- **参数验证异常**：处理`MethodArgumentNotValidException`，返回400状态码

### 组件技术选型

#### Web框架对比

| **特性** | **Spring MVC** | **Spring WebFlux** | **Vert.x** | **Netty** |
|---------|---------------|------------------|-----------|----------|
| **编程模型** | 传统阻塞式 | 响应式非阻塞 | 响应式非阻塞 | 异步非阻塞 |
| **性能** | 中等 | 高 | 高 | 非常高 |
| **学习成本** | 低 | 中等 | 中等 | 高 |
| **生态成熟度** | 非常成熟 | 成熟 | 成熟 | 成熟 |
| **适用场景** | 传统Web应用 | 高并发Web应用 | 高并发应用 | 底层网络应用 |
