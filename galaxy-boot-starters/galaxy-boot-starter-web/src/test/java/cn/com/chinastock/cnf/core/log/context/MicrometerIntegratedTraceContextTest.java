package cn.com.chinastock.cnf.core.log.context;

import io.micrometer.tracing.Span;
import io.micrometer.tracing.TraceContext;
import io.micrometer.tracing.Tracer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * MicrometerIntegratedTraceContext 测试
 */
public class MicrometerIntegratedTraceContextTest {

    @Mock
    private Tracer mockTracer;

    @Mock
    private Span mockSpan;

    @Mock
    private TraceContext mockTraceContext;

    private MicrometerIntegratedTraceContext traceContext;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        traceContext = new MicrometerIntegratedTraceContext(mockTracer);
    }

    @Test
    public void shouldGetTraceIdFromMicrometer() {
        // Given
        String expectedTraceId = "4bf92f3577b34da6a3ce929d0e0e4736";
        when(mockTracer.currentSpan()).thenReturn(mockSpan);
        when(mockSpan.context()).thenReturn(mockTraceContext);
        when(mockTraceContext.traceId()).thenReturn(expectedTraceId);

        // When
        String actualTraceId = traceContext.getTraceId();

        // Then
        assertEquals(expectedTraceId, actualTraceId);
    }

    @Test
    public void shouldGetSpanIdFromMicrometer() {
        // Given
        String expectedSpanId = "00f067aa0ba902b7";
        when(mockTracer.currentSpan()).thenReturn(mockSpan);
        when(mockSpan.context()).thenReturn(mockTraceContext);
        when(mockTraceContext.spanId()).thenReturn(expectedSpanId);

        // When
        String actualSpanId = traceContext.getSpanId();

        // Then
        assertEquals(expectedSpanId, actualSpanId);
    }

    @Test
    public void shouldGetParentSpanIdFromMicrometer() {
        // Given
        String expectedParentSpanId = "01f067aa0ba902b8";
        when(mockTracer.currentSpan()).thenReturn(mockSpan);
        when(mockSpan.context()).thenReturn(mockTraceContext);
        when(mockTraceContext.parentId()).thenReturn(expectedParentSpanId);

        // When
        String actualParentSpanId = traceContext.getParentSpanId();

        // Then
        assertEquals(expectedParentSpanId, actualParentSpanId);
    }

    @Test
    public void shouldFallbackToTraditionalImplementationWhenMicrometerUnavailable() {
        // Given
        when(mockTracer.currentSpan()).thenReturn(null);

        // When
        traceContext.generateTraceId();
        traceContext.generateSpanId();
        String traceId = traceContext.getTraceId();
        String spanId = traceContext.getSpanId();

        // Then
        assertNotNull(traceId);
        assertNotNull(spanId);
        assertFalse(traceId.isEmpty());
        assertFalse(spanId.isEmpty());
    }

    @Test
    public void shouldExtractTraceContextFromHeaders() {
        // Given
        Map<String, String> headers = new HashMap<>();
        headers.put("traceparent", "00-4bf92f3577b34da6a3ce929d0e0e4736-00f067aa0ba902b7-01");
        when(mockTracer.currentSpan()).thenReturn(null); // 模拟 Micrometer 没有当前 span

        // When
        traceContext.extractTraceContext(headers);
        String traceId = traceContext.getTraceId();
        String parentSpanId = traceContext.getParentSpanId();

        // Then
        assertEquals("4bf92f3577b34da6a3ce929d0e0e4736", traceId);
        assertEquals("00f067aa0ba902b7", parentSpanId);
        assertNotNull(traceContext.getSpanId()); // 应该生成新的 spanId
    }

    @Test
    public void shouldGenerateTraceHeadersCorrectly() {
        // Given
        String traceId = "4bf92f3577b34da6a3ce929d0e0e4736";
        String spanId = "00f067aa0ba902b7";

        // When
        Map<String, String> headers = traceContext.generateTraceHeaders(traceId, spanId);

        // Then
        assertEquals(1, headers.size());
        assertTrue(headers.containsKey("traceparent"));
        assertEquals("00-4bf92f3577b34da6a3ce929d0e0e4736-00f067aa0ba902b7-01", headers.get("traceparent"));
    }

    @Test
    public void shouldHandleEmptyHeaders() {
        // Given
        Map<String, String> emptyHeaders = new HashMap<>();
        when(mockTracer.currentSpan()).thenReturn(null);

        // When
        traceContext.extractTraceContext(emptyHeaders);
        String traceId = traceContext.getTraceId();
        String spanId = traceContext.getSpanId();

        // Then
        assertNotNull(traceId);
        assertNotNull(spanId);
        assertFalse(traceId.isEmpty());
        assertFalse(spanId.isEmpty());
    }

    @Test
    public void shouldHandleInvalidTraceparentHeader() {
        // Given
        Map<String, String> headers = new HashMap<>();
        headers.put("traceparent", "invalid-format");
        when(mockTracer.currentSpan()).thenReturn(null);

        // When
        traceContext.extractTraceContext(headers);
        String traceId = traceContext.getTraceId();
        String spanId = traceContext.getSpanId();

        // Then
        assertNotNull(traceId);
        assertNotNull(spanId);
        assertFalse(traceId.isEmpty());
        assertFalse(spanId.isEmpty());
    }

    @Test
    public void shouldPreferMicrometerOverFallback() {
        // Given
        String micrometerTraceId = "micrometer-trace-id";
        String fallbackTraceId = "fallback-trace-id";
        
        // 设置 fallback 值
        when(mockTracer.currentSpan()).thenReturn(null);
        traceContext.generateTraceId(); // 这会设置 fallback 值
        
        // 然后设置 Micrometer 返回值
        when(mockTracer.currentSpan()).thenReturn(mockSpan);
        when(mockSpan.context()).thenReturn(mockTraceContext);
        when(mockTraceContext.traceId()).thenReturn(micrometerTraceId);

        // When
        String actualTraceId = traceContext.getTraceId();

        // Then
        assertEquals(micrometerTraceId, actualTraceId);
        assertNotEquals(fallbackTraceId, actualTraceId);
    }

    @Test
    public void shouldFallbackToGeneratedValuesWhenMicrometerSpanCreationFails() {
        // Given - 模拟 Micrometer span 创建失败
        when(mockTracer.nextSpan()).thenThrow(new RuntimeException("Span creation failed"));

        // When
        traceContext.extractTraceContext(new HashMap<>());

        // Then - 应该生成回退值
        assertNotNull(traceContext.getTraceId());
        assertNotNull(traceContext.getSpanId());
        assertNull(traceContext.getParentSpanId());
    }
}
