package cn.com.chinastock.cnf.core.log.context;

import cn.com.chinastock.cnf.core.log.config.LogProperties;
import cn.com.chinastock.cnf.core.log.LogCategory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import java.util.Map;
import java.util.Optional;

import static cn.com.chinastock.cnf.core.log.config.LogProperties.DEFAULT_MAX_LENGTH;

/**
 * 日志上下文信息管理，存储在MDC中
 *
 * <p>用于管理日志上下文信息，包括：</p>
 * <ul>
 *     <li>请求方法，e.g. GET</li>
 *     <li>请求URI，e.g. /api/login</li>
 *     <li>跟踪ID，e.g. 43d9131b5adc42569e82b4a0ed63106a</li>
 *     <li>父Span ID，e.g. 85c9b31ab6b24e8b</li>
 *     <li>Span ID，e.g. eedf62cd943e4859</li>
 *     <li>系统三字码，e.g. ESB</li>
 *     <li>服务名称，e.g. abc-service</li>
 *     <li>日志类别，参见LogCategory定义，e.g. APP_LOG</li>
 * </ul>
 */
public class LogContext {
    private static final ThreadLocal<LogContext> CONTEXT = ThreadLocal.withInitial(LogContext::new);
    private ITraceContext traceContext;
    private LogProperties logProperties;
    private Logger logger;

    /**
     * 获取当前上下文
     *
     * @return 当前上下文
     */
    public static LogContext current() {
        return CONTEXT.get();
    }

    /**
     * 获取日志记录器
     *
     * @param className 类名
     * @return Logger实例
     */
    public Logger getLogger(String className) {
        if (logger == null || !logger.getName().equals(className)) {
            logger = LoggerFactory.getLogger(className);
        }
        return logger;
    }

    /**
     * 获取日志相关配置
     *
     * @return 日志相关配置
     */
    public LogProperties getLogProperties() {
        return logProperties;
    }

    /**
     * 获取日志最大长度
     *
     * @return 日志最大长度
     */
    public int getMaxLogLength() {
        return Optional.ofNullable(logProperties)
                .map(LogProperties::getMaxLength)
                .orElse(DEFAULT_MAX_LENGTH);
    }

    /**
     * 获取默认日志类别
     *
     * @return 默认日志类别
     */
    public LogCategory getDefaultCategory() {
        return Optional.ofNullable(logProperties)
                .map(LogProperties::getDefaultCategory)
                .orElse(LogCategory.FRAMEWORK_LOG);
    }

    /**
     * 设置日志类别
     *
     * @param category 日志类别
     */
    public void setLogCategory(LogCategory category) {
        MDC.put("log_category", category.name());
    }

    /**
     * 清理日志上下文
     */
    public static void clear() {
        CONTEXT.remove();
    }

    /**
     * 加载日志上下文信息
     *
     * @param logProperties 日志属性配置
     * @param traceContext  跟踪上下文
     * @param method        请求方法 (如 "GET", "POST")
     * @param requestURI    请求的 URI
     * @param headers       请求头信息，主要用于提取 tracing 信息
     */
    public void loadContext(LogProperties logProperties, ITraceContext traceContext,
                            String method, String requestURI, Map<String, String> headers) {
        clearMDC();

        this.logProperties = logProperties;
        this.traceContext = traceContext;

        traceContext.extractTraceContext(headers);

        setMDC(method, requestURI);
    }

    public void setFrameworkLogAsDefault() {
        setLogCategory(LogCategory.FRAMEWORK_LOG);
    }

    private void clearMDC() {
        MDC.remove("request_method");
        MDC.remove("request_uri");
        MDC.remove("traceId");
        MDC.remove("parentSpanId");
        MDC.remove("spanId");
        setFrameworkLogAsDefault();
    }

    private void setMDC(String method, String requestURI) {
        MDC.put("request_method", method);
        MDC.put("request_uri", requestURI);
        // 使用与 Micrometer Tracing 一致的 MDC key 名称
        MDC.put("traceId", traceContext.getTraceId());
        MDC.put("parentSpanId", traceContext.getParentSpanId());
        MDC.put("spanId", traceContext.getSpanId());
    }
}