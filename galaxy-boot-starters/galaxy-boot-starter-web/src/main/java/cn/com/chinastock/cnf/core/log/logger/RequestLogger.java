package cn.com.chinastock.cnf.core.log.logger;

import static cn.com.chinastock.cnf.core.log.utils.HttpLogUtils.formatLogInfo;
import static cn.com.chinastock.cnf.core.log.utils.HttpLogUtils.maskSensitiveFields;

import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.http.MediaType;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import com.alibaba.fastjson2.JSON;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.core.log.config.LogProperties;
import cn.com.chinastock.cnf.core.log.filter.CacheableRequestWrapper;
import cn.com.chinastock.cnf.core.log.utils.HttpFileUtils;
import cn.com.chinastock.cnf.core.log.utils.HttpLogUtils;
import jakarta.servlet.http.HttpServletRequest;

/**
 * RequestLogger 类用于记录 HTTP 请求的日志信息。
 *
 * <p>该类中包含以下公共方法：</p>
 *
 * <ul>
 *     <li>{@link #RequestLogger(LogProperties)}：构造函数，初始化日志配置属性。</li>
 *     <li>{@link #log(HttpServletRequest)}：记录 HTTP 请求的日志信息。</li>
 *     <li>{@link #logWithFieldsMasked(Object)}：记录 HTTP 请求日志， 对带注解字段进行掩码处理。</li>
 * </ul>
 *
 * <AUTHOR>
 */
public class RequestLogger {
    private final LogProperties logProperties;

    /**
     * 构造函数
     *
     * @param logProperties 日志配置属性
     */
    public RequestLogger(LogProperties logProperties) {
        this.logProperties = logProperties;
    }

    /**
     * 记录请求日志
     *
     * @param request HTTP请求对象
     */
    public void log(HttpServletRequest request) {
        Map<String, Object> requestInfo = buildRequestInfo(request);
        String logMessage = formatLogInfo(requestInfo);
        GalaxyLogger.info(LogCategory.REQUEST_LOG, logMessage);
    }

    /**
     * 构建请求信息
     *
     * @param request HTTP请求对象
     * @return 包含请求信息的Map
     */
    private Map<String, Object> buildRequestInfo(HttpServletRequest request) {
        Map<String, Object> requestInfo = new HashMap<>();
        requestInfo.put("query_string", request.getQueryString());
        requestInfo.put("headers", getRequestHeaders(request));
        requestInfo.put("body", getRequestBody(request));
        return requestInfo;
    }

    /**
     * 获取请求头信息
     *
     * @param request HTTP请求对象
     * @return 请求头信息字符串，如果未启用则返回null
     */
    private String getRequestHeaders(HttpServletRequest request) {
        if (!logProperties.isRequestHeadersEnabled()) {
            return null;
        }

        Map<String, String> headers = new HashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            headers.put(headerName, request.getHeader(headerName));
        }
        return JSON.toJSONString(headers);
    }

    /**
     * 获取请求体内容
     *
     * @param request HTTP请求对象
     * @return 请求体内容，对于文件上传请求返回文件信息，其他请求返回请求体内容
     */
    private String getRequestBody(HttpServletRequest request) {
        String contentType = request.getContentType();
        if (contentType != null && contentType.startsWith(MediaType.MULTIPART_FORM_DATA_VALUE)) {
            return extractMultipartFileNames(request);
        }

        if (!(request instanceof CacheableRequestWrapper wrapper) || !wrapper.isCached()) {
            return null;
        }

        return HttpLogUtils.extractBody(wrapper.getCachedBody());
    }

    /**
     * 提取multipart请求中的文件信息，以JSON格式返回
     * 格式为：[{"file_name":"文件名","file_size":"文件大小"}]
     *
     * @param request HTTP请求对象
     * @return JSON格式的文件信息字符串
     */
    private String extractMultipartFileNames(HttpServletRequest request) {
        try {
            if (request.getParts() == null || request.getParts().isEmpty()) {
                return null;
            }

            List<Map<String, String>> fileInfos = request.getParts().stream()
                    .filter(part -> part.getSubmittedFileName() != null)
                    .map(part -> {
                        Map<String, String> fileInfo = new HashMap<>();
                        fileInfo.put("file_name", part.getSubmittedFileName());
                        fileInfo.put("file_size", HttpFileUtils.formatFileSize(part.getSize()));
                        return fileInfo;
                    })
                    .collect(Collectors.toList());

            return fileInfos.isEmpty() ? null : JSON.toJSONString(fileInfos);
        } catch (Exception e) {
            GalaxyLogger.error(LogCategory.FRAMEWORK_LOG, "Failed to extract multipart file information", e);
            return null;
        }
    }

    /**
     * 记录请求日志
     *
     * @param requestObject 请求参数数组
     */
    public void logWithFieldsMasked(Object requestObject) {
        // 获取当前请求的HttpServletRequest
        HttpServletRequest request = null;
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes != null) {
            request = (HttpServletRequest) requestAttributes.resolveReference(RequestAttributes.REFERENCE_REQUEST);
        }

        Map<String, Object> requestInfo = new HashMap<>();
        requestInfo.put("query_string", Optional.ofNullable(request).map(HttpServletRequest::getQueryString).orElse(null));
        requestInfo.put("headers", Optional.ofNullable(request).map(this::getRequestHeaders).orElse(null));
        requestInfo.put("body", getMaskedRequestBody(request, requestObject));
        String logMessage = formatLogInfo(requestInfo);
        GalaxyLogger.info(LogCategory.REQUEST_LOG, logMessage);
    }

    private String getMaskedRequestBody(HttpServletRequest request, Object requestObject) {
        String contentType = Optional.ofNullable(request).map(HttpServletRequest::getContentType).orElse(null);
        if (contentType != null && contentType.startsWith(MediaType.MULTIPART_FORM_DATA_VALUE)) {
            return extractMultipartFileNames(request);
        }

        if (requestObject != null) {
            return maskSensitiveFields(requestObject);
        }

        return null;
    }

}
