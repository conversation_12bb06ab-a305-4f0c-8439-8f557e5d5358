package cn.com.chinastock.cnf.core.log.context;

import io.micrometer.tracing.Span;
import io.micrometer.tracing.TraceContext;
import io.micrometer.tracing.Tracer;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 集成 Micrometer Tracing 的 TraceContext 实现
 * <p>
 * 优先使用 Micrometer Tracing 提供的 tracing 信息，
 * 如果 Micrometer 没有当前 span，则回退到传统的 W3C 实现
 *
 * <AUTHOR>
 */
public class MicrometerIntegratedTraceContext implements ITraceContext, ApplicationContextAware {

    private final Tracer tracer;

    // Spring ApplicationContext，用于动态获取 Tracer Bean
    private ApplicationContext applicationContext;

    // 静态的 Tracer 引用，用于动态获取（保留作为备用方案）
    private static volatile Tracer globalTracer;

    // 回退用的 W3CTraceContext 实现
    private final W3CTraceContext fallbackContext = new W3CTraceContext();

    // 当前创建的 Span（用于管理生命周期）
    private Span currentSpan;

    // 标记是否使用我们创建的 Span 信息
    private boolean useCreatedSpan = false;

    public MicrometerIntegratedTraceContext(Tracer tracer) {
        this.tracer = tracer;
    }

    /**
     * 设置 Spring ApplicationContext
     *
     * @param applicationContext Spring ApplicationContext
     * @throws BeansException 如果设置失败
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    /**
     * 设置全局的 Tracer 实例（保留作为备用方案）
     *
     * @param tracer Micrometer Tracer 实例
     */
    public static void setGlobalTracer(Tracer tracer) {
        globalTracer = tracer;
    }

    /**
     * 获取当前可用的 Tracer 实例
     * 优先级：实例级别的 tracer > Spring容器中的 Tracer Bean > 全局静态 tracer
     *
     * @return 当前可用的 Tracer 实例
     */
    private Tracer getCurrentTracer() {
        // 1. 优先使用实例级别的 tracer
        if (tracer != null) {
            return tracer;
        }

        // 2. 尝试从 Spring 容器获取 Tracer Bean
        if (applicationContext != null) {
            try {
                return applicationContext.getBean(Tracer.class);
            } catch (BeansException e) {
                // 如果没有找到 Tracer Bean，继续尝试其他方式
            }
        }

        // 3. 回退到全局静态 tracer
        return globalTracer;
    }

    private TraceContext getCurrentTraceContext() {
        Tracer currentTracer = getCurrentTracer();
        if (currentTracer != null) {
            Span activeSpan = currentTracer.currentSpan();
            if (activeSpan != null) {
                return activeSpan.context();
            }
        }

        if (useCreatedSpan && currentSpan != null) {
            return currentSpan.context();
        }

        return null;
    }

    @Override
    public String getTraceId() {
        TraceContext context = getCurrentTraceContext();
        if (context != null && StringUtils.hasText(context.traceId())) {
            return context.traceId();
        }

        // 回退到 W3CTraceContext 实现
        return fallbackContext.getTraceId();
    }

    @Override
    public String getSpanId() {
        TraceContext context = getCurrentTraceContext();
        if (context != null && StringUtils.hasText(context.spanId())) {
            return context.spanId();
        }

        // 回退到 W3CTraceContext 实现
        return fallbackContext.getSpanId();
    }

    @Override
    public String getParentSpanId() {
        TraceContext context = getCurrentTraceContext();
        if (context != null && StringUtils.hasText(context.parentId())) {
            return context.parentId();
        }

        // 回退到 W3CTraceContext 实现
        return fallbackContext.getParentSpanId();
    }

    @Override
    public void extractTraceContext(Map<String, String> headers) {
        boolean micrometerTraceContextLoaded = loadMicrometerTraceContext();

        if (!micrometerTraceContextLoaded) {
            fallbackContext.extractTraceContext(headers);
        }
    }

    @Override
    public Map<String, String> generateTraceHeaders(String traceId, String spanId) {
        return fallbackContext.generateTraceHeaders(traceId, spanId);
    }

    private boolean loadMicrometerTraceContext() {
        Tracer currentTracer = getCurrentTracer();
        if (currentTracer != null) {
            try {
                TraceContext currentTraceContext = loadCurrentTraceContext(currentTracer);
                if (currentTraceContext != null) {
                    fallbackContext.setTraceId(currentTraceContext.traceId());
                    fallbackContext.setSpanId(currentTraceContext.spanId());
                    fallbackContext.setParentSpanId(currentTraceContext.parentId());
                }
                return true;
            } catch (Exception e) {
                // 如果创建 span 失败，继续使用 fallbackContext
                return false;
            }
        }
        return false;

    }

    private TraceContext loadCurrentTraceContext(Tracer currentTracer) {
        TraceContext activeContext;
        Span activeSpan = currentTracer.currentSpan();
        if (activeSpan != null) {
            currentSpan = activeSpan;
            useCreatedSpan = false;
            activeContext = activeSpan.context();
        } else {
            currentSpan = currentTracer.nextSpan()
                    .name("http-request")
                    .start();
            useCreatedSpan = true;
            activeContext = currentSpan.context();
        }
        return activeContext;
    }

    @Override
    public void clear() {
        fallbackContext.clear();
        if (currentSpan != null && useCreatedSpan) {
            try {
                currentSpan.end();
            } catch (Exception e) {
                // 忽略清理时的异常
            } finally {
                currentSpan = null;
            }
        } else if (currentSpan != null) {
            currentSpan = null;
        }
        useCreatedSpan = false;
    }

    @Override
    public List<String> getTraceHeaders() {
        return fallbackContext.getTraceHeaders();
    }

    @Override
    public String generateTraceId() {
        return fallbackContext.generateTraceId();
    }

    @Override
    public String generateSpanId() {
        return fallbackContext.generateSpanId();
    }
}
