package cn.com.chinastock.cnf.core.log.logger;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import org.springframework.util.StopWatch;

/**
 * PerformanceLogger 类用于记录方法的执行时间，帮助监控和优化性能。
 *
 * <p>该类中包含以下公共方法：</p>
 *
 * <ul>
 *     <li>{@link #PerformanceLogger()}：构造函数，初始化日志配置属性。</li>
 *     <li>{@link #start()}：启动性能监控。</li>
 *     <li>{@link #stop()}：停止性能监控并记录耗时。</li>
 * </ul>
 *
 * <AUTHOR>
 */
public class PerformanceLogger {
    private final ThreadLocal<StopWatch> stopWatchThreadLocal = ThreadLocal.withInitial(StopWatch::new);

    /**
     * 构造函数
     *
     */
    public PerformanceLogger() {
    }

    /**
     * 启动性能监控
     */
    public void start() {
        StopWatch stopWatch = stopWatchThreadLocal.get();
        stopWatch.start();
    }

    /**
     * 停止性能监控并记录耗时
     */
    public void stop() {
        StopWatch stopWatch = stopWatchThreadLocal.get();
        stopWatch.stop();
        GalaxyLogger.info(LogCategory.PERFORMANCE_LOG,
                "cost=" + stopWatch.getTotalTimeMillis() + " unit=ms");
        stopWatchThreadLocal.remove();
    }
} 