package cn.com.chinastock.cnf.core.log.filter;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.core.log.config.LogProperties;
import cn.com.chinastock.cnf.core.log.context.ITraceContext;
import cn.com.chinastock.cnf.core.log.context.LogContext;
import cn.com.chinastock.cnf.core.log.logger.FrameworkLogger;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.core.Ordered;
import org.springframework.lang.NonNull;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Log Context 过滤器
 *
 * <p>过滤器，主要功能如下：</p>
 * <ul>
 *     <li>处理请求中的信息，提取 HTTP method、HTTP request URI、 traceId、spanId 和 parentSpanId</li>
 *     <li>记录请求日志（可通过配置控制）</li>
 *     <li>记录响应日志（可通过配置控制）</li>
 *     <li>记录性能日志（可通过配置控制）</li>
 * </ul>
 */
public class LogFilter extends OncePerRequestFilter implements Ordered {
    private final ITraceContext traceContext;
    private final LogProperties logProperties;
    private final FrameworkLogger frameworkLogger;

    public LogFilter(ITraceContext traceContext, LogProperties logProperties) {
        this.traceContext = traceContext;
        this.logProperties = logProperties;
        this.frameworkLogger = new FrameworkLogger(logProperties);
    }

    @Override
    protected void doFilterInternal(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull FilterChain filterChain) throws ServletException, IOException {
        loadLogContext(request);
        CacheableRequestWrapper requestWrapper = new CacheableRequestWrapper(request);
        ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper(response);
        writeRequestLogAndStartWatch(requestWrapper);

        try {
            filterChain.doFilter(requestWrapper, responseWrapper);
        } finally {
            writeResponseLogAndStopWatch(responseWrapper);
        }
    }

    private void loadLogContext(HttpServletRequest request) {
        LogContext.current().loadContext(logProperties, traceContext, request.getMethod(), request.getRequestURI(), getRequestHeaders(request));
    }

    private void writeRequestLogAndStartWatch(CacheableRequestWrapper requestWrapper) {
        try {
            frameworkLogger.startPerformanceWatch();
            frameworkLogger.logRequest(requestWrapper);
        } catch (Exception e) {
            GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "writeRequestLogAndStartWatch: exception occurred", e);
        }
    }

    private void writeResponseLogAndStopWatch(ContentCachingResponseWrapper responseWrapper) {
        try {
            frameworkLogger.logResponse(responseWrapper);
            responseWrapper.copyBodyToResponse();

            frameworkLogger.stopPerformanceWatch();
            LogContext.clear();
        } catch (Exception e) {
            GalaxyLogger.error(LogCategory.EXCEPTION_LOG, "writeResponseLogAndStopWatch: exception occurred", e);
        }
    }

    private Map<String, String> getRequestHeaders(HttpServletRequest httpRequest) {
        Map<String, String> headers = new HashMap<>();
        httpRequest.getHeaderNames()
                .asIterator()
                .forEachRemaining(headerName -> headers.put(headerName, httpRequest.getHeader(headerName)));
        return headers;
    }

    @Override
    public int getOrder() {
        // 确保在 OpenTelemetry tracing filter 之后执行
        return Ordered.HIGHEST_PRECEDENCE + 2000;
    }
}