package cn.com.chinastock.cnf.feign.interceptor;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.core.log.context.ITraceContext;
import cn.com.chinastock.cnf.feign.esb.ESBAuthentication;
import cn.com.chinastock.cnf.feign.esb.ESBProperties;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.slf4j.MDC;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Map;

public class FeignRequestInterceptor implements RequestInterceptor {

    private final ITraceContext traceContext;
    private final ESBProperties esbProperties;

    public FeignRequestInterceptor(ITraceContext traceContext, ESBProperties esbProperties) {
        this.traceContext = traceContext;
        this.esbProperties = esbProperties;
    }

    @Override
    public void apply(RequestTemplate requestTemplate) {
        setTracingHeaders(requestTemplate);
        setESBAuthHeaders(requestTemplate);
    }

    private void setTracingHeaders(RequestTemplate requestTemplate) {
        String traceId = MDC.get(TraceConstants.TRACE_ID);
        String spanId = MDC.get(TraceConstants.SPAN_ID);
        GalaxyLogger.debug(LogCategory.FRAMEWORK_LOG, "FeignRequestInterceptor::setTracingHeaders, traceId={}, spanId={}", traceId, spanId);
        traceContext.generateTraceHeaders(traceId, spanId).forEach(requestTemplate::header);
    }

    private void setESBAuthHeaders(RequestTemplate requestTemplate) {
        Map<String, Collection<String>> headers = requestTemplate.headers();

        // 如果不是ESB请求，直接返回
        if (!ESBAuthentication.isESBRequest(headers)) {
            return;
        }

        // 获取并设置ESB请求头
        ESBAuthentication.generateHeaders(esbProperties).forEach((key, value) -> {
            if (shouldSetHeader(headers, key)) {
                requestTemplate.header(key, value);
            }
        });
    }

    private boolean shouldSetHeader(Map<String, Collection<String>> headers, String headerName) {
        return !headers.containsKey(headerName) || CollectionUtils.isEmpty(headers.get(headerName));
    }
}
