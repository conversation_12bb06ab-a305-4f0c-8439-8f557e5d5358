package cn.com.chinastock.cnf.feign.interceptor;

import cn.com.chinastock.cnf.core.log.context.ITraceContext;
import cn.com.chinastock.cnf.feign.esb.ESBAuthentication;
import cn.com.chinastock.cnf.feign.esb.ESBProperties;
import feign.RequestTemplate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.MDC;

import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class FeignRequestInterceptorTest {

    @Mock
    private ITraceContext traceContext;

    @Mock
    private ESBProperties esbProperties;

    @Mock
    private RequestTemplate requestTemplate;

    private FeignRequestInterceptor interceptor;

    @BeforeEach
    void setUp() {
        interceptor = new FeignRequestInterceptor(traceContext, esbProperties);
        MDC.clear();
    }

    @Test
    void shouldAddESBHeadersWhenFunctionNoHeaderExists() {
        // Given
        Map<String, Collection<String>> headers = new HashMap<>();
        headers.put(ESBAuthentication.FUNCTION_NO_HEADER, Collections.singletonList("testFunction"));
        when(requestTemplate.headers()).thenReturn(headers);
        when(traceContext.generateTraceHeaders(any(), any())).thenReturn(new HashMap<>());
        when(esbProperties.getUser()).thenReturn("testUser");
        when(esbProperties.getPassword()).thenReturn("testPassword");
        when(esbProperties.getSystemCode()).thenReturn("testSystem");

        // When
        interceptor.apply(requestTemplate);

        // Then
        verify(requestTemplate).header(eq(ESBAuthentication.CONTENT_TYPE_HEADER), eq(ESBAuthentication.DEFAULT_CONTENT_TYPE));
        verify(requestTemplate).header(eq(ESBAuthentication.USER_HEADER), eq("testUser"));
        verify(requestTemplate).header(eq(ESBAuthentication.CREATED_HEADER), anyString());
        verify(requestTemplate).header(eq(ESBAuthentication.NONCE_HEADER), anyString());
        verify(requestTemplate).header(eq(ESBAuthentication.PWD_DIGEST_HEADER), anyString());
        verify(requestTemplate).header(eq(ESBAuthentication.CALLER_SYSTEM_CODE_HEADER), eq("testSystem"));
    }

    @Test
    void shouldNotAddESBHeadersWhenFunctionNoHeaderExistsButESBHeadersAlreadyExists() {
        // Given
        Map<String, Collection<String>> headers = new HashMap<>();
        headers.put(ESBAuthentication.FUNCTION_NO_HEADER, Collections.singletonList("testFunction"));
        headers.put(ESBAuthentication.CONTENT_TYPE_HEADER, Collections.singletonList("application/xml"));
        headers.put(ESBAuthentication.USER_HEADER, Collections.singletonList("existingUser"));
        headers.put(ESBAuthentication.CREATED_HEADER, Collections.singletonList("existingCreated"));
        headers.put(ESBAuthentication.NONCE_HEADER, Collections.singletonList("existingNonce"));
        headers.put(ESBAuthentication.PWD_DIGEST_HEADER, Collections.singletonList("existingPasswordDigest"));
        headers.put(ESBAuthentication.CALLER_SYSTEM_CODE_HEADER, Collections.singletonList("existingSystemCode"));
        when(requestTemplate.headers()).thenReturn(headers);
        when(traceContext.generateTraceHeaders(any(), any())).thenReturn(new HashMap<>());
        when(esbProperties.getUser()).thenReturn("testUser");
        when(esbProperties.getPassword()).thenReturn("testPassword");
        when(esbProperties.getSystemCode()).thenReturn("testSystem");

        // When
        interceptor.apply(requestTemplate);

        // Then
        verify(requestTemplate, never()).header(eq(ESBAuthentication.CONTENT_TYPE_HEADER), anyString());
        verify(requestTemplate, never()).header(eq(ESBAuthentication.USER_HEADER), anyString());
        verify(requestTemplate, never()).header(eq(ESBAuthentication.CREATED_HEADER), anyString());
        verify(requestTemplate, never()).header(eq(ESBAuthentication.NONCE_HEADER), anyString());
        verify(requestTemplate, never()).header(eq(ESBAuthentication.PWD_DIGEST_HEADER), anyString());
        verify(requestTemplate, never()).header(eq(ESBAuthentication.CALLER_SYSTEM_CODE_HEADER), anyString());
    }

    @Test
    void shouldNotAddESBHeadersWhenFunctionNoHeaderNotExists() {
        // Given
        Map<String, Collection<String>> headers = new HashMap<>();
        when(requestTemplate.headers()).thenReturn(headers);
        when(traceContext.generateTraceHeaders(any(), any())).thenReturn(new HashMap<>());

        // When
        interceptor.apply(requestTemplate);

        // Then
        verify(requestTemplate, never()).header(eq(ESBAuthentication.CONTENT_TYPE_HEADER), anyString());
        verify(requestTemplate, never()).header(eq(ESBAuthentication.USER_HEADER), anyString());
        verify(requestTemplate, never()).header(eq(ESBAuthentication.CREATED_HEADER), anyString());
        verify(requestTemplate, never()).header(eq(ESBAuthentication.NONCE_HEADER), anyString());
        verify(requestTemplate, never()).header(eq(ESBAuthentication.PWD_DIGEST_HEADER), anyString());
        verify(requestTemplate, never()).header(eq(ESBAuthentication.CALLER_SYSTEM_CODE_HEADER), anyString());
    }

    @Test
    void shouldNotOverwriteExistingESBHeaders() {
        // Given
        Map<String, Collection<String>> headers = new HashMap<>();
        headers.put(ESBAuthentication.FUNCTION_NO_HEADER, Collections.singletonList("testFunction"));
        headers.put(ESBAuthentication.CONTENT_TYPE_HEADER, Collections.singletonList("application/xml"));
        headers.put(ESBAuthentication.USER_HEADER, Collections.singletonList("existingUser"));
        when(requestTemplate.headers()).thenReturn(headers);
        when(esbProperties.getUser()).thenReturn("testUser");
        when(esbProperties.getPassword()).thenReturn("testPassword");
        when(esbProperties.getSystemCode()).thenReturn("testSystem");

        // When
        interceptor.apply(requestTemplate);

        // Then
        verify(requestTemplate, never()).header(eq(ESBAuthentication.CONTENT_TYPE_HEADER), anyString());
        verify(requestTemplate, never()).header(eq(ESBAuthentication.USER_HEADER), anyString());
    }

    @Test
    void shouldAddTraceHeadersToRequest() {
        // Given
        String traceId = "test-trace-id";
        String spanId = "test-span-id";
        MDC.put(TraceConstants.TRACE_ID, traceId);
        MDC.put(TraceConstants.SPAN_ID, spanId);

        String traceparentHeader = "test-trace-parent";
        Map<String, String> headers = new HashMap<>();
        headers.put(TraceConstants.TRACE_PARENT, traceparentHeader);
        
        when(traceContext.generateTraceHeaders(traceId, spanId)).thenReturn(headers);

        // When
        interceptor.apply(requestTemplate);

        // Then
        verify(requestTemplate).header(TraceConstants.TRACE_PARENT, traceparentHeader);
        verify(traceContext).generateTraceHeaders(traceId, spanId);
    }

    @Test
    void shouldHandleNullTraceIds() {
        // Given
        MDC.clear();
        when(traceContext.generateTraceHeaders(null, null))
            .thenReturn(new HashMap<>());

        // When
        interceptor.apply(requestTemplate);

        // Then
        verify(traceContext).generateTraceHeaders(null, null);
        verify(requestTemplate, never()).header(anyString(), anyString());
    }
} 