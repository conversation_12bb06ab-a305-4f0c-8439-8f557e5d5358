# Galaxy Boot Starter MCP 用户手册

## 概述

Galaxy Boot Starter MCP 是基于 Spring AI MCP 的封装，提供了通过注解将 SpringBoot RestController 接口自动暴露为 MCP (Model Context Protocol) 工具的功能。这使得 AI 可以通过聊天的方式调用这些 API 完成业务功能。

## 核心特性

### 1. 注解驱动
- 使用 `@McpTool` 注解轻松标记需要暴露的方法
- 支持丰富的配置选项：名称、描述、分组、标签等
- 自动生成 JSON Schema 用于参数验证

### 2. 自动发现与注册
- 自动扫描 Spring 容器中的 Controller
- 智能识别带有 `@McpTool` 注解的方法
- 支持包路径过滤和条件注册

### 3. 类型安全
- 自动生成参数的 JSON Schema
- 支持复杂对象类型的序列化和反序列化
- 智能的类型转换和验证

### 4. 灵活配置
- 支持工具分组和标签管理
- 可配置的过滤器和权限控制
- 单个工具的个性化配置

## 快速开始

### 1. 添加依赖

```xml
<dependency>
    <groupId>cn.com.chinastock</groupId>
    <artifactId>galaxy-boot-starter-mcp</artifactId>
</dependency>
```

### 2. 创建 Controller

```java
@RestController
@RequestMapping("/api")
public class ExampleController {
    
    @McpTool(
        name = "greetUser",
        description = "向用户问候",
        group = "greeting",
        tags = {"greeting", "public"}
    )
    @GetMapping("/greet")
    public String greet(@RequestParam String name) {
        return "Hello, " + name + "!";
    }
}
```

### 3. 配置应用

```yaml
galaxy:
  mcp:
    enabled: true
    server-name: my-mcp-server
    auto-scan: true
    scan-packages:
      - com.example.controller
```

### 4. 启动应用

启动 Spring Boot 应用后，MCP 服务器会自动启动并暴露配置的工具。

## 注解详解

### @McpTool 注解

```java
@McpTool(
    name = "toolName",              // 工具名称，默认使用方法名
    description = "工具描述",        // 工具功能描述，用于 AI 理解
    enabled = true,                 // 是否启用，默认 true
    group = "default",              // 工具分组，默认 "default"
    tags = {"tag1", "tag2"},        // 工具标签，用于分类
    responseMimeType = "application/json"  // 响应 MIME 类型
)
```

#### 参数说明

- **name**: 工具的唯一标识符，如果不指定则使用方法名
- **description**: 工具的功能描述，AI 会根据这个描述来理解工具的用途
- **enabled**: 是否启用此工具，可以用于临时禁用某些工具
- **group**: 工具分组，用于组织和管理相关的工具
- **tags**: 工具标签，支持多标签分类，便于过滤和搜索
- **responseMimeType**: 响应的 MIME 类型，如果不指定会根据返回类型自动推断

## 配置详解

### 基础配置

```yaml
galaxy:
  mcp:
    enabled: true                    # 是否启用 MCP 功能
    server-name: galaxy-mcp-server   # MCP 服务器名称
    server-version: 1.0.0            # MCP 服务器版本
    server-description: "服务器描述"  # MCP 服务器描述
    auto-scan: true                  # 是否启用自动扫描
    scan-packages:                   # 扫描的包路径
      - com.example.controller
```

### 工具配置

```yaml
galaxy:
  mcp:
    tool:
      enabled: true                  # 是否启用工具功能
      timeout-seconds: 30            # 工具响应超时时间
      group-filters:                 # 工具分组过滤器
        - user
        - admin
      tag-filters:                   # 工具标签过滤器
        - public
        - api
      items:                         # 单个工具配置
        getUserInfo:
          enabled: true
          description: "自定义描述"
          response-mime-type: "application/json"
```

### 传输配置

```yaml
galaxy:
  mcp:
    transport:
      type: webmvc                   # 传输类型：webmvc/webflux/stdio
      sse-endpoint: /mcp/sse         # SSE 端点路径
      message-endpoint: /mcp/message # 消息端点路径
      base-url: ""                   # 基础 URL 前缀
```

### 安全配置

```yaml
galaxy:
  mcp:
    security:
      enabled: false                 # 是否启用安全认证
      api-key: your-api-key          # API 密钥
      allowed-ips:                   # 允许的 IP 地址列表
        - 127.0.0.1
        - ***********/24
```

## 高级功能

### 1. 工具分组管理

通过分组可以更好地组织和管理工具：

```java
// 用户管理工具组
@McpTool(group = "user", ...)
public User getUserById(Long id) { ... }

// 系统管理工具组
@McpTool(group = "system", ...)
public SystemInfo getSystemInfo() { ... }
```

### 2. 标签分类

使用标签进行细粒度的工具分类：

```java
@McpTool(
    tags = {"public", "query", "user"}  // 公开的、查询类的、用户相关的工具
)
public List<User> getAllUsers() { ... }

@McpTool(
    tags = {"admin", "create", "user"}  // 管理员、创建类的、用户相关的工具
)
public User createUser(CreateUserRequest request) { ... }
```

### 3. 条件过滤

通过配置文件控制工具的暴露：

```yaml
galaxy:
  mcp:
    tool:
      # 只暴露特定分组的工具
      group-filters:
        - user
        - public
      # 只暴露特定标签的工具
      tag-filters:
        - public
        - api
```

### 4. 单个工具配置

为特定工具进行个性化配置：

```yaml
galaxy:
  mcp:
    tool:
      items:
        sensitiveOperation:
          enabled: false              # 禁用敏感操作
        getUserInfo:
          description: "获取用户详细信息（包含敏感数据）"
          response-mime-type: "application/json"
```

## 支持的参数类型

### 1. 基本类型
- `String`, `Integer`, `Long`, `Double`, `Boolean` 等
- 自动进行类型转换和验证

### 2. 复杂对象
- 支持 `@RequestBody` 注解的复杂对象
- 自动生成 JSON Schema
- 支持嵌套对象和集合类型

### 3. Spring MVC 注解
- `@PathVariable` - 路径变量
- `@RequestParam` - 请求参数
- `@RequestBody` - 请求体
- `@RequestHeader` - 请求头（部分支持）

### 4. 验证注解
- `@Valid` - 参数验证
- `@NotNull`, `@NotBlank`, `@Email` 等 JSR-303 注解

## 返回类型支持

### 1. 基本类型
- 字符串、数字、布尔值等基本类型
- 自动转换为相应的 MIME 类型

### 2. 复杂对象
- POJO 对象自动序列化为 JSON
- 支持嵌套对象和集合类型

### 3. ResponseEntity
- 支持 Spring MVC 的 `ResponseEntity`
- 自动提取响应体内容

### 4. 自定义 MIME 类型
- 通过 `responseMimeType` 属性指定
- 支持 `text/plain`, `application/json`, `text/html` 等

## 最佳实践

### 1. 工具命名
- 使用清晰、描述性的工具名称
- 遵循驼峰命名规范
- 避免使用特殊字符和空格

### 2. 描述编写
- 提供详细、准确的功能描述
- 说明参数的含义和要求
- 包含使用示例和注意事项

### 3. 分组策略
- 按业务功能进行分组
- 保持分组的一致性和逻辑性
- 避免过度细分或过于宽泛

### 4. 标签使用
- 使用有意义的标签
- 保持标签的一致性
- 考虑权限和可见性需求

### 5. 错误处理
- 提供清晰的错误信息
- 使用适当的异常类型
- 考虑用户体验和调试需求

## 故障排除

### 常见问题

1. **工具未被发现**
   - 检查包扫描路径配置
   - 确认 `@McpTool` 注解正确使用
   - 查看启动日志中的工具注册信息

2. **参数类型错误**
   - 确保参数类型支持 JSON 序列化
   - 检查请求参数格式是否正确
   - 使用 `@Valid` 注解进行参数验证

3. **工具执行失败**
   - 检查业务逻辑是否正确
   - 查看异常日志和错误信息
   - 确认参数传递是否正确

### 调试技巧

1. **启用调试日志**
```yaml
logging:
  level:
    cn.com.chinastock.cnf.mcp: DEBUG
```

2. **查看工具注册信息**
启动时会输出工具注册日志：
```
Galaxy Boot MCP: Found 5 MCP tools
Galaxy Boot MCP: Registered tool 'getUserById' from method UserController.getUserById
```

3. **测试 REST API**
在集成 MCP 之前，先测试原始的 REST API 是否正常工作。

## 版本兼容性

- Spring Boot 3.3.7+
- Spring AI 1.0.0+
- Java 21+
- Jackson 2.15+

## 更新日志

### 0.1.1-ALPHA
- 初始版本发布
- 支持基本的 MCP 工具注解和自动配置
- 支持 WebMVC 传输方式
- 支持工具分组和标签功能
- 支持 JSON Schema 自动生成
- 支持灵活的配置和过滤机制
