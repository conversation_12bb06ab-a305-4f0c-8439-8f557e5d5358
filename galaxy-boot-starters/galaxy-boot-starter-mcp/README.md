# Galaxy Boot Starter MCP

> Galaxy Boot Starter MCP 是基于 Spring AI MCP 的封装，提供了通过注解将 SpringBoot RestController 接口自动暴露为 MCP 工具的功能，使得 AI 可以通过聊天的方式调用这些 API 完成业务功能。

## 功能特性

- **注解驱动**：通过 `@McpTool` 注解轻松将 Controller 方法暴露为 MCP 工具
- **自动发现**：自动扫描和注册带有注解的方法
- **类型安全**：自动生成 JSON Schema 用于参数验证
- **灵活配置**：支持工具分组、标签、过滤等高级配置
- **多传输支持**：支持 WebMVC、WebFlux 和 STDIO 传输方式
- **安全控制**：可选的 API 密钥和 IP 白名单功能

## 依赖配置

如果要在您的项目中使用 `Galaxy Boot Starter MCP`，只需要引入对应 `starter` 即可：

```xml
<dependency>
    <groupId>cn.com.chinastock</groupId>
    <artifactId>galaxy-boot-starter-mcp</artifactId>
</dependency>
```

## 快速开始

### 1. 创建 Controller 并添加注解

```java
@RestController
@RequestMapping("/api")
public class UserController {
    
    @McpTool(
        name = "getUserInfo",
        description = "获取用户信息",
        group = "user",
        tags = {"user", "query"}
    )
    @GetMapping("/user/{id}")
    public UserInfo getUserInfo(@PathVariable Long id) {
        // 业务逻辑
        return userService.getUserById(id);
    }
    
    @McpTool(
        name = "createUser",
        description = "创建新用户",
        group = "user",
        tags = {"user", "create"}
    )
    @PostMapping("/user")
    public UserInfo createUser(@RequestBody CreateUserRequest request) {
        // 业务逻辑
        return userService.createUser(request);
    }
}
```

### 2. 配置应用属性

```yaml
galaxy:
  mcp:
    enabled: true
    server-name: my-app-mcp-server
    server-version: 1.0.0
    auto-scan: true
    scan-packages:
      - com.example.controller
    tool:
      enabled: true
      timeout-seconds: 30
    transport:
      type: webmvc
      sse-endpoint: /mcp/sse
      message-endpoint: /mcp/message
```

### 3. 启动应用

启动 Spring Boot 应用后，MCP 服务器会自动启动并暴露配置的工具。

## 注解说明

### @McpTool

用于标记需要暴露为 MCP 工具的 Controller 方法。

```java
@McpTool(
    name = "toolName",              // 工具名称，默认使用方法名
    description = "工具描述",        // 工具功能描述，用于 AI 理解
    enabled = true,                 // 是否启用，默认 true
    group = "default",              // 工具分组，默认 "default"
    tags = {"tag1", "tag2"},        // 工具标签，用于分类
    responseMimeType = "application/json"  // 响应 MIME 类型
)
```

## 配置属性

### 基础配置

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `galaxy.mcp.enabled` | Boolean | `true` | 是否启用 MCP 功能 |
| `galaxy.mcp.server-name` | String | `galaxy-mcp-server` | MCP 服务器名称 |
| `galaxy.mcp.server-version` | String | `1.0.0` | MCP 服务器版本 |
| `galaxy.mcp.auto-scan` | Boolean | `true` | 是否启用自动扫描 |
| `galaxy.mcp.scan-packages` | List<String> | `[]` | 扫描的包路径 |

### 工具配置

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `galaxy.mcp.tool.enabled` | Boolean | `true` | 是否启用工具功能 |
| `galaxy.mcp.tool.timeout-seconds` | Integer | `30` | 工具响应超时时间 |
| `galaxy.mcp.tool.group-filters` | List<String> | `[]` | 工具分组过滤器 |
| `galaxy.mcp.tool.tag-filters` | List<String> | `[]` | 工具标签过滤器 |

### 传输配置

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `galaxy.mcp.transport.type` | String | `webmvc` | 传输类型：webmvc/webflux/stdio |
| `galaxy.mcp.transport.sse-endpoint` | String | `/mcp/sse` | SSE 端点路径 |
| `galaxy.mcp.transport.message-endpoint` | String | `/mcp/message` | 消息端点路径 |
| `galaxy.mcp.transport.base-url` | String | `""` | 基础 URL 前缀 |

### 安全配置

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `galaxy.mcp.security.enabled` | Boolean | `false` | 是否启用安全认证 |
| `galaxy.mcp.security.api-key` | String | - | API 密钥 |
| `galaxy.mcp.security.allowed-ips` | List<String> | `[]` | 允许的 IP 地址列表 |

## 高级配置

### 单个工具配置

可以为特定工具进行个性化配置：

```yaml
galaxy:
  mcp:
    tool:
      items:
        getUserInfo:
          enabled: true
          description: "自定义描述"
          response-mime-type: "application/json"
        createUser:
          enabled: false
```

### 工具过滤

只暴露特定分组或标签的工具：

```yaml
galaxy:
  mcp:
    tool:
      group-filters:
        - user
        - order
      tag-filters:
        - public
        - api
```

## 使用示例

### 完整示例

```java
@RestController
@RequestMapping("/api/order")
public class OrderController {
    
    @Autowired
    private OrderService orderService;
    
    @McpTool(
        name = "queryOrder",
        description = "根据订单号查询订单信息",
        group = "order",
        tags = {"order", "query", "public"}
    )
    @GetMapping("/{orderNo}")
    public OrderInfo queryOrder(@PathVariable String orderNo) {
        return orderService.getByOrderNo(orderNo);
    }
    
    @McpTool(
        name = "createOrder",
        description = "创建新订单",
        group = "order",
        tags = {"order", "create", "public"},
        responseMimeType = "application/json"
    )
    @PostMapping
    public OrderInfo createOrder(@RequestBody CreateOrderRequest request) {
        return orderService.createOrder(request);
    }
    
    @McpTool(
        name = "cancelOrder",
        description = "取消订单",
        group = "order",
        tags = {"order", "cancel", "public"}
    )
    @PutMapping("/{orderNo}/cancel")
    public void cancelOrder(@PathVariable String orderNo, 
                           @RequestParam String reason) {
        orderService.cancelOrder(orderNo, reason);
    }
}
```

## 注意事项

1. **参数支持**：支持 `@PathVariable`、`@RequestParam`、`@RequestBody` 等 Spring MVC 注解
2. **返回类型**：支持基本类型、复杂对象、`ResponseEntity` 等返回类型
3. **异常处理**：工具执行异常会被自动捕获并返回错误信息
4. **类型转换**：自动进行参数类型转换和 JSON 序列化
5. **性能考虑**：建议合理设置超时时间和过滤条件

## 集成测试

启动应用后，可以通过以下方式测试 MCP 功能：

1. **查看工具列表**：访问 MCP 端点查看已注册的工具
2. **调用工具**：通过 MCP 客户端调用工具
3. **监控日志**：查看工具执行日志和性能指标

## 故障排除

### 常见问题

1. **工具未被发现**：检查包扫描路径和注解配置
2. **参数类型错误**：确保参数类型支持 JSON 序列化
3. **超时问题**：调整超时配置或优化业务逻辑
4. **权限问题**：检查安全配置和 IP 白名单

### 调试技巧

启用调试日志：

```yaml
logging:
  level:
    cn.com.chinastock.cnf.mcp: DEBUG
```

## 版本兼容性

- Spring Boot 3.3.7+
- Spring AI 1.0.0+
- Java 21+

## 更新日志

### 0.1.1-ALPHA
- 初始版本发布
- 支持基本的 MCP 工具注解和自动配置
- 支持 WebMVC 传输方式
- 支持工具分组和标签功能
