# Galaxy Boot MCP Server 客户端集成指南

## 概述

本指南详细说明如何在各种 AI 编程工具（如 Cursor、<PERSON><PERSON>、<PERSON> 等）中配置和使用 Galaxy Boot MCP Server。

## MCP Server 配置

### 1. 标准 MCP 配置文件

创建 MCP 配置文件 `mcp-config.json`：

```json
{
  "mcpServers": {
    "galaxy-mcp-server": {
      "command": "java",
      "args": [
        "-jar",
        "/path/to/galaxy-boot-mcp-example.jar",
        "--galaxy.mcp.transport.type=stdio",
        "--galaxy.mcp.enabled=true"
      ],
      "env": {
        "JAVA_OPTS": "-Xmx512m",
        "SPRING_PROFILES_ACTIVE": "mcp"
      }
    }
  }
}
```

### 2. 远程服务器 SSE 传输配置（推荐）

对于运行在远程服务器上的服务，使用 SSE (Server-Sent Events) 传输：

```json
{
  "mcpServers": {
    "galaxy-remote-server": {
      "command": "node",
      "args": [
        "/path/to/mcp-sse-client.js",
        "https://your-server.com:8080/mcp/sse"
      ],
      "env": {
        "MCP_SERVER_URL": "https://your-server.com:8080",
        "MCP_API_KEY": "your-api-key-if-needed"
      }
    }
  }
}
```

### 3. HTTP Proxy 传输配置

使用 HTTP 代理方式连接远程服务器：

```json
{
  "mcpServers": {
    "galaxy-http-proxy": {
      "command": "python3",
      "args": [
        "/path/to/mcp-http-proxy.py",
        "--server-url", "https://your-server.com:8080",
        "--endpoint", "/mcp/message"
      ]
    }
  }
}
```

### 4. WebSocket 传输配置

使用 WebSocket 连接（需要服务器支持）：

```json
{
  "mcpServers": {
    "galaxy-websocket": {
      "command": "node",
      "args": [
        "/path/to/mcp-ws-client.js",
        "wss://your-server.com:8080/mcp/ws"
      ]
    }
  }
}
```

## Cursor 集成

### 1. 配置文件位置

在 Cursor 中，MCP 配置文件通常位于：

**macOS:**
```
~/Library/Application Support/Cursor/User/globalStorage/mcp-servers.json
```

**Windows:**
```
%APPDATA%\Cursor\User\globalStorage\mcp-servers.json
```

**Linux:**
```
~/.config/Cursor/User/globalStorage/mcp-servers.json
```

### 2. Cursor 配置示例

```json
{
  "mcpServers": {
    "galaxy-business-api": {
      "command": "java",
      "args": [
        "-jar",
        "/Users/<USER>/projects/galaxy-boot-mcp-example.jar",
        "--server.port=8080",
        "--galaxy.mcp.enabled=true",
        "--galaxy.mcp.transport.type=stdio"
      ],
      "env": {
        "SPRING_PROFILES_ACTIVE": "production"
      }
    }
  }
}
```

### 3. 在 Cursor 中使用

配置完成后，在 Cursor 的聊天界面中可以这样使用：

```
@galaxy-business-api 帮我计算 15 + 25 的结果

@galaxy-business-api 查询用户ID为1的用户信息

@galaxy-business-api 创建一个新用户，用户名是alice，邮箱是*****************
```

## Cline (Claude Dev) 集成

### 1. 配置文件

Cline 使用 VS Code 的配置系统，在 `settings.json` 中添加：

```json
{
  "cline.mcpServers": {
    "galaxy-mcp-server": {
      "command": "java",
      "args": [
        "-jar",
        "galaxy-boot-mcp-example.jar",
        "--galaxy.mcp.transport.type=stdio"
      ]
    }
  }
}
```

### 2. 工作区配置

在项目的 `.vscode/settings.json` 中：

```json
{
  "cline.mcpServers": {
    "local-business-api": {
      "command": "mvn",
      "args": [
        "spring-boot:run",
        "-Dgalaxy.mcp.transport.type=stdio",
        "-Dgalaxy.mcp.enabled=true"
      ],
      "cwd": "${workspaceFolder}/galaxy-boot-examples/galaxy-boot-mcp-example"
    }
  }
}
```

## Claude Desktop 集成

### 1. 配置文件位置

**macOS:**
```
~/Library/Application Support/Claude/claude_desktop_config.json
```

**Windows:**
```
%APPDATA%\Claude\claude_desktop_config.json
```

### 2. 配置示例

```json
{
  "mcpServers": {
    "galaxy-business-tools": {
      "command": "java",
      "args": [
        "-jar",
        "/path/to/galaxy-boot-mcp-example.jar",
        "--galaxy.mcp.transport.type=stdio",
        "--logging.level.cn.com.chinastock.cnf.mcp=INFO"
      ],
      "env": {
        "JAVA_OPTS": "-Xmx256m -Xms128m"
      }
    }
  }
}
```

## 配置生成脚本

创建自动配置生成脚本：

### 1. Bash 脚本 (generate-mcp-config.sh)

```bash
#!/bin/bash

# Galaxy Boot MCP Server 配置生成脚本

SERVER_NAME=${1:-"galaxy-mcp-server"}
JAR_PATH=${2:-"$(pwd)/target/galaxy-boot-mcp-example.jar"}
TRANSPORT_TYPE=${3:-"stdio"}

cat > mcp-config.json << EOF
{
  "mcpServers": {
    "${SERVER_NAME}": {
      "command": "java",
      "args": [
        "-jar",
        "${JAR_PATH}",
        "--galaxy.mcp.transport.type=${TRANSPORT_TYPE}",
        "--galaxy.mcp.enabled=true",
        "--logging.level.cn.com.chinastock.cnf.mcp=INFO"
      ],
      "env": {
        "JAVA_OPTS": "-Xmx512m -Xms256m",
        "SPRING_PROFILES_ACTIVE": "mcp"
      }
    }
  }
}
EOF

echo "MCP 配置文件已生成: mcp-config.json"
echo "服务器名称: ${SERVER_NAME}"
echo "JAR 路径: ${JAR_PATH}"
echo "传输类型: ${TRANSPORT_TYPE}"
```

### 2. PowerShell 脚本 (Generate-McpConfig.ps1)

```powershell
param(
    [string]$ServerName = "galaxy-mcp-server",
    [string]$JarPath = "$(Get-Location)\target\galaxy-boot-mcp-example.jar",
    [string]$TransportType = "stdio"
)

$config = @{
    mcpServers = @{
        $ServerName = @{
            command = "java"
            args = @(
                "-jar",
                $JarPath,
                "--galaxy.mcp.transport.type=$TransportType",
                "--galaxy.mcp.enabled=true",
                "--logging.level.cn.com.chinastock.cnf.mcp=INFO"
            )
            env = @{
                JAVA_OPTS = "-Xmx512m -Xms256m"
                SPRING_PROFILES_ACTIVE = "mcp"
            }
        }
    }
}

$config | ConvertTo-Json -Depth 4 | Out-File -FilePath "mcp-config.json" -Encoding UTF8

Write-Host "MCP 配置文件已生成: mcp-config.json"
Write-Host "服务器名称: $ServerName"
Write-Host "JAR 路径: $JarPath"
Write-Host "传输类型: $TransportType"
```

## 使用示例

### 1. 数学计算

```
用户: 帮我计算 123 * 456 的结果
AI: 我来帮你计算 123 * 456。

[调用 multiply 工具]
参数: a=123, b=456

结果: 56088

123 * 456 = 56088
```

### 2. 用户管理

```
用户: 查询所有用户的统计信息
AI: 我来查询用户统计信息。

[调用 getUserStatistics 工具]

结果显示:
- 总用户数: 4
- 平均年龄: 29.0岁
- 部门分布:
  - IT部: 1人
  - 开发部: 1人
  - 产品部: 1人
  - 销售部: 1人
```

### 3. 复杂业务操作

```
用户: 创建一个新用户，用户名是john，姓名是约翰·史密斯，邮箱是****************，年龄28岁，部门是开发部，职位是高级工程师
AI: 我来为您创建新用户。

[调用 createUser 工具]
参数: {
  "username": "john",
  "name": "约翰·史密斯",
  "email": "<EMAIL>",
  "age": 28,
  "department": "开发部",
  "position": "高级工程师"
}

用户创建成功！
- 用户ID: 5
- 用户名: john
- 姓名: 约翰·史密斯
- 邮箱: <EMAIL>
- 年龄: 28岁
- 部门: 开发部
- 职位: 高级工程师
- 创建时间: 2024-01-01T15:30:00
```

## 故障排除

### 1. 常见问题

**问题**: MCP Server 无法启动
**解决**: 检查 Java 版本和 JAR 文件路径

**问题**: 工具调用失败
**解决**: 检查应用日志和网络连接

**问题**: 配置不生效
**解决**: 重启 AI 客户端并检查配置文件格式

### 2. 调试技巧

1. **启用详细日志**:
```bash
--logging.level.cn.com.chinastock.cnf.mcp=DEBUG
```

2. **检查工具注册**:
```bash
curl http://localhost:8080/actuator/health
```

3. **测试单个工具**:
```bash
curl "http://localhost:8080/api/calculator/add?a=1&b=2"
```

## 最佳实践

1. **性能优化**: 设置合适的 JVM 参数
2. **安全考虑**: 在生产环境中启用认证
3. **监控告警**: 配置应用监控和日志收集
4. **版本管理**: 使用版本化的配置文件
5. **文档维护**: 保持工具描述的准确性

通过以上配置，您就可以在各种 AI 编程工具中使用 Galaxy Boot MCP Server 提供的业务功能了！
