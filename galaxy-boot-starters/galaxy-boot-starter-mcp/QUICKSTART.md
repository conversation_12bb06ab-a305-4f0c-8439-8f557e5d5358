# Galaxy Boot Starter MCP 快速开始

## 5分钟快速体验

### 1. 添加依赖

在您的 Spring Boot 项目中添加依赖：

```xml
<dependency>
    <groupId>cn.com.chinastock</groupId>
    <artifactId>galaxy-boot-starter-mcp</artifactId>
    <version>0.1.1-ALPHA</version>
</dependency>
```

### 2. 创建 Controller

```java
@RestController
@RequestMapping("/api")
public class HelloController {

    @McpTool(
        name = "sayHello",
        description = "向指定的人问候",
        group = "greeting",
        tags = {"greeting", "public"}
    )
    @GetMapping("/hello")
    public String sayHello(@RequestParam String name) {
        return "Hello, " + name + "!";
    }

    @McpTool(
        name = "calculate",
        description = "计算两个数的和",
        group = "math",
        tags = {"math", "public"}
    )
    @GetMapping("/add")
    public int add(@RequestParam int a, @RequestParam int b) {
        return a + b;
    }
}
```

### 3. 配置应用

在 `application.yml` 中添加配置：

```yaml
galaxy:
  mcp:
    enabled: true
    server-name: my-mcp-server
    auto-scan: true
    scan-packages:
      - com.example.controller  # 替换为您的包路径
```

### 4. 启动应用

```bash
mvn spring-boot:run
```

### 5. 查看日志

启动成功后，您会看到类似的日志：

```
Galaxy Boot MCP: Found 2 MCP tools
Galaxy Boot MCP: Registered tool 'sayHello' from method HelloController.sayHello
Galaxy Boot MCP: Registered tool 'calculate' from method HelloController.add
Galaxy Boot MCP: Created 2 Function Callbacks
```

### 6. 测试 REST API

在集成 AI 之前，先测试原始的 REST API：

```bash
# 测试问候接口
curl "http://localhost:8080/api/hello?name=World"
# 输出: Hello, World!

# 测试计算接口
curl "http://localhost:8080/api/add?a=10&b=5"
# 输出: 15
```

## 完整示例

查看 `galaxy-boot-examples/galaxy-boot-mcp-example` 目录中的完整示例应用，包含：

- 用户管理工具（CRUD 操作）
- 计算器工具（数学运算）
- 完整的配置示例
- 详细的使用说明

### 运行示例应用

```bash
cd galaxy-boot-examples/galaxy-boot-mcp-example
mvn spring-boot:run
```

示例应用包含以下工具：

#### 用户管理工具
- `getUserById` - 根据ID获取用户
- `getAllUsers` - 获取所有用户
- `createUser` - 创建新用户
- `searchUsers` - 搜索用户
- `getUserStatistics` - 获取用户统计

#### 计算器工具
- `add` - 加法运算
- `subtract` - 减法运算
- `multiply` - 乘法运算
- `divide` - 除法运算
- `sqrt` - 平方根

## AI 集成示例

当您的应用与 AI 模型集成后，AI 可以这样调用您的工具：

### 对话示例 1：问候功能
**用户**: "向张三问好"
**AI**: 调用 `sayHello` 工具，参数 `name="张三"`
**结果**: "Hello, 张三!"

### 对话示例 2：数学计算
**用户**: "计算 25 + 17"
**AI**: 调用 `calculate` 工具，参数 `a=25, b=17`
**结果**: 42

### 对话示例 3：用户管理
**用户**: "创建一个新用户，用户名是alice，姓名是爱丽丝"
**AI**: 调用 `createUser` 工具，传入用户信息
**结果**: 返回创建的用户对象

## 高级配置

### 工具过滤

只暴露特定标签的工具：

```yaml
galaxy:
  mcp:
    tool:
      tag-filters:
        - public  # 只暴露标记为 public 的工具
```

### 单个工具配置

为特定工具自定义配置：

```yaml
galaxy:
  mcp:
    tool:
      items:
        sayHello:
          description: "自定义的问候工具描述"
        calculate:
          enabled: false  # 禁用计算工具
```

### 安全配置

启用 API 密钥认证：

```yaml
galaxy:
  mcp:
    security:
      enabled: true
      api-key: your-secret-key
      allowed-ips:
        - 127.0.0.1
        - ***********/24
```

## 实际测试方法

### 方法一：直接启动示例应用

1. **编译并启动示例应用**：
```bash
cd galaxy-boot-examples/galaxy-boot-mcp-example
mvn clean compile
mvn spring-boot:run
```

2. **查看启动日志**：
应用启动后，您会看到类似的日志输出：
```
Galaxy Boot MCP: Found 12 MCP tools
Galaxy Boot MCP: Registered tool 'getUserById' from method UserController.getUserById
Galaxy Boot MCP: Registered tool 'createUser' from method UserController.createUser
Galaxy Boot MCP: Registered tool 'add' from method CalculatorController.add
...
Galaxy Boot MCP: Created 12 Function Callbacks
```

3. **测试用户管理API**：
```bash
# 获取所有用户（包含初始测试数据）
curl http://localhost:8080/api/users

# 根据ID获取用户
curl http://localhost:8080/api/users/1

# 根据用户名获取用户
curl http://localhost:8080/api/users/username/admin

# 搜索用户
curl "http://localhost:8080/api/users/search?keyword=管理"

# 获取用户统计信息
curl http://localhost:8080/api/users/statistics

# 根据部门获取用户
curl http://localhost:8080/api/users/department/IT部
```

4. **测试计算器API**：
```bash
# 基本运算
curl "http://localhost:8080/api/calculator/add?a=10&b=5"
curl "http://localhost:8080/api/calculator/subtract?a=10&b=3"
curl "http://localhost:8080/api/calculator/multiply?a=6&b=7"
curl "http://localhost:8080/api/calculator/divide?a=20&b=4"

# 高级运算
curl "http://localhost:8080/api/calculator/power?base=2&exponent=8"
curl "http://localhost:8080/api/calculator/sqrt?number=16"
curl "http://localhost:8080/api/calculator/percentage?part=25&total=100"
```

5. **测试创建用户**：
```bash
curl -X POST http://localhost:8080/api/users \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "name": "测试用户",
    "email": "<EMAIL>",
    "age": 25,
    "department": "测试部",
    "position": "测试工程师"
  }'
```

6. **测试复合计算**：
```bash
curl -X POST http://localhost:8080/api/calculator/complex \
  -H "Content-Type: application/json" \
  -d '{"a": 15, "b": 3}'
```

### 方法二：使用测试脚本

创建一个测试脚本 `test-mcp-example.sh`：

```bash
#!/bin/bash

echo "=== Galaxy Boot MCP Example 测试脚本 ==="
echo

BASE_URL="http://localhost:8080"

echo "1. 测试用户管理功能..."
echo "获取所有用户："
curl -s "$BASE_URL/api/users" | jq '.'
echo

echo "获取用户统计："
curl -s "$BASE_URL/api/users/statistics" | jq '.'
echo

echo "搜索用户："
curl -s "$BASE_URL/api/users/search?keyword=管理" | jq '.'
echo

echo "2. 测试计算器功能..."
echo "加法 10 + 5 = $(curl -s "$BASE_URL/api/calculator/add?a=10&b=5")"
echo "减法 10 - 3 = $(curl -s "$BASE_URL/api/calculator/subtract?a=10&b=3")"
echo "乘法 6 × 7 = $(curl -s "$BASE_URL/api/calculator/multiply?a=6&b=7")"
echo "除法 20 ÷ 4 = $(curl -s "$BASE_URL/api/calculator/divide?a=20&b=4")"
echo "平方根 √16 = $(curl -s "$BASE_URL/api/calculator/sqrt?number=16")"
echo

echo "3. 测试创建用户..."
NEW_USER=$(curl -s -X POST "$BASE_URL/api/users" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser'$(date +%s)'",
    "name": "测试用户",
    "email": "<EMAIL>",
    "age": 25,
    "department": "测试部",
    "position": "测试工程师"
  }')
echo "创建的用户："
echo "$NEW_USER" | jq '.'
echo

echo "4. 测试复合计算..."
COMPLEX_RESULT=$(curl -s -X POST "$BASE_URL/api/calculator/complex" \
  -H "Content-Type: application/json" \
  -d '{"a": 15, "b": 3}')
echo "复合计算结果："
echo "$COMPLEX_RESULT" | jq '.'
echo

echo "=== 测试完成 ==="
```

使用方法：
```bash
chmod +x test-mcp-example.sh
./test-mcp-example.sh
```

### 预期结果

#### 用户管理测试结果
```json
// GET /api/users
[
  {
    "id": 1,
    "username": "admin",
    "name": "管理员",
    "email": "<EMAIL>",
    "age": 30,
    "department": "IT部",
    "position": "系统管理员",
    "createTime": "2024-01-01T10:00:00",
    "updateTime": "2024-01-01T10:00:00"
  }
  // ... 更多用户
]

// GET /api/users/statistics
{
  "totalUsers": 4,
  "averageAge": 29.0,
  "departmentDistribution": {
    "IT部": 1,
    "开发部": 1,
    "产品部": 1,
    "销售部": 1
  }
}
```

#### 计算器测试结果
```bash
# GET /api/calculator/add?a=10&b=5
15

# POST /api/calculator/complex
{
  "input": {"a": 15, "b": 3},
  "add": 18,
  "subtract": 12,
  "multiply": 45,
  "divide": 5.0,
  "success": true
}
```

## 故障排除

### 常见问题及解决方案

1. **应用启动失败**：
   - 检查 Java 版本（需要 Java 21+）
   - 确认端口 8080 未被占用
   - 查看完整的错误日志

2. **工具未注册**：
   - 检查 `@McpTool` 注解是否正确使用
   - 确认包扫描路径配置
   - 查看启动日志中的扫描信息

3. **API 调用失败**：
   - 确认应用已完全启动
   - 检查请求 URL 和参数格式
   - 查看应用日志中的错误信息

### 调试技巧

1. **启用详细日志**：
```yaml
logging:
  level:
    cn.com.chinastock.cnf.mcp: DEBUG
    cn.com.chinastock.cnf.examples.mcp: DEBUG
```

2. **使用 curl 的详细模式**：
```bash
curl -v http://localhost:8080/api/users
```

3. **检查应用状态**：
```bash
# 检查进程
ps aux | grep java

# 检查端口
netstat -an | grep 8080
```

## 常见问题

### Q: 工具没有被发现？
A: 检查以下几点：
1. 确认包路径配置正确
2. 确认 `@McpTool` 注解使用正确
3. 查看启动日志中的工具注册信息

### Q: 参数类型错误？
A: 确保：
1. 参数类型支持 JSON 序列化
2. 使用 `@Valid` 注解进行参数验证
3. 复杂对象使用 `@RequestBody` 注解

### Q: 如何调试？
A: 启用调试日志：
```yaml
logging:
  level:
    cn.com.chinastock.cnf.mcp: DEBUG
```

## 下一步

1. 阅读 [用户手册](docs/UserManual.md) 了解详细功能
2. 查看 [设计文档](DESIGN.md) 了解架构原理
3. 运行示例应用体验完整功能
4. 根据您的业务需求定制工具

## 获取帮助

- 查看项目文档
- 提交 Issue 报告问题
- 参与社区讨论

开始您的 AI 增强之旅吧！🚀