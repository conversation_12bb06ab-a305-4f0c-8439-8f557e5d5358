package cn.com.chinastock.cnf.mcp;

import cn.com.chinastock.cnf.mcp.annotation.McpTool;
import cn.com.chinastock.cnf.mcp.config.McpAutoConfiguration;
import cn.com.chinastock.cnf.mcp.config.McpProperties;
import cn.com.chinastock.cnf.mcp.service.McpToolService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.AutoConfigurations;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * MCP自动配置测试
 * 
 * <AUTHOR> Boot Team
 * @since 0.1.1
 */
class McpAutoConfigurationTest {
    
    private final ApplicationContextRunner contextRunner = new ApplicationContextRunner()
            .withConfiguration(AutoConfigurations.of(McpAutoConfiguration.class));
    
    @Test
    void shouldAutoConfigureWhenMcpEnabled() {
        contextRunner
                .withPropertyValues("galaxy.mcp.enabled=true")
                .withUserConfiguration(TestController.class)
                .run(context -> {
                    assertThat(context).hasSingleBean(McpProperties.class);
                    assertThat(context).hasSingleBean(McpToolService.class);
                });
    }
    
    @Test
    void shouldNotAutoConfigureWhenMcpDisabled() {
        contextRunner
                .withPropertyValues("galaxy.mcp.enabled=false")
                .run(context -> {
                    assertThat(context).doesNotHaveBean(McpToolService.class);
                });
    }
    
    @Test
    void shouldConfigureWithCustomProperties() {
        contextRunner
                .withPropertyValues(
                        "galaxy.mcp.enabled=true",
                        "galaxy.mcp.server-name=test-server",
                        "galaxy.mcp.server-version=2.0.0",
                        "galaxy.mcp.tool.timeout-seconds=60"
                )
                .withUserConfiguration(TestController.class)
                .run(context -> {
                    McpProperties properties = context.getBean(McpProperties.class);
                    assertThat(properties.getServerName()).isEqualTo("test-server");
                    assertThat(properties.getServerVersion()).isEqualTo("2.0.0");
                    assertThat(properties.getTool().getTimeoutSeconds()).isEqualTo(60);
                });
    }
    
    /**
     * 测试用的Controller
     */
    @RestController
    static class TestController {
        
        @McpTool(
            name = "greet",
            description = "Greet a person with a custom message",
            group = "greeting",
            tags = {"test", "greeting"}
        )
        @GetMapping("/greet")
        public String greet(@RequestParam String name) {
            return "Hello, " + name + "!";
        }
        
        @McpTool(
            name = "calculate",
            description = "Calculate the sum of two numbers",
            group = "math",
            tags = {"test", "math"}
        )
        @GetMapping("/calculate")
        public int calculate(@RequestParam int a, @RequestParam int b) {
            return a + b;
        }
    }
}
