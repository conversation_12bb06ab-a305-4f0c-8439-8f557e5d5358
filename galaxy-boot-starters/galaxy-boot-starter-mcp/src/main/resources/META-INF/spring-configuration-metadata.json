{"groups": [{"name": "galaxy.mcp", "type": "cn.com.chinastock.cnf.mcp.config.McpProperties", "description": "Galaxy Boot MCP configuration properties."}, {"name": "galaxy.mcp.tool", "type": "cn.com.chinastock.cnf.mcp.config.McpProperties$ToolConfig", "description": "MCP tool configuration properties."}, {"name": "galaxy.mcp.transport", "type": "cn.com.chinastock.cnf.mcp.config.McpProperties$TransportConfig", "description": "MCP transport configuration properties."}, {"name": "galaxy.mcp.security", "type": "cn.com.chinastock.cnf.mcp.config.McpProperties$SecurityConfig", "description": "MCP security configuration properties."}], "properties": [{"name": "galaxy.mcp.enabled", "type": "java.lang.Bo<PERSON>an", "description": "Whether to enable MCP functionality.", "defaultValue": true}, {"name": "galaxy.mcp.server-name", "type": "java.lang.String", "description": "MCP server name for identification.", "defaultValue": "galaxy-mcp-server"}, {"name": "galaxy.mcp.server-version", "type": "java.lang.String", "description": "MCP server version.", "defaultValue": "1.0.0"}, {"name": "galaxy.mcp.server-description", "type": "java.lang.String", "description": "MCP server description.", "defaultValue": "Galaxy Boot MCP Server"}, {"name": "galaxy.mcp.scan-packages", "type": "java.util.List<java.lang.String>", "description": "Package paths to scan for @McpTool annotated methods."}, {"name": "galaxy.mcp.auto-scan", "type": "java.lang.Bo<PERSON>an", "description": "Whether to enable automatic scanning for MCP tools.", "defaultValue": true}, {"name": "galaxy.mcp.tool.enabled", "type": "java.lang.Bo<PERSON>an", "description": "Whether to enable tool functionality.", "defaultValue": true}, {"name": "galaxy.mcp.tool.timeout-seconds", "type": "java.lang.Integer", "description": "Tool response timeout in seconds.", "defaultValue": 30}, {"name": "galaxy.mcp.tool.group-filters", "type": "java.util.List<java.lang.String>", "description": "Tool group filters, only expose tools in specified groups."}, {"name": "galaxy.mcp.tool.tag-filters", "type": "java.util.List<java.lang.String>", "description": "Tool tag filters, only expose tools with specified tags."}, {"name": "galaxy.mcp.transport.type", "type": "java.lang.String", "description": "Transport type: webmvc, webflux, stdio.", "defaultValue": "webmvc"}, {"name": "galaxy.mcp.transport.sse-endpoint", "type": "java.lang.String", "description": "SSE endpoint path for web transport.", "defaultValue": "/mcp/sse"}, {"name": "galaxy.mcp.transport.message-endpoint", "type": "java.lang.String", "description": "Message endpoint path for web transport.", "defaultValue": "/mcp/message"}, {"name": "galaxy.mcp.transport.base-url", "type": "java.lang.String", "description": "Base URL prefix for endpoints.", "defaultValue": ""}, {"name": "galaxy.mcp.security.enabled", "type": "java.lang.Bo<PERSON>an", "description": "Whether to enable security authentication.", "defaultValue": false}, {"name": "galaxy.mcp.security.api-key", "type": "java.lang.String", "description": "API key for authentication."}, {"name": "galaxy.mcp.security.allowed-ips", "type": "java.util.List<java.lang.String>", "description": "List of allowed IP addresses."}], "hints": [{"name": "galaxy.mcp.transport.type", "values": [{"value": "webmvc", "description": "Spring MVC based SSE transport."}, {"value": "webflux", "description": "Spring WebFlux based reactive SSE transport."}, {"value": "stdio", "description": "Standard input/output transport."}]}]}