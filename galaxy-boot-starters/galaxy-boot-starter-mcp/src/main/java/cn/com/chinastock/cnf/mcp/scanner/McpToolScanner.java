package cn.com.chinastock.cnf.mcp.scanner;

import cn.com.chinastock.cnf.mcp.annotation.McpTool;
import cn.com.chinastock.cnf.mcp.config.McpProperties;
import cn.com.chinastock.cnf.mcp.model.McpToolInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RestController;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * MCP工具扫描器
 * 
 * 扫描Spring容器中带有@McpTool注解的方法，并将其注册为MCP工具
 * 
 * <AUTHOR> Boot Team
 * @since 0.1.1
 */
public class McpToolScanner implements BeanPostProcessor, ApplicationContextAware {
    
    private static final Logger logger = LoggerFactory.getLogger(McpToolScanner.class);
    
    private final McpProperties mcpProperties;
    private final Set<McpToolInfo> discoveredTools = ConcurrentHashMap.newKeySet();
    private ApplicationContext applicationContext;
    
    public McpToolScanner(McpProperties mcpProperties) {
        this.mcpProperties = mcpProperties;
    }
    
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
    
    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        Class<?> beanClass = bean.getClass();
        
        // 只处理Controller类
        if (!isController(beanClass)) {
            return bean;
        }
        
        // 检查包路径过滤
        if (!isInScanPackages(beanClass)) {
            return bean;
        }
        
        // 扫描方法
        scanMethods(bean, beanClass);
        
        return bean;
    }
    
    /**
     * 检查是否为Controller类
     */
    private boolean isController(Class<?> clazz) {
        return AnnotationUtils.findAnnotation(clazz, Controller.class) != null ||
               AnnotationUtils.findAnnotation(clazz, RestController.class) != null;
    }
    
    /**
     * 检查类是否在扫描包路径中
     */
    private boolean isInScanPackages(Class<?> clazz) {
        if (!mcpProperties.isAutoScan()) {
            return false;
        }
        
        List<String> scanPackages = mcpProperties.getScanPackages();
        if (scanPackages.isEmpty()) {
            // 如果没有指定扫描包，则扫描所有包
            return true;
        }
        
        String className = clazz.getName();
        return scanPackages.stream().anyMatch(className::startsWith);
    }
    
    /**
     * 扫描类中的方法
     */
    private void scanMethods(Object bean, Class<?> beanClass) {
        Method[] methods = beanClass.getDeclaredMethods();
        
        for (Method method : methods) {
            McpTool mcpTool = AnnotationUtils.findAnnotation(method, McpTool.class);
            if (mcpTool != null && mcpTool.enabled()) {
                try {
                    McpToolInfo toolInfo = createToolInfo(bean, method, mcpTool);
                    if (isToolEnabled(toolInfo)) {
                        discoveredTools.add(toolInfo);
                        logger.info("Discovered MCP tool: {} in class {}", 
                                   toolInfo.getName(), beanClass.getSimpleName());
                    }
                } catch (Exception e) {
                    logger.error("Failed to create tool info for method {} in class {}", 
                                method.getName(), beanClass.getSimpleName(), e);
                }
            }
        }
    }
    
    /**
     * 创建工具信息
     */
    private McpToolInfo createToolInfo(Object bean, Method method, McpTool mcpTool) {
        String toolName = mcpTool.name().isEmpty() ? method.getName() : mcpTool.name();
        String description = mcpTool.description();
        String group = mcpTool.group();
        String[] tags = mcpTool.tags();
        String responseMimeType = mcpTool.responseMimeType();
        
        // 检查配置覆盖
        McpProperties.ToolItemConfig itemConfig = mcpProperties.getTool().getItems().get(toolName);
        if (itemConfig != null) {
            if (itemConfig.getDescription() != null) {
                description = itemConfig.getDescription();
            }
            if (itemConfig.getResponseMimeType() != null) {
                responseMimeType = itemConfig.getResponseMimeType();
            }
        }
        
        return new McpToolInfo(
            toolName,
            description,
            group,
            tags,
            responseMimeType,
            bean,
            method
        );
    }
    
    /**
     * 检查工具是否启用
     */
    private boolean isToolEnabled(McpToolInfo toolInfo) {
        McpProperties.ToolConfig toolConfig = mcpProperties.getTool();
        
        // 检查全局工具开关
        if (!toolConfig.isEnabled()) {
            return false;
        }
        
        // 检查单个工具配置
        McpProperties.ToolItemConfig itemConfig = toolConfig.getItems().get(toolInfo.getName());
        if (itemConfig != null && !itemConfig.isEnabled()) {
            return false;
        }
        
        // 检查分组过滤器
        List<String> groupFilters = toolConfig.getGroupFilters();
        if (!groupFilters.isEmpty() && !groupFilters.contains(toolInfo.getGroup())) {
            return false;
        }
        
        // 检查标签过滤器
        List<String> tagFilters = toolConfig.getTagFilters();
        if (!tagFilters.isEmpty()) {
            boolean hasMatchingTag = false;
            for (String tag : toolInfo.getTags()) {
                if (tagFilters.contains(tag)) {
                    hasMatchingTag = true;
                    break;
                }
            }
            if (!hasMatchingTag) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 获取发现的工具列表
     */
    public List<McpToolInfo> getDiscoveredTools() {
        return new ArrayList<>(discoveredTools);
    }
    
    /**
     * 清空发现的工具
     */
    public void clearDiscoveredTools() {
        discoveredTools.clear();
    }
}
