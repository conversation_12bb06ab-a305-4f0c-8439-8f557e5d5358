package cn.com.chinastock.cnf.mcp.converter;

import cn.com.chinastock.cnf.mcp.config.McpProperties;
import cn.com.chinastock.cnf.mcp.model.McpToolInfo;
import cn.com.chinastock.cnf.mcp.util.JsonSchemaGenerator;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.model.function.FunctionCallback;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.*;
import java.util.function.Function;

/**
 * Controller方法到MCP工具的转换器
 * 
 * 将带有@McpTool注解的Controller方法转换为MCP工具规范
 * 
 * <AUTHOR> Boot Team
 * @since 0.1.1
 */
public class ControllerToolConverter {
    
    private static final Logger logger = LoggerFactory.getLogger(ControllerToolConverter.class);
    
    private final McpProperties mcpProperties;
    private final ObjectMapper objectMapper;
    private final JsonSchemaGenerator schemaGenerator;
    
    public ControllerToolConverter(McpProperties mcpProperties, ObjectMapper objectMapper) {
        this.mcpProperties = mcpProperties;
        this.objectMapper = objectMapper;
        this.schemaGenerator = new JsonSchemaGenerator(objectMapper);
    }
    
    /**
     * 将工具信息转换为Spring AI Function Callback
     */
    public FunctionCallback convertToFunctionCallback(McpToolInfo toolInfo) {
        try {
            String functionName = toolInfo.getName();
            String description = toolInfo.getDescription();

            // 生成输入类型的 JSON Schema
            String inputSchema = generateInputSchemaString(toolInfo.getMethod());

            // 创建Function Callback，使用 Map 作为输入类型
            Function<Map<String, Object>, Object> function = (input) -> executeToolMethodInternal(toolInfo, input);

            return FunctionCallback.builder()
                    .function(functionName, function)
                    .description(description)
                    .inputType(Map.class)  // 使用 Map 作为输入类型
                    .build();

        } catch (Exception e) {
            logger.error("Failed to convert tool: {}", toolInfo.getName(), e);
            throw new RuntimeException("Failed to convert tool: " + toolInfo.getName(), e);
        }
    }
    


    
    /**
     * 检查是否为特殊参数类型（不需要包含在Schema中）
     */
    private boolean isSpecialParameterType(Class<?> paramType) {
        return paramType == HttpServletRequest.class ||
               paramType == HttpServletResponse.class ||
               paramType == HttpSession.class ||
               paramType.getName().startsWith("org.springframework.") ||
               paramType.getName().startsWith("jakarta.servlet.") ||
               paramType.getName().startsWith("javax.servlet.");
    }
    
    /**
     * 检查是否为必需参数
     */
    private boolean isRequiredParameter(Parameter parameter) {
        // 如果有@RequestParam注解且required=true，则为必需参数
        RequestParam requestParam = parameter.getAnnotation(RequestParam.class);
        if (requestParam != null) {
            return requestParam.required();
        }
        
        // 如果有@RequestBody注解，则为必需参数
        RequestBody requestBody = parameter.getAnnotation(RequestBody.class);
        if (requestBody != null) {
            return requestBody.required();
        }
        
        // 基本类型默认为必需参数
        return parameter.getType().isPrimitive();
    }

    /**
     * 生成输入参数的JSON Schema字符串
     */
    private String generateInputSchemaString(Method method) {
        try {
            Map<String, Object> schema = new HashMap<>();
            schema.put("type", "object");

            Map<String, Object> properties = new HashMap<>();
            List<String> required = new ArrayList<>();

            Parameter[] parameters = method.getParameters();
            for (Parameter parameter : parameters) {
                String paramName = parameter.getName();
                Class<?> paramType = parameter.getType();

                // 跳过特殊参数类型
                if (isSpecialParameterType(paramType)) {
                    continue;
                }

                // 生成参数Schema
                Map<String, Object> paramSchema = generateParameterSchema(paramType);
                properties.put(paramName, paramSchema);

                // 检查是否为必需参数
                if (isRequiredParameter(parameter)) {
                    required.add(paramName);
                }
            }

            schema.put("properties", properties);
            if (!required.isEmpty()) {
                schema.put("required", required);
            }

            return objectMapper.writeValueAsString(schema);

        } catch (Exception e) {
            logger.error("Failed to generate input schema for method: {}", method.getName(), e);
            // 返回一个基本的 schema
            return "{\"type\":\"object\",\"properties\":{}}";
        }
    }

    /**
     * 生成参数的Schema
     */
    private Map<String, Object> generateParameterSchema(Class<?> paramType) {
        Map<String, Object> schema = new HashMap<>();

        if (paramType == String.class) {
            schema.put("type", "string");
        } else if (paramType == Integer.class || paramType == int.class) {
            schema.put("type", "integer");
        } else if (paramType == Long.class || paramType == long.class) {
            schema.put("type", "integer");
        } else if (paramType == Double.class || paramType == double.class ||
                   paramType == Float.class || paramType == float.class) {
            schema.put("type", "number");
        } else if (paramType == Boolean.class || paramType == boolean.class) {
            schema.put("type", "boolean");
        } else {
            // 复杂对象类型
            schema.put("type", "object");
        }

        return schema;
    }



    /**
     * 执行工具方法（公共方法，供 MCP Controller 调用）
     */
    public Object executeToolMethod(McpToolInfo toolInfo, Object input) {
        return executeToolMethodInternal(toolInfo, input);
    }

    /**
     * 执行工具方法（内部实现）
     */
    private Object executeToolMethodInternal(McpToolInfo toolInfo, Object input) {
        try {
            Method method = toolInfo.getMethod();
            Object bean = toolInfo.getBean();

            // 准备方法参数
            Object[] args = prepareMethodArguments(method, input);

            // 执行方法
            Object result = method.invoke(bean, args);

            // 处理返回结果
            return processMethodResult(toolInfo, result);

        } catch (Exception e) {
            logger.error("Failed to execute tool method: {}", toolInfo.getName(), e);
            throw new RuntimeException("Tool execution failed: " + e.getMessage(), e);
        }
    }
    
    /**
     * 准备方法参数
     */
    private Object[] prepareMethodArguments(Method method, Object input) {
        Map<String, Object> arguments;
        if (input instanceof Map) {
            arguments = (Map<String, Object>) input;
        } else {
            // 如果输入不是Map，尝试转换为Map
            arguments = objectMapper.convertValue(input, Map.class);
        }
        Parameter[] parameters = method.getParameters();
        Object[] args = new Object[parameters.length];
        
        for (int i = 0; i < parameters.length; i++) {
            Parameter parameter = parameters[i];
            String paramName = parameter.getName();
            Class<?> paramType = parameter.getType();
            
            // 跳过特殊参数类型
            if (isSpecialParameterType(paramType)) {
                args[i] = null;
                continue;
            }
            
            // 从请求参数中获取值
            Object value = arguments.get(paramName);
            if (value != null) {
                // 类型转换
                args[i] = convertArgumentValue(value, paramType);
            } else {
                // 使用默认值
                args[i] = getDefaultValue(paramType);
            }
        }
        
        return args;
    }
    
    /**
     * 转换参数值类型
     */
    private Object convertArgumentValue(Object value, Class<?> targetType) {
        try {
            if (targetType.isAssignableFrom(value.getClass())) {
                return value;
            }
            
            // 使用Jackson进行类型转换
            return objectMapper.convertValue(value, targetType);
            
        } catch (Exception e) {
            logger.warn("Failed to convert argument value {} to type {}", value, targetType.getName());
            return getDefaultValue(targetType);
        }
    }
    
    /**
     * 获取类型的默认值
     */
    private Object getDefaultValue(Class<?> type) {
        if (type.isPrimitive()) {
            if (type == boolean.class) return false;
            if (type == byte.class) return (byte) 0;
            if (type == short.class) return (short) 0;
            if (type == int.class) return 0;
            if (type == long.class) return 0L;
            if (type == float.class) return 0.0f;
            if (type == double.class) return 0.0d;
            if (type == char.class) return '\0';
        }
        return null;
    }
    
    /**
     * 处理方法返回结果
     */
    private Object processMethodResult(McpToolInfo toolInfo, Object result) {
        try {
            // 处理ResponseEntity
            if (result instanceof ResponseEntity) {
                ResponseEntity<?> responseEntity = (ResponseEntity<?>) result;
                result = responseEntity.getBody();
            }

            return result;

        } catch (Exception e) {
            logger.error("Failed to process method result for tool: {}", toolInfo.getName(), e);
            throw new RuntimeException("Failed to process result: " + e.getMessage(), e);
        }
    }
    

}
