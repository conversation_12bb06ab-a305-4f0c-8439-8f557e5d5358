package cn.com.chinastock.cnf.mcp.model;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Objects;

/**
 * MCP工具信息
 * 
 * 封装从@McpTool注解扫描到的工具信息
 * 
 * <AUTHOR> Boot Team
 * @since 0.1.1
 */
public class McpToolInfo {
    
    /**
     * 工具名称
     */
    private final String name;
    
    /**
     * 工具描述
     */
    private final String description;
    
    /**
     * 工具分组
     */
    private final String group;
    
    /**
     * 工具标签
     */
    private final String[] tags;
    
    /**
     * 响应MIME类型
     */
    private final String responseMimeType;
    
    /**
     * Bean实例
     */
    private final Object bean;
    
    /**
     * 方法对象
     */
    private final Method method;
    
    public McpToolInfo(String name, String description, String group, String[] tags, 
                       String responseMimeType, Object bean, Method method) {
        this.name = name;
        this.description = description;
        this.group = group;
        this.tags = tags != null ? tags.clone() : new String[0];
        this.responseMimeType = responseMimeType;
        this.bean = bean;
        this.method = method;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public String getGroup() {
        return group;
    }
    
    public String[] getTags() {
        return tags.clone();
    }
    
    public String getResponseMimeType() {
        return responseMimeType;
    }
    
    public Object getBean() {
        return bean;
    }
    
    public Method getMethod() {
        return method;
    }
    
    /**
     * 获取方法的完整签名
     */
    public String getMethodSignature() {
        return method.getDeclaringClass().getSimpleName() + "." + method.getName();
    }
    
    /**
     * 检查是否有指定标签
     */
    public boolean hasTag(String tag) {
        return Arrays.asList(tags).contains(tag);
    }
    
    /**
     * 检查是否属于指定分组
     */
    public boolean belongsToGroup(String group) {
        return Objects.equals(this.group, group);
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        McpToolInfo that = (McpToolInfo) o;
        return Objects.equals(name, that.name) &&
               Objects.equals(bean, that.bean) &&
               Objects.equals(method, that.method);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(name, bean, method);
    }
    
    @Override
    public String toString() {
        return "McpToolInfo{" +
               "name='" + name + '\'' +
               ", description='" + description + '\'' +
               ", group='" + group + '\'' +
               ", tags=" + Arrays.toString(tags) +
               ", responseMimeType='" + responseMimeType + '\'' +
               ", methodSignature='" + getMethodSignature() + '\'' +
               '}';
    }
}
