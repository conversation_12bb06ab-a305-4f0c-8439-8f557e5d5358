package cn.com.chinastock.cnf.mcp.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * MCP配置属性
 * 
 * <AUTHOR> Boot Team
 * @since 0.1.1
 */
@ConfigurationProperties(prefix = "galaxy.mcp")
public class McpProperties {
    
    /**
     * 是否启用MCP功能
     */
    private boolean enabled = true;
    
    /**
     * MCP服务器名称
     */
    private String serverName = "galaxy-mcp-server";
    
    /**
     * MCP服务器版本
     */
    private String serverVersion = "1.0.0";
    
    /**
     * MCP服务器描述
     */
    private String serverDescription = "Galaxy Boot MCP Server";
    
    /**
     * 扫描的包路径，用于发现@McpTool注解的方法
     */
    private List<String> scanPackages = new ArrayList<>();
    
    /**
     * 是否启用自动扫描
     */
    private boolean autoScan = true;
    
    /**
     * 工具配置
     */
    private ToolConfig tool = new ToolConfig();
    
    /**
     * 传输配置
     */
    private TransportConfig transport = new TransportConfig();
    
    /**
     * 安全配置
     */
    private SecurityConfig security = new SecurityConfig();
    
    // Getters and Setters
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    public String getServerName() {
        return serverName;
    }
    
    public void setServerName(String serverName) {
        this.serverName = serverName;
    }
    
    public String getServerVersion() {
        return serverVersion;
    }
    
    public void setServerVersion(String serverVersion) {
        this.serverVersion = serverVersion;
    }
    
    public String getServerDescription() {
        return serverDescription;
    }
    
    public void setServerDescription(String serverDescription) {
        this.serverDescription = serverDescription;
    }
    
    public List<String> getScanPackages() {
        return scanPackages;
    }
    
    public void setScanPackages(List<String> scanPackages) {
        this.scanPackages = scanPackages;
    }
    
    public boolean isAutoScan() {
        return autoScan;
    }
    
    public void setAutoScan(boolean autoScan) {
        this.autoScan = autoScan;
    }
    
    public ToolConfig getTool() {
        return tool;
    }
    
    public void setTool(ToolConfig tool) {
        this.tool = tool;
    }
    
    public TransportConfig getTransport() {
        return transport;
    }
    
    public void setTransport(TransportConfig transport) {
        this.transport = transport;
    }
    
    public SecurityConfig getSecurity() {
        return security;
    }
    
    public void setSecurity(SecurityConfig security) {
        this.security = security;
    }
    
    /**
     * 工具配置
     */
    public static class ToolConfig {
        /**
         * 是否启用工具功能
         */
        private boolean enabled = true;
        
        /**
         * 工具响应超时时间（秒）
         */
        private int timeoutSeconds = 30;
        
        /**
         * 工具分组过滤器，只暴露指定分组的工具
         */
        private List<String> groupFilters = new ArrayList<>();
        
        /**
         * 工具标签过滤器，只暴露包含指定标签的工具
         */
        private List<String> tagFilters = new ArrayList<>();
        
        /**
         * 每个工具的自定义配置
         */
        private Map<String, ToolItemConfig> items = new HashMap<>();
        
        // Getters and Setters
        public boolean isEnabled() {
            return enabled;
        }
        
        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }
        
        public int getTimeoutSeconds() {
            return timeoutSeconds;
        }
        
        public void setTimeoutSeconds(int timeoutSeconds) {
            this.timeoutSeconds = timeoutSeconds;
        }
        
        public List<String> getGroupFilters() {
            return groupFilters;
        }
        
        public void setGroupFilters(List<String> groupFilters) {
            this.groupFilters = groupFilters;
        }
        
        public List<String> getTagFilters() {
            return tagFilters;
        }
        
        public void setTagFilters(List<String> tagFilters) {
            this.tagFilters = tagFilters;
        }
        
        public Map<String, ToolItemConfig> getItems() {
            return items;
        }
        
        public void setItems(Map<String, ToolItemConfig> items) {
            this.items = items;
        }
    }
    
    /**
     * 单个工具配置
     */
    public static class ToolItemConfig {
        /**
         * 是否启用此工具
         */
        private boolean enabled = true;
        
        /**
         * 工具描述覆盖
         */
        private String description;
        
        /**
         * 响应MIME类型覆盖
         */
        private String responseMimeType;
        
        // Getters and Setters
        public boolean isEnabled() {
            return enabled;
        }
        
        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }
        
        public String getDescription() {
            return description;
        }
        
        public void setDescription(String description) {
            this.description = description;
        }
        
        public String getResponseMimeType() {
            return responseMimeType;
        }
        
        public void setResponseMimeType(String responseMimeType) {
            this.responseMimeType = responseMimeType;
        }
    }
    
    /**
     * 传输配置
     */
    public static class TransportConfig {
        /**
         * 传输类型：webmvc, webflux, stdio
         */
        private String type = "webmvc";
        
        /**
         * SSE端点路径
         */
        private String sseEndpoint = "/mcp/sse";
        
        /**
         * 消息端点路径
         */
        private String messageEndpoint = "/mcp/message";
        
        /**
         * 基础URL前缀
         */
        private String baseUrl = "";
        
        // Getters and Setters
        public String getType() {
            return type;
        }
        
        public void setType(String type) {
            this.type = type;
        }
        
        public String getSseEndpoint() {
            return sseEndpoint;
        }
        
        public void setSseEndpoint(String sseEndpoint) {
            this.sseEndpoint = sseEndpoint;
        }
        
        public String getMessageEndpoint() {
            return messageEndpoint;
        }
        
        public void setMessageEndpoint(String messageEndpoint) {
            this.messageEndpoint = messageEndpoint;
        }
        
        public String getBaseUrl() {
            return baseUrl;
        }
        
        public void setBaseUrl(String baseUrl) {
            this.baseUrl = baseUrl;
        }
    }
    
    /**
     * 安全配置
     */
    public static class SecurityConfig {
        /**
         * 是否启用安全认证
         */
        private boolean enabled = false;
        
        /**
         * API密钥
         */
        private String apiKey;
        
        /**
         * 允许的IP地址列表
         */
        private List<String> allowedIps = new ArrayList<>();
        
        // Getters and Setters
        public boolean isEnabled() {
            return enabled;
        }
        
        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }
        
        public String getApiKey() {
            return apiKey;
        }
        
        public void setApiKey(String apiKey) {
            this.apiKey = apiKey;
        }
        
        public List<String> getAllowedIps() {
            return allowedIps;
        }
        
        public void setAllowedIps(List<String> allowedIps) {
            this.allowedIps = allowedIps;
        }
    }
}
