package cn.com.chinastock.cnf.mcp.service;

import cn.com.chinastock.cnf.mcp.config.McpProperties;
import cn.com.chinastock.cnf.mcp.converter.ControllerToolConverter;
import cn.com.chinastock.cnf.mcp.model.McpToolInfo;
import cn.com.chinastock.cnf.mcp.scanner.McpToolScanner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.model.function.FunctionCallback;
import org.springframework.beans.factory.InitializingBean;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * MCP工具服务
 * 
 * 管理MCP工具的发现、转换和注册
 * 
 * <AUTHOR> Boot Team
 * @since 0.1.1
 */
public class McpToolService implements InitializingBean {
    
    private static final Logger logger = LoggerFactory.getLogger(McpToolService.class);
    
    private final McpToolScanner mcpToolScanner;
    private final ControllerToolConverter controllerToolConverter;
    private final McpProperties mcpProperties;
    
    public McpToolService(McpToolScanner mcpToolScanner,
                         ControllerToolConverter controllerToolConverter,
                         McpProperties mcpProperties) {
        this.mcpToolScanner = mcpToolScanner;
        this.controllerToolConverter = controllerToolConverter;
        this.mcpProperties = mcpProperties;
    }
    
    @Override
    public void afterPropertiesSet() throws Exception {
        logger.info("Galaxy Boot MCP: MCP Tool Service initialized");
        logConfiguration();
    }
    
    /**
     * 记录配置信息
     */
    private void logConfiguration() {
        logger.info("Galaxy Boot MCP: Server Name: {}", mcpProperties.getServerName());
        logger.info("Galaxy Boot MCP: Server Version: {}", mcpProperties.getServerVersion());
        logger.info("Galaxy Boot MCP: Auto Scan: {}", mcpProperties.isAutoScan());
        logger.info("Galaxy Boot MCP: Scan Packages: {}", mcpProperties.getScanPackages());
        logger.info("Galaxy Boot MCP: Tool Enabled: {}", mcpProperties.getTool().isEnabled());
        logger.info("Galaxy Boot MCP: Transport Type: {}", mcpProperties.getTransport().getType());
    }
    
    /**
     * 获取发现的工具列表
     */
    public List<McpToolInfo> getDiscoveredTools() {
        return mcpToolScanner.getDiscoveredTools();
    }
    
    /**
     * 将发现的工具转换为Function Callbacks
     */
    public List<FunctionCallback> convertToFunctionCallbacks() {
        List<McpToolInfo> discoveredTools = getDiscoveredTools();
        List<FunctionCallback> functionCallbacks = new ArrayList<>();

        for (McpToolInfo toolInfo : discoveredTools) {
            try {
                FunctionCallback functionCallback =
                    controllerToolConverter.convertToFunctionCallback(toolInfo);
                functionCallbacks.add(functionCallback);

                logger.debug("Galaxy Boot MCP: Converted tool '{}' to Function Callback",
                            toolInfo.getName());

            } catch (Exception e) {
                logger.error("Galaxy Boot MCP: Failed to convert tool '{}' to Function Callback",
                            toolInfo.getName(), e);
            }
        }

        return functionCallbacks;
    }
    
    /**
     * 根据名称查找工具
     */
    public McpToolInfo findToolByName(String name) {
        return getDiscoveredTools().stream()
                .filter(tool -> tool.getName().equals(name))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 根据分组查找工具
     */
    public List<McpToolInfo> findToolsByGroup(String group) {
        return getDiscoveredTools().stream()
                .filter(tool -> tool.belongsToGroup(group))
                .collect(Collectors.toList());
    }
    
    /**
     * 根据标签查找工具
     */
    public List<McpToolInfo> findToolsByTag(String tag) {
        return getDiscoveredTools().stream()
                .filter(tool -> tool.hasTag(tag))
                .collect(Collectors.toList());
    }
    
    /**
     * 获取工具统计信息
     */
    public ToolStatistics getToolStatistics() {
        List<McpToolInfo> tools = getDiscoveredTools();
        
        long totalTools = tools.size();
        long enabledTools = tools.stream()
                .filter(tool -> isToolEnabled(tool))
                .count();
        
        long groupCount = tools.stream()
                .map(McpToolInfo::getGroup)
                .distinct()
                .count();
        
        long tagCount = tools.stream()
                .flatMap(tool -> java.util.Arrays.stream(tool.getTags()))
                .distinct()
                .count();
        
        return new ToolStatistics(totalTools, enabledTools, groupCount, tagCount);
    }
    
    /**
     * 检查工具是否启用
     */
    private boolean isToolEnabled(McpToolInfo toolInfo) {
        McpProperties.ToolConfig toolConfig = mcpProperties.getTool();
        
        // 检查全局开关
        if (!toolConfig.isEnabled()) {
            return false;
        }
        
        // 检查单个工具配置
        McpProperties.ToolItemConfig itemConfig = toolConfig.getItems().get(toolInfo.getName());
        if (itemConfig != null) {
            return itemConfig.isEnabled();
        }
        
        return true;
    }
    
    /**
     * 刷新工具列表
     */
    public void refreshTools() {
        logger.info("Galaxy Boot MCP: Refreshing MCP tools");
        mcpToolScanner.clearDiscoveredTools();
        // 注意：实际的重新扫描会在下次Bean后处理时自动进行
    }
    
    /**
     * 工具统计信息
     */
    public static class ToolStatistics {
        private final long totalTools;
        private final long enabledTools;
        private final long groupCount;
        private final long tagCount;
        
        public ToolStatistics(long totalTools, long enabledTools, long groupCount, long tagCount) {
            this.totalTools = totalTools;
            this.enabledTools = enabledTools;
            this.groupCount = groupCount;
            this.tagCount = tagCount;
        }
        
        public long getTotalTools() {
            return totalTools;
        }
        
        public long getEnabledTools() {
            return enabledTools;
        }
        
        public long getGroupCount() {
            return groupCount;
        }
        
        public long getTagCount() {
            return tagCount;
        }
        
        @Override
        public String toString() {
            return String.format("ToolStatistics{totalTools=%d, enabledTools=%d, groupCount=%d, tagCount=%d}",
                    totalTools, enabledTools, groupCount, tagCount);
        }
    }
}
