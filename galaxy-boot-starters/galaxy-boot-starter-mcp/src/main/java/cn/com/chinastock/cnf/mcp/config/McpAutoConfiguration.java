package cn.com.chinastock.cnf.mcp.config;

import cn.com.chinastock.cnf.mcp.converter.ControllerToolConverter;
import cn.com.chinastock.cnf.mcp.model.McpToolInfo;
import cn.com.chinastock.cnf.mcp.scanner.McpToolScanner;
import cn.com.chinastock.cnf.mcp.service.McpToolService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.model.function.FunctionCallback;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * MCP自动配置类
 * 
 * 自动配置MCP相关的Bean和功能
 * 
 * <AUTHOR> Boot Team
 * @since 0.1.1
 */
@AutoConfiguration
@ConditionalOnClass({FunctionCallback.class})
@ConditionalOnProperty(prefix = "galaxy.mcp", name = "enabled", havingValue = "true", matchIfMissing = true)
@EnableConfigurationProperties(McpProperties.class)
public class McpAutoConfiguration {
    
    private static final Logger logger = LoggerFactory.getLogger(McpAutoConfiguration.class);
    
    public McpAutoConfiguration() {
        logger.info("Galaxy Boot MCP: Initializing MCP Auto Configuration");
    }
    
    /**
     * 配置MCP工具扫描器
     */
    @Bean
    @ConditionalOnMissingBean
    public McpToolScanner mcpToolScanner(McpProperties mcpProperties) {
        logger.info("Galaxy Boot MCP: Creating MCP Tool Scanner");
        return new McpToolScanner(mcpProperties);
    }
    
    /**
     * 配置Controller工具转换器
     */
    @Bean
    @ConditionalOnMissingBean
    public ControllerToolConverter controllerToolConverter(McpProperties mcpProperties, 
                                                          ObjectMapper objectMapper) {
        logger.info("Galaxy Boot MCP: Creating Controller Tool Converter");
        return new ControllerToolConverter(mcpProperties, objectMapper);
    }
    
    /**
     * 配置MCP工具服务
     */
    @Bean
    @ConditionalOnMissingBean
    public McpToolService mcpToolService(McpToolScanner mcpToolScanner,
                                        ControllerToolConverter controllerToolConverter,
                                        McpProperties mcpProperties) {
        logger.info("Galaxy Boot MCP: Creating MCP Tool Service");
        return new McpToolService(mcpToolScanner, controllerToolConverter, mcpProperties);
    }
    
    /**
     * 配置Function Callbacks列表
     *
     * 这个Bean会被Spring AI自动检测并注册为可用的函数
     * 暂时禁用，专注于MCP协议实现
     */
    @Bean
    @ConditionalOnProperty(prefix = "galaxy.mcp", name = "spring-ai-integration", havingValue = "true", matchIfMissing = false)
    public List<FunctionCallback> galaxyMcpFunctionCallbacks(McpToolService mcpToolService) {
        logger.info("Galaxy Boot MCP: Creating Function Callbacks");

        try {
            List<McpToolInfo> discoveredTools = mcpToolService.getDiscoveredTools();
            logger.info("Galaxy Boot MCP: Found {} MCP tools", discoveredTools.size());

            List<FunctionCallback> functionCallbacks = mcpToolService.convertToFunctionCallbacks();
            logger.info("Galaxy Boot MCP: Created {} Function Callbacks", functionCallbacks.size());

            // 打印工具信息
            for (McpToolInfo toolInfo : discoveredTools) {
                logger.info("Galaxy Boot MCP: Registered tool '{}' from method {}",
                           toolInfo.getName(), toolInfo.getMethodSignature());
            }

            return functionCallbacks;

        } catch (Exception e) {
            logger.error("Galaxy Boot MCP: Failed to create Function Callbacks", e);
            throw new RuntimeException("Failed to initialize MCP tools", e);
        }
    }

    /**
     * 初始化 MCP 工具信息（不依赖 Spring AI）
     */
    @Bean
    public List<McpToolInfo> galaxyMcpToolInfos(McpToolService mcpToolService) {
        logger.info("Galaxy Boot MCP: Initializing MCP Tool Infos");

        try {
            List<McpToolInfo> discoveredTools = mcpToolService.getDiscoveredTools();
            logger.info("Galaxy Boot MCP: Found {} MCP tools", discoveredTools.size());

            // 打印工具信息
            for (McpToolInfo toolInfo : discoveredTools) {
                logger.info("Galaxy Boot MCP: Registered tool '{}' from method {}",
                           toolInfo.getName(), toolInfo.getMethodSignature());
            }

            return discoveredTools;

        } catch (Exception e) {
            logger.error("Galaxy Boot MCP: Failed to initialize MCP tools", e);
            throw new RuntimeException("Failed to initialize MCP tools", e);
        }
    }
    
    /**
     * 内部配置类，用于配置ObjectMapper（如果需要自定义）
     */
    @Configuration
    @ConditionalOnMissingBean(ObjectMapper.class)
    static class ObjectMapperConfiguration {
        
        @Bean
        public ObjectMapper objectMapper() {
            ObjectMapper mapper = new ObjectMapper();
            // 可以在这里添加自定义配置
            return mapper;
        }
    }
}
