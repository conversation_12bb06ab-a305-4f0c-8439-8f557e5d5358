package cn.com.chinastock.cnf.mcp.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.victools.jsonschema.generator.*;
import com.github.victools.jsonschema.module.jackson.JacksonModule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * JSON Schema生成器
 * 
 * 用于为Java类型生成JSON Schema，支持MCP工具参数定义
 * 
 * <AUTHOR> Boot Team
 * @since 0.1.1
 */
public class JsonSchemaGenerator {
    
    private static final Logger logger = LoggerFactory.getLogger(JsonSchemaGenerator.class);
    
    private final ObjectMapper objectMapper;
    private final SchemaGenerator schemaGenerator;
    private final ConcurrentMap<Class<?>, JsonNode> schemaCache = new ConcurrentHashMap<>();
    
    public JsonSchemaGenerator(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
        this.schemaGenerator = createSchemaGenerator();
    }
    
    /**
     * 创建Schema生成器
     */
    private SchemaGenerator createSchemaGenerator() {
        SchemaGeneratorConfigBuilder configBuilder = new SchemaGeneratorConfigBuilder(
            SchemaVersion.DRAFT_2019_09, OptionPreset.PLAIN_JSON);
        
        // 添加Jackson模块支持
        configBuilder.with(new JacksonModule());
        
        // 配置基本选项
        configBuilder.with(Option.SCHEMA_VERSION_INDICATOR)
                    .with(Option.ADDITIONAL_FIXED_TYPES)
                    .with(Option.EXTRA_OPEN_API_FORMAT_VALUES)
                    .with(Option.FLATTENED_ENUMS_FROM_TOSTRING);
        
        SchemaGeneratorConfig config = configBuilder.build();
        return new SchemaGenerator(config);
    }
    
    /**
     * 为指定类型生成JSON Schema
     * 
     * @param type 要生成Schema的类型
     * @return JSON Schema
     */
    public JsonNode generateSchema(Class<?> type) {
        // 检查缓存
        JsonNode cachedSchema = schemaCache.get(type);
        if (cachedSchema != null) {
            return cachedSchema;
        }
        
        try {
            // 生成Schema
            JsonNode schema = generateSchemaInternal(type);
            
            // 缓存结果
            schemaCache.put(type, schema);
            
            return schema;
            
        } catch (Exception e) {
            logger.error("Failed to generate JSON schema for type: {}", type.getName(), e);
            
            // 返回基本Schema
            return createBasicSchema(type);
        }
    }
    
    /**
     * 内部Schema生成逻辑
     */
    private JsonNode generateSchemaInternal(Class<?> type) {
        // 处理基本类型
        if (isPrimitiveOrWrapper(type)) {
            return createPrimitiveSchema(type);
        }
        
        // 处理字符串类型
        if (type == String.class) {
            return createStringSchema();
        }
        
        // 处理数组类型
        if (type.isArray()) {
            return createArraySchema(type.getComponentType());
        }
        
        // 处理复杂对象类型
        return schemaGenerator.generateSchema(type);
    }
    
    /**
     * 检查是否为基本类型或包装类型
     */
    private boolean isPrimitiveOrWrapper(Class<?> type) {
        return type.isPrimitive() ||
               type == Boolean.class ||
               type == Byte.class ||
               type == Short.class ||
               type == Integer.class ||
               type == Long.class ||
               type == Float.class ||
               type == Double.class ||
               type == Character.class;
    }
    
    /**
     * 创建基本类型Schema
     */
    private JsonNode createPrimitiveSchema(Class<?> type) {
        ObjectMapper mapper = new ObjectMapper();
        
        if (type == boolean.class || type == Boolean.class) {
            return mapper.createObjectNode().put("type", "boolean");
        }
        
        if (type == byte.class || type == Byte.class ||
            type == short.class || type == Short.class ||
            type == int.class || type == Integer.class) {
            return mapper.createObjectNode().put("type", "integer");
        }
        
        if (type == long.class || type == Long.class) {
            return mapper.createObjectNode()
                        .put("type", "integer")
                        .put("format", "int64");
        }
        
        if (type == float.class || type == Float.class ||
            type == double.class || type == Double.class) {
            return mapper.createObjectNode().put("type", "number");
        }
        
        if (type == char.class || type == Character.class) {
            return mapper.createObjectNode()
                        .put("type", "string")
                        .put("minLength", 1)
                        .put("maxLength", 1);
        }
        
        // 默认返回字符串类型
        return createStringSchema();
    }
    
    /**
     * 创建字符串Schema
     */
    private JsonNode createStringSchema() {
        return new ObjectMapper().createObjectNode().put("type", "string");
    }
    
    /**
     * 创建数组Schema
     */
    private JsonNode createArraySchema(Class<?> componentType) {
        ObjectMapper mapper = new ObjectMapper();
        JsonNode itemSchema = generateSchema(componentType);
        
        return mapper.createObjectNode()
                    .put("type", "array")
                    .set("items", itemSchema);
    }
    
    /**
     * 创建基本Schema（当生成失败时使用）
     */
    private JsonNode createBasicSchema(Class<?> type) {
        ObjectMapper mapper = new ObjectMapper();
        
        if (isPrimitiveOrWrapper(type) || type == String.class) {
            return createPrimitiveSchema(type);
        }
        
        // 对于复杂类型，返回通用对象Schema
        return mapper.createObjectNode()
                    .put("type", "object")
                    .put("description", "Complex object of type: " + type.getSimpleName());
    }
    
    /**
     * 清空Schema缓存
     */
    public void clearCache() {
        schemaCache.clear();
        logger.debug("JSON Schema cache cleared");
    }
    
    /**
     * 获取缓存大小
     */
    public int getCacheSize() {
        return schemaCache.size();
    }
}
