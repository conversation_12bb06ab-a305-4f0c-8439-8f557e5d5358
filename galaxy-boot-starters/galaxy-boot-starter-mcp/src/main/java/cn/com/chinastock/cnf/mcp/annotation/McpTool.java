package cn.com.chinastock.cnf.mcp.annotation;

import java.lang.annotation.*;

/**
 * 标记RestController方法为MCP工具的注解
 * 
 * 使用此注解的方法将自动暴露为MCP Server的工具，
 * 可以通过AI聊天的方式调用这些API完成业务功能。
 * 
 * <AUTHOR> Boot Team
 * @since 0.1.1
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface McpTool {
    
    /**
     * 工具名称，如果不指定则使用方法名
     * @return 工具名称
     */
    String name() default "";
    
    /**
     * 工具描述，用于AI理解工具的功能
     * @return 工具描述
     */
    String description() default "";
    
    /**
     * 是否启用此工具
     * @return 是否启用
     */
    boolean enabled() default true;
    
    /**
     * 工具分组，用于组织和管理工具
     * @return 工具分组
     */
    String group() default "default";
    
    /**
     * 工具标签，用于分类和搜索
     * @return 工具标签
     */
    String[] tags() default {};
    
    /**
     * 响应MIME类型，如果不指定则根据返回类型自动推断
     * @return MIME类型
     */
    String responseMimeType() default "";
}
