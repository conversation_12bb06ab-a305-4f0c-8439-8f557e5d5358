# Galaxy Boot Starter MCP 设计文档

## 概述

Galaxy Boot Starter MCP 是一个基于 Spring AI 的封装，提供了通过注解将 SpringBoot RestController 接口自动暴露为 MCP (Model Context Protocol) 工具的功能。这使得 AI 可以通过聊天的方式调用这些 API 完成业务功能。

## 设计目标

1. **简化集成**：通过注解驱动的方式，让开发者能够轻松地将现有的 REST API 暴露给 AI
2. **类型安全**：自动生成 JSON Schema，确保参数类型的正确性
3. **灵活配置**：支持工具分组、标签、过滤等高级配置选项
4. **无侵入性**：不影响现有的 REST API 功能，只是增加了 AI 调用能力

## 核心架构

### 1. 注解层 (@McpTool)
- 提供声明式的工具标记方式
- 支持工具名称、描述、分组、标签等配置
- 与 Spring MVC 注解完全兼容

### 2. 扫描层 (McpToolScanner)
- 自动扫描 Spring 容器中的 Controller
- 识别带有 @McpTool 注解的方法
- 支持包路径过滤和条件注册

### 3. 转换层 (ControllerToolConverter)
- 将 Controller 方法转换为 Spring AI Function Callback
- 处理参数类型转换和 JSON Schema 生成
- 支持复杂对象的序列化和反序列化

### 4. 服务层 (McpToolService)
- 管理工具的生命周期
- 提供工具查询和统计功能
- 支持动态工具管理

### 5. 配置层 (McpProperties & McpAutoConfiguration)
- 提供丰富的配置选项
- 支持 Spring Boot 自动配置
- 集成 Spring Boot Configuration Properties

## 技术实现

### 核心依赖
- Spring Boot 3.3.7+
- Spring AI 1.0.0-M6
- Jackson (JSON 处理)
- JSON Schema Generator (参数 Schema 生成)

### 关键组件

#### @McpTool 注解
```java
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface McpTool {
    String name() default "";
    String description() default "";
    boolean enabled() default true;
    String group() default "default";
    String[] tags() default {};
    String responseMimeType() default "";
}
```

#### 工具扫描器
- 实现 BeanPostProcessor 接口
- 在 Bean 初始化后扫描 @McpTool 注解
- 支持包路径过滤和条件注册

#### 工具转换器
- 将 Controller 方法转换为 Function Callback
- 处理参数映射和类型转换
- 支持 ResponseEntity 和复杂返回类型

#### 自动配置
- 基于 Spring Boot AutoConfiguration
- 条件化配置，支持开关控制
- 自动注册 Function Callback 到 Spring AI

## 配置体系

### 基础配置
```yaml
galaxy:
  mcp:
    enabled: true
    server-name: galaxy-mcp-server
    auto-scan: true
    scan-packages:
      - com.example.controller
```

### 工具配置
```yaml
galaxy:
  mcp:
    tool:
      enabled: true
      timeout-seconds: 30
      group-filters: [user, admin]
      tag-filters: [public]
      items:
        toolName:
          enabled: true
          description: "自定义描述"
```

### 传输配置
```yaml
galaxy:
  mcp:
    transport:
      type: webmvc
      sse-endpoint: /mcp/sse
      message-endpoint: /mcp/message
```

## 使用流程

### 1. 添加依赖
```xml
<dependency>
    <groupId>cn.com.chinastock</groupId>
    <artifactId>galaxy-boot-starter-mcp</artifactId>
</dependency>
```

### 2. 标记方法
```java
@McpTool(
    name = "getUserInfo",
    description = "获取用户信息",
    group = "user",
    tags = {"user", "query"}
)
@GetMapping("/user/{id}")
public User getUserInfo(@PathVariable Long id) {
    return userService.getUserById(id);
}
```

### 3. 配置应用
```yaml
galaxy:
  mcp:
    enabled: true
    scan-packages:
      - com.example.controller
```

### 4. 启动应用
应用启动后，标记的方法会自动注册为 AI 可调用的工具。

## 特性支持

### 参数类型支持
- 基本类型：String, Integer, Long, Double, Boolean 等
- 复杂对象：支持 @RequestBody 注解的 POJO
- Spring MVC 注解：@PathVariable, @RequestParam, @RequestBody
- 验证注解：@Valid, @NotNull, @NotBlank 等

### 返回类型支持
- 基本类型和字符串
- 复杂对象（自动 JSON 序列化）
- ResponseEntity（自动提取 body）
- 自定义 MIME 类型

### 高级功能
- 工具分组管理
- 标签分类
- 条件过滤
- 单个工具配置
- 统计信息

## 扩展点

### 1. 自定义转换器
可以实现自定义的 ControllerToolConverter 来处理特殊的转换需求。

### 2. 自定义扫描器
可以实现自定义的 McpToolScanner 来支持特殊的扫描逻辑。

### 3. 自定义配置
可以通过 McpProperties 扩展配置选项。

## 最佳实践

### 1. 工具设计
- 保持工具功能单一和明确
- 提供详细的描述信息
- 合理使用分组和标签

### 2. 参数设计
- 使用清晰的参数名称
- 提供参数验证
- 避免过于复杂的参数结构

### 3. 错误处理
- 提供清晰的错误信息
- 使用适当的异常类型
- 考虑用户体验

### 4. 性能优化
- 合理设置超时时间
- 使用缓存减少重复计算
- 避免长时间运行的操作

## 未来规划

### 短期目标
- 支持更多的 Spring AI 传输方式
- 增强 JSON Schema 生成能力
- 提供更丰富的配置选项

### 长期目标
- 支持异步工具调用
- 集成更多的 AI 模型
- 提供可视化管理界面
- 支持工具编排和组合

## 版本历史

### 0.1.1-ALPHA
- 初始版本发布
- 支持基本的 @McpTool 注解
- 支持 Spring AI Function Callback 集成
- 支持工具分组和标签
- 支持灵活的配置选项
