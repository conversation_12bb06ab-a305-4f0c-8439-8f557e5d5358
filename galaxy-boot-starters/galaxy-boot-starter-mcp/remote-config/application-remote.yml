# Galaxy Boot MCP Server 远程服务器配置
# 用于部署在远程服务器上的配置

server:
  port: 8080
  # 如果需要 HTTPS
  # ssl:
  #   enabled: true
  #   key-store: classpath:keystore.p12
  #   key-store-password: password
  #   key-store-type: PKCS12

spring:
  application:
    name: galaxy-mcp-remote-server

# Galaxy Boot MCP 配置
galaxy:
  mcp:
    # 启用MCP功能
    enabled: true
    # 服务器信息
    server-name: galaxy-mcp-remote-server
    server-version: 1.0.0
    server-description: Galaxy Boot MCP Remote Server
    # 自动扫描配置
    auto-scan: true
    scan-packages:
      - cn.com.chinastock.cnf.examples.mcp.controller
    # 工具配置
    tool:
      enabled: true
      timeout-seconds: 30
      # 只暴露public标签的工具
      tag-filters:
        - public
    # 传输配置 - 支持多种传输方式
    transport:
      type: webmvc  # webmvc 支持 HTTP 和 SSE
      sse-endpoint: /mcp/sse
      message-endpoint: /mcp/message
      websocket-endpoint: /mcp/ws
      base-url: ""
    # 安全配置
    security:
      enabled: true
      api-key: ${MCP_API_KEY:your-default-api-key}
      allowed-ips:
        - 0.0.0.0/0  # 允许所有IP，生产环境请限制
      # 允许的域名（CORS）
      allowed-origins:
        - "*"  # 生产环境请限制具体域名

# 跨域配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
      cors:
        allowed-origins: "*"
        allowed-methods: GET,POST,PUT,DELETE,OPTIONS
        allowed-headers: "*"

# 日志配置
logging:
  level:
    cn.com.chinastock.cnf.mcp: INFO
    org.springframework.web: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/galaxy-mcp-server.log

# Actuator 配置
management:
  endpoint:
    health:
      show-details: always
  info:
    env:
      enabled: true
    java:
      enabled: true
    os:
      enabled: true
