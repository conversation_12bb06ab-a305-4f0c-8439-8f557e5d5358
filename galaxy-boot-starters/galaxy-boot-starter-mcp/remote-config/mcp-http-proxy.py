#!/usr/bin/env python3

"""
Galaxy Boot MCP Server HTTP 代理客户端
用于通过 HTTP 连接远程 Galaxy Boot MCP Server
"""

import json
import sys
import os
import argparse
import requests
import time
from typing import Dict, Any, Optional

class McpHttpProxy:
    def __init__(self, server_url: str, endpoint: str = "/mcp/message", 
                 api_key: Optional[str] = None, timeout: int = 30):
        self.server_url = server_url.rstrip('/')
        self.endpoint = endpoint
        self.api_key = api_key or os.getenv('MCP_API_KEY')
        self.timeout = timeout
        self.session = requests.Session()
        
        # 设置请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'Galaxy-MCP-HTTP-Proxy/1.0'
        })
        
        if self.api_key:
            self.session.headers['Authorization'] = f'Bearer {self.api_key}'

    def send_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """发送请求到远程 MCP 服务器"""
        url = f"{self.server_url}{self.endpoint}"
        
        try:
            response = self.session.post(
                url, 
                json=request, 
                timeout=self.timeout
            )
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.Timeout:
            return {
                "jsonrpc": "2.0",
                "id": request.get("id"),
                "error": {
                    "code": -32603,
                    "message": "Request timeout"
                }
            }
        except requests.exceptions.ConnectionError:
            return {
                "jsonrpc": "2.0",
                "id": request.get("id"),
                "error": {
                    "code": -32603,
                    "message": "Connection error"
                }
            }
        except requests.exceptions.HTTPError as e:
            return {
                "jsonrpc": "2.0",
                "id": request.get("id"),
                "error": {
                    "code": -32603,
                    "message": f"HTTP error: {e.response.status_code}"
                }
            }
        except Exception as e:
            return {
                "jsonrpc": "2.0",
                "id": request.get("id"),
                "error": {
                    "code": -32603,
                    "message": f"Unexpected error: {str(e)}"
                }
            }

    def health_check(self) -> bool:
        """检查服务器健康状态"""
        try:
            health_url = f"{self.server_url}/actuator/health"
            response = self.session.get(health_url, timeout=5)
            return response.status_code == 200
        except:
            return False

    def run(self):
        """运行代理客户端"""
        # 检查服务器连接
        if not self.health_check():
            print(f"Warning: Cannot connect to server {self.server_url}", 
                  file=sys.stderr)
        
        print(f"MCP HTTP Proxy connected to {self.server_url}", 
              file=sys.stderr)
        
        # 处理标准输入
        try:
            for line in sys.stdin:
                line = line.strip()
                if not line:
                    continue
                
                try:
                    request = json.loads(line)
                    response = self.send_request(request)
                    print(json.dumps(response))
                    sys.stdout.flush()
                    
                except json.JSONDecodeError as e:
                    error_response = {
                        "jsonrpc": "2.0",
                        "id": None,
                        "error": {
                            "code": -32700,
                            "message": f"Parse error: {str(e)}"
                        }
                    }
                    print(json.dumps(error_response))
                    sys.stdout.flush()
                    
        except KeyboardInterrupt:
            print("MCP HTTP Proxy shutting down...", file=sys.stderr)
        except Exception as e:
            print(f"Unexpected error: {str(e)}", file=sys.stderr)
            sys.exit(1)

def main():
    parser = argparse.ArgumentParser(
        description='Galaxy Boot MCP Server HTTP Proxy Client'
    )
    parser.add_argument(
        '--server-url', 
        required=True,
        help='MCP Server URL (e.g., https://your-server.com:8080)'
    )
    parser.add_argument(
        '--endpoint', 
        default='/mcp/message',
        help='MCP message endpoint (default: /mcp/message)'
    )
    parser.add_argument(
        '--api-key',
        help='API key for authentication (can also use MCP_API_KEY env var)'
    )
    parser.add_argument(
        '--timeout',
        type=int,
        default=30,
        help='Request timeout in seconds (default: 30)'
    )
    parser.add_argument(
        '--test',
        action='store_true',
        help='Run a simple test'
    )
    
    args = parser.parse_args()
    
    proxy = McpHttpProxy(
        server_url=args.server_url,
        endpoint=args.endpoint,
        api_key=args.api_key,
        timeout=args.timeout
    )
    
    if args.test:
        # 运行测试
        test_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "tools/list",
            "params": {}
        }
        
        print("Testing connection...", file=sys.stderr)
        response = proxy.send_request(test_request)
        print("Test response:", json.dumps(response, indent=2), file=sys.stderr)
        
        if "error" not in response:
            print("✅ Connection test successful!", file=sys.stderr)
        else:
            print("❌ Connection test failed!", file=sys.stderr)
            sys.exit(1)
    else:
        proxy.run()

if __name__ == '__main__':
    main()
