# Galaxy Boot MCP Server 客户端配置示例

## 测试验证

我们的 Galaxy Boot MCP Server 已经成功运行并通过了以下测试：

### ✅ 服务器状态
- 应用成功启动在端口 8080
- 发现并注册了 17 个 MCP 工具
- MCP 端点正常响应

### ✅ MCP 协议测试
```bash
# 1. 获取服务器信息
curl http://localhost:8080/mcp/info
# 返回: {"connectedClients":0,"capabilities":{"tools":true},"name":"Galaxy Boot MCP Server","toolCount":17}

# 2. 获取工具列表
curl -X POST http://localhost:8080/mcp/message \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":1,"method":"tools/list","params":{}}'
# 返回: 17个可用工具的列表

# 3. 调用工具
curl -X POST http://localhost:8080/mcp/message \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":2,"method":"tools/call","params":{"name":"add","arguments":{"a":10,"b":5}}}'
# 返回: {"result":{"content":[{"text":"15.0","type":"text"}],"isError":false}}
```

## 实际客户端配置

### 1. Cursor 配置

**配置文件位置**: `~/Library/Application Support/Cursor/User/globalStorage/mcp-servers.json`

```json
{
  "mcpServers": {
    "galaxy-business-server": {
      "command": "python3",
      "args": [
        "/path/to/mcp-http-proxy.py",
        "--server-url", "http://your-server.com:8080",
        "--endpoint", "/mcp/message"
      ],
      "env": {
        "MCP_API_KEY": "your-api-key-if-needed"
      }
    }
  }
}
```

**使用示例**:
```
用户: @galaxy-business-server 帮我计算 123 * 456
AI: 我来帮你计算 123 * 456。
[调用 multiply 工具，参数: a=123, b=456]
结果: 56088
```

### 2. Claude Desktop 配置

**配置文件位置**: `~/Library/Application Support/Claude/claude_desktop_config.json`

```json
{
  "mcpServers": {
    "galaxy-business-tools": {
      "command": "python3",
      "args": [
        "/path/to/mcp-http-proxy.py",
        "--server-url", "https://your-server.com:8080",
        "--endpoint", "/mcp/message"
      ],
      "env": {
        "MCP_API_KEY": "your-secure-api-key"
      }
    }
  }
}
```

**使用示例**:
```
用户: 查询所有用户的统计信息
AI: 我来查询用户统计信息。
[调用 getUserStatistics 工具]
结果显示:
- 总用户数: 4
- 平均年龄: 29.0岁
- 部门分布: IT部(1), 开发部(1), 产品部(1), 销售部(1)
```

### 3. Cline (VS Code) 配置

**配置文件**: VS Code `settings.json`

```json
{
  "cline.mcpServers": {
    "galaxy-remote-api": {
      "command": "python3",
      "args": [
        "/path/to/mcp-http-proxy.py",
        "--server-url", "http://localhost:8080",
        "--endpoint", "/mcp/message"
      ]
    }
  }
}
```

## HTTP 代理客户端脚本

### Python HTTP 代理 (推荐)

保存为 `mcp-http-proxy.py`:

```python
#!/usr/bin/env python3
import json
import sys
import requests
import argparse

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument('--server-url', required=True)
    parser.add_argument('--endpoint', default='/mcp/message')
    parser.add_argument('--api-key', help='API key for authentication')
    args = parser.parse_args()
    
    session = requests.Session()
    session.headers.update({'Content-Type': 'application/json'})
    
    if args.api_key:
        session.headers['Authorization'] = f'Bearer {args.api_key}'
    
    for line in sys.stdin:
        try:
            request = json.loads(line.strip())
            response = session.post(f"{args.server_url}{args.endpoint}", json=request)
            print(json.dumps(response.json()))
            sys.stdout.flush()
        except Exception as e:
            error_response = {
                "jsonrpc": "2.0",
                "id": request.get("id") if 'request' in locals() else None,
                "error": {"code": -32603, "message": str(e)}
            }
            print(json.dumps(error_response))

if __name__ == '__main__':
    main()
```

### Node.js SSE 客户端

保存为 `mcp-sse-client.js`:

```javascript
#!/usr/bin/env node
const EventSource = require('eventsource');
const https = require('https');
const http = require('http');

const serverUrl = process.argv[2];
if (!serverUrl) {
    console.error('Usage: node mcp-sse-client.js <server-url>');
    process.exit(1);
}

// SSE 连接用于接收服务器推送
const sseUrl = `${serverUrl}/mcp/sse`;
const eventSource = new EventSource(sseUrl);

eventSource.onmessage = (event) => {
    console.error('SSE message:', event.data);
};

// 处理标准输入的 MCP 请求
process.stdin.on('data', async (data) => {
    try {
        const request = JSON.parse(data.toString());
        const response = await sendHttpRequest(serverUrl, request);
        console.log(JSON.stringify(response));
    } catch (error) {
        console.log(JSON.stringify({
            jsonrpc: "2.0",
            id: null,
            error: { code: -32603, message: error.message }
        }));
    }
});

async function sendHttpRequest(serverUrl, request) {
    // HTTP 请求实现...
    const url = new URL('/mcp/message', serverUrl);
    const postData = JSON.stringify(request);
    
    return new Promise((resolve, reject) => {
        const options = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            }
        };
        
        const req = (url.protocol === 'https:' ? https : http).request(url, options, (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    resolve(JSON.parse(data));
                } catch (e) {
                    reject(e);
                }
            });
        });
        
        req.on('error', reject);
        req.write(postData);
        req.end();
    });
}
```

## 部署到生产环境

### 1. Docker 部署

```dockerfile
FROM openjdk:21-jre-slim

COPY galaxy-boot-mcp-example.jar /app/app.jar
EXPOSE 8080

CMD ["java", "-jar", "/app/app.jar", \
     "--galaxy.mcp.enabled=true", \
     "--galaxy.mcp.security.enabled=true", \
     "--galaxy.mcp.security.api-key=${MCP_API_KEY}"]
```

### 2. 使用 nginx 反向代理

```nginx
server {
    listen 443 ssl;
    server_name your-mcp-server.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /mcp/sse {
        proxy_pass http://localhost:8080;
        proxy_set_header Connection '';
        proxy_http_version 1.1;
        chunked_transfer_encoding off;
        proxy_buffering off;
        proxy_cache off;
    }
}
```

## 可用的 MCP 工具

我们的服务器提供以下 17 个工具：

### 计算器工具
- `add` - 加法运算
- `subtract` - 减法运算  
- `multiply` - 乘法运算
- `divide` - 除法运算
- `power` - 幂运算
- `sqrt` - 平方根
- `percentage` - 百分比计算
- `complexCalculation` - 复合计算

### 用户管理工具
- `getUserById` - 根据ID获取用户
- `getUserByUsername` - 根据用户名获取用户
- `getAllUsers` - 获取所有用户
- `getUsersByDepartment` - 根据部门获取用户
- `createUser` - 创建用户
- `updateUser` - 更新用户
- `deleteUser` - 删除用户
- `searchUsers` - 搜索用户
- `getUserStatistics` - 获取用户统计

## 使用场景示例

### 1. 数据分析助手
```
用户: 分析一下我们公司的用户分布情况
AI: 我来帮你分析用户分布。
[调用 getUserStatistics 工具]
根据统计数据显示：
- 总用户数：4人
- 平均年龄：29岁
- 部门分布均匀，每个部门1人
- 建议加强某些部门的人员配置
```

### 2. 业务计算助手
```
用户: 如果我们的转化率是15%，访问量是1000，那么转化用户数是多少？
AI: 我来计算转化用户数。
[调用 percentage 工具计算]
[调用 multiply 工具计算]
转化用户数 = 1000 × 15% = 150人
```

### 3. 用户管理助手
```
用户: 帮我创建一个新员工档案，姓名张三，邮箱********************
AI: 我来为您创建新员工档案。
[调用 createUser 工具]
员工档案创建成功！
- 员工ID: 5
- 姓名: 张三
- 邮箱: <EMAIL>
```

通过这种方式，您的业务系统就可以通过 AI 对话的方式进行操作了！🚀
