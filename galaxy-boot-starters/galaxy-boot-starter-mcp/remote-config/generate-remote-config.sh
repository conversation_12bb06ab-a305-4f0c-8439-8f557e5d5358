#!/bin/bash

# Galaxy Boot MCP Server 远程配置生成脚本
# 用于生成连接远程服务器的 MCP 客户端配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认参数
SERVER_URL=${1:-"https://your-server.com:8080"}
SERVER_NAME=${2:-"galaxy-remote-server"}
API_KEY=${3:-""}
CLIENT_TYPE=${4:-"all"}

echo -e "${BLUE}=== Galaxy Boot MCP 远程服务器配置生成器 ===${NC}"
echo

echo -e "${GREEN}配置参数:${NC}"
echo "  服务器URL: $SERVER_URL"
echo "  服务器名称: $SERVER_NAME"
echo "  API密钥: ${API_KEY:-"未设置"}"
echo "  客户端类型: $CLIENT_TYPE"
echo

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 生成 Cursor 远程配置
generate_cursor_remote_config() {
    cat > cursor-remote-config.json << EOF
{
  "mcpServers": {
    "${SERVER_NAME}": {
      "command": "node",
      "args": [
        "${SCRIPT_DIR}/mcp-sse-client.js",
        "${SERVER_URL}"
      ],
      "env": {
        "MCP_API_KEY": "${API_KEY}",
        "NODE_ENV": "production"
      }
    }
  }
}
EOF

    echo -e "${GREEN}✅ Cursor 远程配置已生成: cursor-remote-config.json${NC}"
    echo -e "${YELLOW}配置文件位置:${NC}"
    echo "  macOS: ~/Library/Application Support/Cursor/User/globalStorage/mcp-servers.json"
    echo "  Windows: %APPDATA%\\Cursor\\User\\globalStorage\\mcp-servers.json"
    echo "  Linux: ~/.config/Cursor/User/globalStorage/mcp-servers.json"
}

# 生成 Claude Desktop 远程配置
generate_claude_remote_config() {
    cat > claude-remote-config.json << EOF
{
  "mcpServers": {
    "${SERVER_NAME}": {
      "command": "python3",
      "args": [
        "${SCRIPT_DIR}/mcp-http-proxy.py",
        "--server-url", "${SERVER_URL}",
        "--endpoint", "/mcp/message"
      ],
      "env": {
        "MCP_API_KEY": "${API_KEY}"
      }
    }
  }
}
EOF

    echo -e "${GREEN}✅ Claude Desktop 远程配置已生成: claude-remote-config.json${NC}"
    echo -e "${YELLOW}配置文件位置:${NC}"
    echo "  macOS: ~/Library/Application Support/Claude/claude_desktop_config.json"
    echo "  Windows: %APPDATA%\\Claude\\claude_desktop_config.json"
}

# 生成 Cline 远程配置
generate_cline_remote_config() {
    cat > cline-remote-settings.json << EOF
{
  "cline.mcpServers": {
    "${SERVER_NAME}": {
      "command": "python3",
      "args": [
        "${SCRIPT_DIR}/mcp-http-proxy.py",
        "--server-url", "${SERVER_URL}",
        "--endpoint", "/mcp/message"
      ],
      "env": {
        "MCP_API_KEY": "${API_KEY}"
      }
    }
  }
}
EOF

    echo -e "${GREEN}✅ Cline 远程配置已生成: cline-remote-settings.json${NC}"
    echo -e "${YELLOW}将以上配置添加到 VS Code 的 settings.json 中${NC}"
}

# 生成通用 HTTP 配置
generate_http_config() {
    cat > http-remote-config.json << EOF
{
  "mcpServers": {
    "${SERVER_NAME}-http": {
      "command": "curl",
      "args": [
        "-X", "POST",
        "${SERVER_URL}/mcp/message",
        "-H", "Content-Type: application/json",
        "-H", "Accept: application/json"$([ -n "$API_KEY" ] && echo ",
        \"-H\", \"Authorization: Bearer ${API_KEY}\""),
        "-d", "@-"
      ]
    }
  }
}
EOF

    echo -e "${GREEN}✅ HTTP 远程配置已生成: http-remote-config.json${NC}"
}

# 生成 Docker 配置
generate_docker_config() {
    cat > docker-compose.yml << EOF
version: '3.8'

services:
  galaxy-mcp-server:
    image: openjdk:21-jre-slim
    container_name: galaxy-mcp-server
    ports:
      - "8080:8080"
    volumes:
      - ./galaxy-boot-mcp-example.jar:/app/app.jar
      - ./logs:/app/logs
    environment:
      - JAVA_OPTS=-Xmx512m -Xms256m
      - SPRING_PROFILES_ACTIVE=remote
      - MCP_API_KEY=${API_KEY}
    command: >
      java \$JAVA_OPTS
      -jar /app/app.jar
      --galaxy.mcp.enabled=true
      --galaxy.mcp.transport.type=webmvc
      --galaxy.mcp.security.enabled=true
      --galaxy.mcp.security.api-key=\$MCP_API_KEY
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    container_name: galaxy-mcp-nginx
    ports:
      - "443:443"
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - galaxy-mcp-server
    restart: unless-stopped
EOF

    # 生成 Nginx 配置
    cat > nginx.conf << EOF
events {
    worker_connections 1024;
}

http {
    upstream galaxy_mcp {
        server galaxy-mcp-server:8080;
    }

    server {
        listen 80;
        server_name ${SERVER_URL#*://};
        return 301 https://\$server_name\$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name ${SERVER_URL#*://};

        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;

        location / {
            proxy_pass http://galaxy_mcp;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        }

        location /mcp/sse {
            proxy_pass http://galaxy_mcp;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
            
            # SSE 特殊配置
            proxy_set_header Connection '';
            proxy_http_version 1.1;
            chunked_transfer_encoding off;
            proxy_buffering off;
            proxy_cache off;
        }
    }
}
EOF

    echo -e "${GREEN}✅ Docker 配置已生成: docker-compose.yml, nginx.conf${NC}"
}

# 生成测试脚本
generate_test_script() {
    cat > test-remote-connection.sh << EOF
#!/bin/bash

# 测试远程 MCP 服务器连接

SERVER_URL="${SERVER_URL}"
API_KEY="${API_KEY}"

echo "=== 测试远程 MCP 服务器连接 ==="
echo "服务器URL: \$SERVER_URL"
echo

# 1. 测试健康检查
echo "1. 测试健康检查..."
if curl -s "\$SERVER_URL/actuator/health" > /dev/null; then
    echo "✅ 健康检查通过"
else
    echo "❌ 健康检查失败"
    exit 1
fi

# 2. 测试 API 端点
echo
echo "2. 测试 API 端点..."
HEADERS="-H 'Content-Type: application/json'"
if [ -n "\$API_KEY" ]; then
    HEADERS="\$HEADERS -H 'Authorization: Bearer \$API_KEY'"
fi

# 测试计算器 API
RESULT=\$(curl -s \$HEADERS "\$SERVER_URL/api/calculator/add?a=10&b=5")
if [ "\$RESULT" = "15.0" ]; then
    echo "✅ 计算器 API 测试通过"
else
    echo "❌ 计算器 API 测试失败: \$RESULT"
fi

# 3. 测试 MCP 端点
echo
echo "3. 测试 MCP 端点..."
MCP_REQUEST='{"jsonrpc":"2.0","id":1,"method":"tools/list","params":{}}'
MCP_RESPONSE=\$(curl -s \$HEADERS -X POST "\$SERVER_URL/mcp/message" -d "\$MCP_REQUEST")

if echo "\$MCP_RESPONSE" | grep -q "tools"; then
    echo "✅ MCP 端点测试通过"
    echo "可用工具数量: \$(echo "\$MCP_RESPONSE" | jq '.result.tools | length' 2>/dev/null || echo "未知")"
else
    echo "❌ MCP 端点测试失败"
    echo "响应: \$MCP_RESPONSE"
fi

echo
echo "=== 测试完成 ==="
EOF

    chmod +x test-remote-connection.sh
    echo -e "${GREEN}✅ 测试脚本已生成: test-remote-connection.sh${NC}"
}

# 主逻辑
case "$CLIENT_TYPE" in
    "cursor")
        generate_cursor_remote_config
        ;;
    "claude")
        generate_claude_remote_config
        ;;
    "cline")
        generate_cline_remote_config
        ;;
    "http")
        generate_http_config
        ;;
    "docker")
        generate_docker_config
        ;;
    "all")
        generate_cursor_remote_config
        echo
        generate_claude_remote_config
        echo
        generate_cline_remote_config
        echo
        generate_http_config
        echo
        generate_docker_config
        ;;
    *)
        echo -e "${RED}未知的客户端类型: $CLIENT_TYPE${NC}"
        echo "支持的类型: cursor, claude, cline, http, docker, all"
        exit 1
        ;;
esac

echo
generate_test_script

echo
echo -e "${BLUE}=== 使用说明 ===${NC}"
echo "1. 确保远程服务器运行正常:"
echo "   ./test-remote-connection.sh"
echo
echo "2. 安装客户端依赖:"
echo "   npm install eventsource  # 用于 SSE 客户端"
echo "   pip3 install requests    # 用于 HTTP 代理"
echo
echo "3. 配置 AI 客户端:"
echo "   - 复制相应的配置文件内容到客户端配置中"
echo "   - 设置正确的服务器URL和API密钥"
echo "   - 重启 AI 客户端"
echo
echo "4. 开始使用 MCP 工具"
echo
echo -e "${YELLOW}注意事项:${NC}"
echo "- 确保服务器URL可访问"
echo "- 如果使用HTTPS，确保SSL证书有效"
echo "- 在生产环境中设置强密码的API密钥"
echo "- 考虑使用防火墙限制访问IP"
