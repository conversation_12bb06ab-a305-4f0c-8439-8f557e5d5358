#!/usr/bin/env node

/**
 * Galaxy Boot MCP Server SSE 客户端
 * 用于连接远程 Galaxy Boot MCP Server 的 SSE 端点
 */

const EventSource = require('eventsource');
const https = require('https');
const http = require('http');
const url = require('url');

class McpSseClient {
    constructor(serverUrl, options = {}) {
        this.serverUrl = serverUrl;
        this.apiKey = options.apiKey || process.env.MCP_API_KEY;
        this.timeout = options.timeout || 30000;
        this.eventSource = null;
        this.messageId = 0;
        this.pendingRequests = new Map();
    }

    async connect() {
        const sseUrl = `${this.serverUrl}/mcp/sse`;
        const headers = {
            'Accept': 'text/event-stream',
            'Cache-Control': 'no-cache'
        };

        if (this.apiKey) {
            headers['Authorization'] = `Bearer ${this.apiKey}`;
        }

        this.eventSource = new EventSource(sseUrl, { headers });

        this.eventSource.onopen = () => {
            console.error('Connected to MCP Server SSE endpoint');
        };

        this.eventSource.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleMessage(data);
            } catch (error) {
                console.error('Error parsing SSE message:', error);
            }
        };

        this.eventSource.onerror = (error) => {
            console.error('SSE connection error:', error);
        };

        // 处理标准输入
        process.stdin.on('data', (data) => {
            try {
                const request = JSON.parse(data.toString());
                this.sendRequest(request);
            } catch (error) {
                console.error('Error parsing input:', error);
            }
        });
    }

    async sendRequest(request) {
        const messageId = ++this.messageId;
        request.id = messageId;

        const postData = JSON.stringify(request);
        const parsedUrl = url.parse(this.serverUrl);
        
        const options = {
            hostname: parsedUrl.hostname,
            port: parsedUrl.port || (parsedUrl.protocol === 'https:' ? 443 : 80),
            path: '/mcp/message',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            }
        };

        if (this.apiKey) {
            options.headers['Authorization'] = `Bearer ${this.apiKey}`;
        }

        const httpModule = parsedUrl.protocol === 'https:' ? https : http;

        return new Promise((resolve, reject) => {
            const req = httpModule.request(options, (res) => {
                let responseData = '';
                
                res.on('data', (chunk) => {
                    responseData += chunk;
                });

                res.on('end', () => {
                    try {
                        const response = JSON.parse(responseData);
                        console.log(JSON.stringify(response));
                        resolve(response);
                    } catch (error) {
                        reject(error);
                    }
                });
            });

            req.on('error', (error) => {
                reject(error);
            });

            req.setTimeout(this.timeout, () => {
                req.destroy();
                reject(new Error('Request timeout'));
            });

            req.write(postData);
            req.end();
        });
    }

    handleMessage(message) {
        // 处理服务器推送的消息
        if (message.type === 'notification') {
            console.error('Server notification:', message);
        } else if (message.type === 'response') {
            // 处理响应消息
            console.log(JSON.stringify(message));
        }
    }

    disconnect() {
        if (this.eventSource) {
            this.eventSource.close();
        }
    }
}

// 主程序
async function main() {
    const serverUrl = process.argv[2];
    if (!serverUrl) {
        console.error('Usage: node mcp-sse-client.js <server-url>');
        process.exit(1);
    }

    const client = new McpSseClient(serverUrl, {
        apiKey: process.env.MCP_API_KEY,
        timeout: 30000
    });

    // 处理进程退出
    process.on('SIGINT', () => {
        console.error('Disconnecting from MCP Server...');
        client.disconnect();
        process.exit(0);
    });

    process.on('SIGTERM', () => {
        client.disconnect();
        process.exit(0);
    });

    try {
        await client.connect();
        console.error('MCP SSE Client started successfully');
    } catch (error) {
        console.error('Failed to connect to MCP Server:', error);
        process.exit(1);
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = McpSseClient;
