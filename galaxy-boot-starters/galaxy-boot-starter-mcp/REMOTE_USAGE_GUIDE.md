# Galaxy Boot MCP Server 远程使用指南

## 概述

本指南详细说明如何将 Galaxy Boot MCP Server 部署到远程服务器，并在 Cursor、<PERSON><PERSON>、<PERSON> 等 AI 客户端中配置使用。

## 部署到远程服务器

### 1. 构建应用

```bash
# 在项目根目录执行
cd galaxy-boot-examples/galaxy-boot-mcp-example
mvn clean package -DskipTests

# 生成的 JAR 文件位于 target/galaxy-boot-mcp-example-0.1.1-ALPHA.jar
```

### 2. 上传到服务器

```bash
# 上传 JAR 文件到服务器
scp target/galaxy-boot-mcp-example-0.1.1-ALPHA.jar <EMAIL>:/opt/galaxy-mcp/

# 上传配置文件
scp src/main/resources/application.yml <EMAIL>:/opt/galaxy-mcp/
```

### 3. 在服务器上启动

```bash
# SSH 到服务器
ssh <EMAIL>

# 启动应用
cd /opt/galaxy-mcp
java -jar galaxy-boot-mcp-example-0.1.1-ALPHA.jar \
  --server.address=0.0.0.0 \
  --server.port=8080 \
  --galaxy.mcp.enabled=true \
  --galaxy.mcp.security.enabled=true \
  --galaxy.mcp.security.api-key=your-secure-api-key
```

### 4. 使用 systemd 管理服务

创建服务文件 `/etc/systemd/system/galaxy-mcp.service`：

```ini
[Unit]
Description=Galaxy Boot MCP Server
After=network.target

[Service]
Type=simple
User=galaxy-mcp
WorkingDirectory=/opt/galaxy-mcp
ExecStart=/usr/bin/java -Xmx512m -jar galaxy-boot-mcp-example-0.1.1-ALPHA.jar \
  --server.address=0.0.0.0 \
  --server.port=8080 \
  --galaxy.mcp.enabled=true \
  --galaxy.mcp.security.enabled=true \
  --galaxy.mcp.security.api-key=your-secure-api-key
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启动服务：

```bash
sudo systemctl daemon-reload
sudo systemctl enable galaxy-mcp
sudo systemctl start galaxy-mcp
sudo systemctl status galaxy-mcp
```

## 配置 AI 客户端

### 1. 生成配置文件

使用配置生成脚本：

```bash
cd galaxy-boot-starters/galaxy-boot-starter-mcp/remote-config
chmod +x generate-remote-config.sh

# 生成所有客户端配置
./generate-remote-config.sh "https://your-server.com:8080" "galaxy-remote-server" "your-api-key" "all"
```

### 2. Cursor 配置

将生成的 `cursor-remote-config.json` 内容复制到 Cursor 的配置文件中：

**macOS:**
```bash
# 编辑配置文件
code ~/Library/Application\ Support/Cursor/User/globalStorage/mcp-servers.json
```

**配置内容:**
```json
{
  "mcpServers": {
    "galaxy-remote-server": {
      "command": "node",
      "args": [
        "/path/to/mcp-sse-client.js",
        "https://your-server.com:8080"
      ],
      "env": {
        "MCP_API_KEY": "your-api-key"
      }
    }
  }
}
```

### 3. Claude Desktop 配置

**macOS:**
```bash
# 编辑配置文件
code ~/Library/Application\ Support/Claude/claude_desktop_config.json
```

**配置内容:**
```json
{
  "mcpServers": {
    "galaxy-remote-server": {
      "command": "python3",
      "args": [
        "/path/to/mcp-http-proxy.py",
        "--server-url", "https://your-server.com:8080",
        "--endpoint", "/mcp/message"
      ],
      "env": {
        "MCP_API_KEY": "your-api-key"
      }
    }
  }
}
```

### 4. Cline (VS Code) 配置

在 VS Code 的 `settings.json` 中添加：

```json
{
  "cline.mcpServers": {
    "galaxy-remote-server": {
      "command": "python3",
      "args": [
        "/path/to/mcp-http-proxy.py",
        "--server-url", "https://your-server.com:8080",
        "--endpoint", "/mcp/message"
      ],
      "env": {
        "MCP_API_KEY": "your-api-key"
      }
    }
  }
}
```

## 使用示例

### 1. 在 Cursor 中使用

配置完成后，在 Cursor 的聊天界面中：

```
用户: @galaxy-remote-server 帮我计算 123 * 456

AI: 我来帮你计算 123 * 456。

[调用远程 multiply 工具]
参数: a=123, b=456

结果: 56088

123 * 456 = 56088
```

### 2. 在 Claude Desktop 中使用

```
用户: 使用 galaxy-remote-server 查询所有用户的统计信息

AI: 我来查询用户统计信息。

[调用远程 getUserStatistics 工具]

结果显示:
- 总用户数: 4
- 平均年龄: 29.0岁
- 部门分布:
  - IT部: 1人
  - 开发部: 1人
  - 产品部: 1人
  - 销售部: 1人
```

### 3. 复杂业务操作

```
用户: 创建一个新用户，用户名是alice，邮箱是*****************，年龄25岁，部门是开发部

AI: 我来为您创建新用户。

[调用远程 createUser 工具]
参数: {
  "username": "alice",
  "name": "Alice",
  "email": "<EMAIL>",
  "age": 25,
  "department": "开发部",
  "position": "软件工程师"
}

用户创建成功！用户ID: 5
```

## 测试连接

### 1. 使用测试脚本

```bash
# 测试服务器连接
./test-remote-connection.sh
```

### 2. 手动测试

```bash
# 测试健康检查
curl https://your-server.com:8080/actuator/health

# 测试 MCP 信息端点
curl https://your-server.com:8080/mcp/info

# 测试计算器 API
curl "https://your-server.com:8080/api/calculator/add?a=10&b=5" \
  -H "Authorization: Bearer your-api-key"

# 测试 MCP 消息端点
curl -X POST https://your-server.com:8080/mcp/message \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-api-key" \
  -d '{"jsonrpc":"2.0","id":1,"method":"tools/list","params":{}}'
```

## 安全配置

### 1. 启用 HTTPS

使用 Let's Encrypt 获取免费 SSL 证书：

```bash
# 安装 certbot
sudo apt install certbot

# 获取证书
sudo certbot certonly --standalone -d your-server.com

# 配置应用使用 HTTPS
java -jar galaxy-boot-mcp-example.jar \
  --server.ssl.enabled=true \
  --server.ssl.key-store=/etc/letsencrypt/live/your-server.com/keystore.p12 \
  --server.ssl.key-store-password=your-keystore-password \
  --server.ssl.key-store-type=PKCS12
```

### 2. 配置防火墙

```bash
# 只允许必要的端口
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP (重定向到 HTTPS)
sudo ufw allow 443   # HTTPS
sudo ufw enable
```

### 3. 设置 API 密钥

```bash
# 生成强密码
openssl rand -base64 32

# 在应用启动时设置
--galaxy.mcp.security.api-key=your-generated-strong-api-key
```

## 监控和日志

### 1. 查看应用日志

```bash
# 查看实时日志
sudo journalctl -u galaxy-mcp -f

# 查看历史日志
sudo journalctl -u galaxy-mcp --since "1 hour ago"
```

### 2. 监控应用状态

```bash
# 检查应用状态
curl https://your-server.com:8080/actuator/health

# 查看应用信息
curl https://your-server.com:8080/actuator/info

# 查看 MCP 服务器信息
curl https://your-server.com:8080/mcp/info
```

## 故障排除

### 1. 常见问题

**问题**: 无法连接到服务器
**解决**: 
- 检查服务器是否运行：`sudo systemctl status galaxy-mcp`
- 检查防火墙设置：`sudo ufw status`
- 检查端口是否监听：`netstat -tlnp | grep 8080`

**问题**: API 调用返回 401 错误
**解决**: 
- 检查 API 密钥是否正确
- 确认请求头中包含正确的 Authorization

**问题**: 工具调用失败
**解决**: 
- 查看应用日志：`sudo journalctl -u galaxy-mcp -f`
- 检查工具是否正确注册：`curl https://your-server.com:8080/mcp/info`

### 2. 调试技巧

1. **启用详细日志**:
```bash
--logging.level.cn.com.chinastock.cnf.mcp=DEBUG
```

2. **使用 curl 测试**:
```bash
curl -v https://your-server.com:8080/api/calculator/add?a=1&b=2
```

3. **检查网络连接**:
```bash
telnet your-server.com 8080
```

## 最佳实践

1. **安全性**:
   - 使用强密码的 API 密钥
   - 启用 HTTPS
   - 限制访问 IP 范围
   - 定期更新密钥

2. **性能**:
   - 设置合适的 JVM 参数
   - 使用连接池
   - 配置适当的超时时间

3. **可靠性**:
   - 使用 systemd 管理服务
   - 配置自动重启
   - 设置健康检查
   - 备份配置文件

4. **监控**:
   - 配置日志轮转
   - 设置监控告警
   - 定期检查服务状态

通过以上配置，您就可以在任何支持 MCP 的 AI 客户端中使用部署在远程服务器上的 Galaxy Boot MCP Server 了！
