#!/bin/bash

# Galaxy Boot MCP Server 配置生成脚本
# 用法: ./generate-mcp-config.sh [服务器名称] [JAR路径] [传输类型] [客户端类型]

set -e

# 默认参数
SERVER_NAME=${1:-"galaxy-mcp-server"}
JAR_PATH=${2:-"$(pwd)/target/galaxy-boot-mcp-example.jar"}
TRANSPORT_TYPE=${3:-"stdio"}
CLIENT_TYPE=${4:-"generic"}

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Galaxy Boot MCP Server 配置生成器 ===${NC}"
echo

# 检查 JAR 文件是否存在
if [[ ! -f "$JAR_PATH" ]]; then
    echo -e "${YELLOW}警告: JAR 文件不存在: $JAR_PATH${NC}"
    echo -e "${YELLOW}请确保先构建项目: mvn clean package${NC}"
fi

# 获取绝对路径
JAR_PATH=$(realpath "$JAR_PATH" 2>/dev/null || echo "$JAR_PATH")

echo -e "${GREEN}配置参数:${NC}"
echo "  服务器名称: $SERVER_NAME"
echo "  JAR 路径: $JAR_PATH"
echo "  传输类型: $TRANSPORT_TYPE"
echo "  客户端类型: $CLIENT_TYPE"
echo

# 生成通用 MCP 配置
generate_generic_config() {
    cat > mcp-config.json << EOF
{
  "mcpServers": {
    "${SERVER_NAME}": {
      "command": "java",
      "args": [
        "-jar",
        "${JAR_PATH}",
        "--galaxy.mcp.transport.type=${TRANSPORT_TYPE}",
        "--galaxy.mcp.enabled=true",
        "--logging.level.cn.com.chinastock.cnf.mcp=INFO"
      ],
      "env": {
        "JAVA_OPTS": "-Xmx512m -Xms256m",
        "SPRING_PROFILES_ACTIVE": "mcp"
      }
    }
  }
}
EOF
}

# 生成 Cursor 配置
generate_cursor_config() {
    cat > cursor-mcp-config.json << EOF
{
  "mcpServers": {
    "${SERVER_NAME}": {
      "command": "java",
      "args": [
        "-jar",
        "${JAR_PATH}",
        "--galaxy.mcp.transport.type=${TRANSPORT_TYPE}",
        "--galaxy.mcp.enabled=true",
        "--server.port=8080"
      ],
      "env": {
        "JAVA_OPTS": "-Xmx512m",
        "SPRING_PROFILES_ACTIVE": "production"
      }
    }
  }
}
EOF

    echo -e "${YELLOW}Cursor 配置文件位置:${NC}"
    echo "  macOS: ~/Library/Application Support/Cursor/User/globalStorage/mcp-servers.json"
    echo "  Windows: %APPDATA%\\Cursor\\User\\globalStorage\\mcp-servers.json"
    echo "  Linux: ~/.config/Cursor/User/globalStorage/mcp-servers.json"
}

# 生成 Claude Desktop 配置
generate_claude_config() {
    cat > claude-desktop-config.json << EOF
{
  "mcpServers": {
    "${SERVER_NAME}": {
      "command": "java",
      "args": [
        "-jar",
        "${JAR_PATH}",
        "--galaxy.mcp.transport.type=${TRANSPORT_TYPE}",
        "--galaxy.mcp.enabled=true",
        "--logging.level.cn.com.chinastock.cnf.mcp=INFO"
      ],
      "env": {
        "JAVA_OPTS": "-Xmx256m -Xms128m"
      }
    }
  }
}
EOF

    echo -e "${YELLOW}Claude Desktop 配置文件位置:${NC}"
    echo "  macOS: ~/Library/Application Support/Claude/claude_desktop_config.json"
    echo "  Windows: %APPDATA%\\Claude\\claude_desktop_config.json"
}

# 生成 Cline (VS Code) 配置
generate_cline_config() {
    cat > cline-settings.json << EOF
{
  "cline.mcpServers": {
    "${SERVER_NAME}": {
      "command": "java",
      "args": [
        "-jar",
        "${JAR_PATH}",
        "--galaxy.mcp.transport.type=${TRANSPORT_TYPE}",
        "--galaxy.mcp.enabled=true"
      ],
      "env": {
        "JAVA_OPTS": "-Xmx512m"
      }
    }
  }
}
EOF

    echo -e "${YELLOW}将以上配置添加到 VS Code 的 settings.json 中${NC}"
}

# 生成 HTTP 传输配置
generate_http_config() {
    cat > mcp-http-config.json << EOF
{
  "mcpServers": {
    "${SERVER_NAME}-http": {
      "command": "curl",
      "args": [
        "-X", "POST",
        "http://localhost:8080/mcp/message",
        "-H", "Content-Type: application/json",
        "-H", "Accept: application/json",
        "-d", "@-"
      ]
    }
  }
}
EOF
}

# 生成启动脚本
generate_startup_script() {
    cat > start-mcp-server.sh << 'EOF'
#!/bin/bash

# Galaxy Boot MCP Server 启动脚本

JAR_PATH="$1"
if [[ -z "$JAR_PATH" ]]; then
    echo "用法: $0 <jar-path>"
    exit 1
fi

if [[ ! -f "$JAR_PATH" ]]; then
    echo "错误: JAR 文件不存在: $JAR_PATH"
    exit 1
fi

echo "启动 Galaxy Boot MCP Server..."
echo "JAR 路径: $JAR_PATH"
echo "时间: $(date)"
echo

java -Xmx512m -Xms256m \
     -jar "$JAR_PATH" \
     --galaxy.mcp.enabled=true \
     --galaxy.mcp.transport.type=stdio \
     --logging.level.cn.com.chinastock.cnf.mcp=INFO \
     --spring.profiles.active=mcp
EOF

    chmod +x start-mcp-server.sh
}

# 生成测试脚本
generate_test_script() {
    cat > test-mcp-server.sh << 'EOF'
#!/bin/bash

# Galaxy Boot MCP Server 测试脚本

BASE_URL="http://localhost:8080"

echo "=== Galaxy Boot MCP Server 测试 ==="
echo

# 检查服务器状态
echo "1. 检查服务器状态..."
if curl -s "$BASE_URL/actuator/health" > /dev/null; then
    echo "✅ 服务器运行正常"
else
    echo "❌ 服务器未运行或无法访问"
    exit 1
fi

echo

# 测试计算器 API
echo "2. 测试计算器功能..."
echo "加法 10 + 5 = $(curl -s "$BASE_URL/api/calculator/add?a=10&b=5")"
echo "乘法 6 × 7 = $(curl -s "$BASE_URL/api/calculator/multiply?a=6&b=7")"
echo "平方根 √16 = $(curl -s "$BASE_URL/api/calculator/sqrt?number=16")"

echo

# 测试复合计算
echo "3. 测试复合计算..."
RESULT=$(curl -s -X POST "$BASE_URL/api/calculator/complex" \
  -H "Content-Type: application/json" \
  -d '{"a": 15, "b": 3}')
echo "复合计算结果: $RESULT"

echo
echo "=== 测试完成 ==="
EOF

    chmod +x test-mcp-server.sh
}

# 主逻辑
case "$CLIENT_TYPE" in
    "cursor")
        generate_cursor_config
        echo -e "${GREEN}✅ Cursor 配置文件已生成: cursor-mcp-config.json${NC}"
        ;;
    "claude")
        generate_claude_config
        echo -e "${GREEN}✅ Claude Desktop 配置文件已生成: claude-desktop-config.json${NC}"
        ;;
    "cline")
        generate_cline_config
        echo -e "${GREEN}✅ Cline (VS Code) 配置已生成: cline-settings.json${NC}"
        ;;
    "http")
        generate_http_config
        echo -e "${GREEN}✅ HTTP 传输配置已生成: mcp-http-config.json${NC}"
        ;;
    "all")
        generate_generic_config
        generate_cursor_config
        generate_claude_config
        generate_cline_config
        generate_http_config
        echo -e "${GREEN}✅ 所有配置文件已生成${NC}"
        ;;
    *)
        generate_generic_config
        echo -e "${GREEN}✅ 通用 MCP 配置文件已生成: mcp-config.json${NC}"
        ;;
esac

# 生成辅助脚本
generate_startup_script
generate_test_script

echo
echo -e "${GREEN}✅ 启动脚本已生成: start-mcp-server.sh${NC}"
echo -e "${GREEN}✅ 测试脚本已生成: test-mcp-server.sh${NC}"

echo
echo -e "${BLUE}=== 使用说明 ===${NC}"
echo "1. 启动 MCP Server:"
echo "   ./start-mcp-server.sh $JAR_PATH"
echo
echo "2. 测试 MCP Server:"
echo "   ./test-mcp-server.sh"
echo
echo "3. 在 AI 客户端中配置 MCP Server:"
echo "   - 复制相应的配置文件内容到客户端配置中"
echo "   - 重启 AI 客户端"
echo "   - 开始使用 MCP 工具"
echo
echo -e "${YELLOW}注意: 请确保 JAR 文件路径正确，并且 Java 21+ 已安装${NC}"
EOF
