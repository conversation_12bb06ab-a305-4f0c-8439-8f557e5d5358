#!/bin/bash

# Galaxy Boot MCP Server Bash 客户端
# 简单的 bash 脚本，用于测试和演示 MCP 协议调用

set -e

# 默认配置
SERVER_URL=${MCP_SERVER_URL:-"http://localhost:8080"}
ENDPOINT="/mcp/message"
API_KEY=${MCP_API_KEY:-""}

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 帮助信息
show_help() {
    echo "Galaxy Boot MCP Server Bash Client"
    echo
    echo "Usage: $0 [command] [options]"
    echo
    echo "Commands:"
    echo "  list                    - 列出所有可用工具"
    echo "  call <tool> <args>      - 调用指定工具"
    echo "  test                    - 运行测试"
    echo "  demo                    - 运行演示"
    echo "  interactive             - 交互模式"
    echo
    echo "Examples:"
    echo "  $0 list"
    echo "  $0 call add '{\"a\":10,\"b\":5}'"
    echo "  $0 call multiply '{\"a\":6,\"b\":7}'"
    echo "  $0 test"
    echo
    echo "Environment Variables:"
    echo "  MCP_SERVER_URL         - MCP 服务器 URL (默认: http://localhost:8080)"
    echo "  MCP_API_KEY            - API 密钥 (可选)"
}

# 发送 MCP 请求
send_mcp_request() {
    local request="$1"

    if [ -n "$API_KEY" ]; then
        curl -s -X POST "$SERVER_URL$ENDPOINT" \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer $API_KEY" \
            -d "$request"
    else
        curl -s -X POST "$SERVER_URL$ENDPOINT" \
            -H "Content-Type: application/json" \
            -d "$request"
    fi
}

# 检查服务器状态
check_server() {
    echo -e "${BLUE}检查服务器状态...${NC}"
    
    local response=$(curl -s "$SERVER_URL/mcp/info" || echo "")
    if [ -z "$response" ]; then
        echo -e "${RED}❌ 无法连接到服务器: $SERVER_URL${NC}"
        return 1
    fi
    
    local tool_count=$(echo "$response" | jq -r '.toolCount // 0' 2>/dev/null || echo "0")
    echo -e "${GREEN}✅ 服务器运行正常，发现 $tool_count 个工具${NC}"
    return 0
}

# 列出工具
list_tools() {
    echo -e "${BLUE}获取工具列表...${NC}"
    
    local request='{"jsonrpc":"2.0","id":1,"method":"tools/list","params":{}}'
    local response=$(send_mcp_request "$request")
    
    if echo "$response" | jq -e '.result.tools' > /dev/null 2>&1; then
        echo -e "${GREEN}可用工具:${NC}"
        echo "$response" | jq -r '.result.tools[] | "  - \(.name): \(.description)"'
    else
        echo -e "${RED}❌ 获取工具列表失败${NC}"
        echo "$response" | jq '.'
    fi
}

# 调用工具
call_tool() {
    local tool_name="$1"
    local arguments="$2"
    
    if [ -z "$tool_name" ] || [ -z "$arguments" ]; then
        echo -e "${RED}❌ 用法: call_tool <tool_name> <arguments_json>${NC}"
        return 1
    fi
    
    echo -e "${BLUE}调用工具: $tool_name${NC}"
    echo -e "${YELLOW}参数: $arguments${NC}"
    
    local request=$(jq -n \
        --arg tool "$tool_name" \
        --argjson args "$arguments" \
        '{"jsonrpc":"2.0","id":2,"method":"tools/call","params":{"name":$tool,"arguments":$args}}')
    
    local response=$(send_mcp_request "$request")
    
    if echo "$response" | jq -e '.result' > /dev/null 2>&1; then
        local content=$(echo "$response" | jq -r '.result.content[0].text // .result')
        echo -e "${GREEN}✅ 结果: $content${NC}"
    else
        echo -e "${RED}❌ 调用失败${NC}"
        echo "$response" | jq '.'
    fi
}

# 运行测试
run_test() {
    echo -e "${BLUE}=== Galaxy Boot MCP 客户端测试 ===${NC}"
    echo
    
    # 检查服务器
    if ! check_server; then
        return 1
    fi
    echo
    
    # 测试工具列表
    echo -e "${BLUE}1. 测试工具列表${NC}"
    list_tools
    echo
    
    # 测试计算器工具
    echo -e "${BLUE}2. 测试计算器工具${NC}"
    call_tool "add" '{"a":10,"b":5}'
    call_tool "multiply" '{"a":6,"b":7}'
    call_tool "sqrt" '{"number":16}'
    echo
    
    # 测试复合计算
    echo -e "${BLUE}3. 测试复合计算${NC}"
    call_tool "complexCalculation" '{"a":15,"b":3}'
    echo
    
    echo -e "${GREEN}=== 测试完成 ===${NC}"
}

# 运行演示
run_demo() {
    echo -e "${BLUE}=== Galaxy Boot MCP 演示 ===${NC}"
    echo
    
    if ! check_server; then
        return 1
    fi
    
    echo -e "${YELLOW}演示场景: AI 助手帮助用户进行数学计算${NC}"
    echo
    
    echo -e "${BLUE}用户: 帮我计算 123 + 456${NC}"
    echo -e "${YELLOW}AI: 我来帮你计算 123 + 456${NC}"
    call_tool "add" '{"a":123,"b":456}'
    echo
    
    echo -e "${BLUE}用户: 那 25 的平方根是多少？${NC}"
    echo -e "${YELLOW}AI: 我来计算 25 的平方根${NC}"
    call_tool "sqrt" '{"number":25}'
    echo
    
    echo -e "${BLUE}用户: 计算 2 的 8 次方${NC}"
    echo -e "${YELLOW}AI: 我来计算 2 的 8 次方${NC}"
    call_tool "power" '{"base":2,"exponent":8}'
    echo
    
    echo -e "${BLUE}用户: 帮我做一个复合计算，a=20, b=4${NC}"
    echo -e "${YELLOW}AI: 我来进行复合计算${NC}"
    call_tool "complexCalculation" '{"a":20,"b":4}'
    echo
    
    echo -e "${GREEN}=== 演示完成 ===${NC}"
}

# 交互模式
interactive_mode() {
    echo -e "${BLUE}Galaxy Boot MCP 交互模式${NC}"
    echo "输入 'help' 查看命令，'quit' 退出"
    echo
    
    if ! check_server; then
        return 1
    fi
    
    while true; do
        echo -n "mcp> "
        read -r command
        
        case "$command" in
            "help")
                echo "可用命令:"
                echo "  list                    - 列出工具"
                echo "  call <tool> <args>      - 调用工具"
                echo "  test                    - 运行测试"
                echo "  demo                    - 运行演示"
                echo "  quit                    - 退出"
                ;;
            "list")
                list_tools
                ;;
            "test")
                run_test
                ;;
            "demo")
                run_demo
                ;;
            "quit"|"exit")
                echo "再见！"
                break
                ;;
            call*)
                # 解析 call 命令
                local parts=($command)
                if [ ${#parts[@]} -ge 3 ]; then
                    local tool_name="${parts[1]}"
                    local args="${command#call $tool_name }"
                    call_tool "$tool_name" "$args"
                else
                    echo "用法: call <tool_name> <arguments_json>"
                fi
                ;;
            "")
                # 空命令，继续
                ;;
            *)
                echo "未知命令: $command"
                echo "输入 'help' 查看可用命令"
                ;;
        esac
        echo
    done
}

# STDIO 模式（用于 MCP 客户端）
stdio_mode() {
    # 检查服务器连接
    if ! check_server >/dev/null 2>&1; then
        echo '{"jsonrpc":"2.0","id":null,"error":{"code":-32603,"message":"Cannot connect to MCP server"}}' >&2
        exit 1
    fi

    # 处理标准输入
    while IFS= read -r line; do
        if [ -z "$line" ]; then
            continue
        fi

        # 直接转发请求到 MCP 服务器
        response=$(send_mcp_request "$line")
        echo "$response"
    done
}

# 主程序
main() {
    # 检查依赖
    if ! command -v curl &> /dev/null; then
        echo -e "${RED}❌ 需要安装 curl${NC}"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        echo -e "${RED}❌ 需要安装 jq${NC}"
        exit 1
    fi
    
    # 解析命令行参数
    case "${1:-help}" in
        "list")
            check_server && list_tools
            ;;
        "call")
            if [ $# -ge 3 ]; then
                check_server && call_tool "$2" "$3"
            else
                echo "用法: $0 call <tool_name> <arguments_json>"
                exit 1
            fi
            ;;
        "test")
            run_test
            ;;
        "demo")
            run_demo
            ;;
        "interactive")
            interactive_mode
            ;;
        "stdio")
            stdio_mode
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            echo "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 运行主程序
main "$@"
