# Galaxy Boot MCP Server 使用示例

## 概述

本文档展示了如何在各种环境中使用 Galaxy Boot MCP Server，包括直接 API 调用、客户端脚本调用，以及在 Cursor、Claude Desktop 等 AI 工具中的配置和使用。

## 服务器状态

我们的 MCP 服务器已经成功启动，提供以下功能：

- **服务器地址**: http://localhost:8080
- **可用工具数量**: 17个
- **支持的协议**: MCP 2.0 (JSON-RPC)
- **传输方式**: HTTP POST

### 可用工具列表

#### 计算器工具
- `add` - 计算两个数的和
- `subtract` - 计算两个数的差
- `multiply` - 计算两个数的乘积
- `divide` - 计算两个数的商
- `power` - 计算幂运算
- `sqrt` - 计算平方根
- `percentage` - 计算百分比
- `complexCalculation` - 复合数学计算

#### 用户管理工具
- `getUserById` - 根据ID获取用户
- `getUserByUsername` - 根据用户名获取用户
- `getAllUsers` - 获取所有用户
- `getUsersByDepartment` - 根据部门获取用户
- `createUser` - 创建新用户
- `updateUser` - 更新用户信息
- `deleteUser` - 删除用户
- `searchUsers` - 搜索用户
- `getUserStatistics` - 获取用户统计

## 1. 直接 API 调用

### 获取工具列表

```bash
curl -X POST http://localhost:8080/mcp/message \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":1,"method":"tools/list","params":{}}'
```

**响应示例**:
```json
{
  "result": {
    "tools": [
      {
        "name": "add",
        "description": "计算两个数的和",
        "inputSchema": {"type": "object", "properties": {}}
      }
      // ... 更多工具
    ]
  },
  "id": 1,
  "jsonrpc": "2.0"
}
```

### 调用计算器工具

```bash
# 加法运算
curl -X POST http://localhost:8080/mcp/message \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":2,"method":"tools/call","params":{"name":"add","arguments":{"a":15,"b":25}}}'

# 响应: {"result":{"content":[{"text":"40.0","type":"text"}],"isError":false},"id":2,"jsonrpc":"2.0"}
```

```bash
# 平方根计算
curl -X POST http://localhost:8080/mcp/message \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":3,"method":"tools/call","params":{"name":"sqrt","arguments":{"number":16}}}'
```

```bash
# 复合计算
curl -X POST http://localhost:8080/mcp/message \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":4,"method":"tools/call","params":{"name":"complexCalculation","arguments":{"a":20,"b":4}}}'
```

## 2. 使用 Bash 客户端

我们提供了一个功能完整的 bash 客户端脚本。

### 安装依赖

```bash
# macOS
brew install jq curl

# Ubuntu/Debian
sudo apt-get install jq curl

# CentOS/RHEL
sudo yum install jq curl
```

### 基本使用

```bash
# 查看帮助
./mcp-bash-client.sh help

# 列出所有工具
./mcp-bash-client.sh list

# 调用工具
./mcp-bash-client.sh call add '{"a":10,"b":5}'
./mcp-bash-client.sh call multiply '{"a":6,"b":7}'

# 运行测试
./mcp-bash-client.sh test

# 运行演示
./mcp-bash-client.sh demo

# 交互模式
./mcp-bash-client.sh interactive
```

### 测试结果示例

```
=== Galaxy Boot MCP 客户端测试 ===

检查服务器状态...
✅ 服务器运行正常，发现 17 个工具

1. 测试工具列表
获取工具列表...
可用工具:
  - add: 计算两个数的和
  - multiply: 计算两个数的乘积
  - sqrt: 计算一个数的平方根
  // ... 更多工具

2. 测试计算器工具
调用工具: add
参数: {"a":10,"b":5}
✅ 结果: 15.0

调用工具: multiply
参数: {"a":6,"b":7}
✅ 结果: Tool multiply executed successfully with arguments: {a=6, b=7}

=== 测试完成 ===
```

## 3. 在 Cursor 中配置

### 配置文件位置

**macOS**:
```
~/Library/Application Support/Cursor/User/globalStorage/mcp-servers.json
```

**Windows**:
```
%APPDATA%\Cursor\User\globalStorage\mcp-servers.json
```

**Linux**:
```
~/.config/Cursor/User/globalStorage/mcp-servers.json
```

### 配置内容

```json
{
  "mcpServers": {
    "galaxy-business-tools": {
      "command": "bash",
      "args": [
        "/path/to/galaxy-boot-starter-mcp/client-examples/mcp-bash-client.sh",
        "stdio"
      ],
      "env": {
        "MCP_SERVER_URL": "http://localhost:8080",
        "MCP_API_KEY": ""
      }
    }
  }
}
```

### 在 Cursor 中使用

配置完成后，在 Cursor 的聊天界面中可以这样使用：

```
用户: @galaxy-business-tools 帮我计算 123 + 456

AI: 我来帮你计算 123 + 456。
[调用 add 工具，参数: a=123, b=456]
结果: 579

用户: @galaxy-business-tools 计算 25 的平方根

AI: 我来计算 25 的平方根。
[调用 sqrt 工具，参数: number=25]
结果: 5.0
```

## 4. 在 Claude Desktop 中配置

### 配置文件位置

**macOS**:
```
~/Library/Application Support/Claude/claude_desktop_config.json
```

**Windows**:
```
%APPDATA%\Claude\claude_desktop_config.json
```

### 配置内容

```json
{
  "mcpServers": {
    "galaxy-business-api": {
      "command": "bash",
      "args": [
        "/path/to/galaxy-boot-starter-mcp/client-examples/mcp-bash-client.sh",
        "stdio"
      ],
      "env": {
        "MCP_SERVER_URL": "http://localhost:8080"
      }
    }
  }
}
```

## 5. 业务场景示例

### 场景 1: 数学计算助手

```
用户: 我需要计算一些数学问题

AI: 我可以帮你进行各种数学计算。你需要计算什么？

用户: 计算 (15 + 25) * 3 的结果

AI: 我来分步计算：
1. 首先计算 15 + 25
[调用 add 工具: a=15, b=25] → 40
2. 然后计算 40 * 3  
[调用 multiply 工具: a=40, b=3] → 120

所以 (15 + 25) * 3 = 120
```

### 场景 2: 用户管理助手

```
用户: 帮我查看公司的用户统计信息

AI: 我来为您查询用户统计信息。
[调用 getUserStatistics 工具]

根据统计数据显示：
- 总用户数：4人
- 平均年龄：29岁
- 部门分布：
  - IT部：1人
  - 开发部：1人  
  - 产品部：1人
  - 销售部：1人

用户: 帮我查看开发部的所有员工

AI: 我来查询开发部的员工信息。
[调用 getUsersByDepartment 工具: department="开发部"]
// 返回开发部员工列表
```

### 场景 3: 复合业务操作

```
用户: 我要创建一个新员工档案，然后计算一下我们的员工增长率

AI: 好的，我来帮您完成这两个任务。

首先，请提供新员工的信息：
- 用户名
- 姓名  
- 邮箱
- 年龄
- 部门
- 职位

用户: 用户名：alice，姓名：爱丽丝，邮箱：<EMAIL>，年龄：28，部门：开发部，职位：高级工程师

AI: 我来创建新员工档案：
[调用 createUser 工具，传入员工信息]
✅ 员工档案创建成功！

现在计算员工增长率：
[调用 getUserStatistics 工具获取当前统计]
当前总员工数：5人（新增1人）
增长率：(1/4) * 100% = 25%

员工增长率为 25%。
```

## 6. 错误处理示例

### 连接错误

```bash
$ ./mcp-bash-client.sh test
❌ 无法连接到服务器: http://localhost:8080
```

### 工具调用错误

```bash
$ ./mcp-bash-client.sh call nonexistent '{"a":1}'
❌ 调用失败
{
  "jsonrpc": "2.0",
  "id": 2,
  "error": {
    "code": -32601,
    "message": "Method not found: tools/call"
  }
}
```

## 7. 高级配置

### 使用 API 密钥

```bash
export MCP_API_KEY="your-secret-api-key"
./mcp-bash-client.sh test
```

### 连接远程服务器

```bash
export MCP_SERVER_URL="https://your-server.com:8080"
./mcp-bash-client.sh test
```

### 自定义超时

```bash
# 在脚本中修改 timeout 参数
curl -s -X POST "$SERVER_URL$ENDPOINT" \
    -H "Content-Type: application/json" \
    -d "$request" \
    --max-time 60
```

## 8. 故障排除

### 常见问题

1. **服务器无法连接**
   - 检查服务器是否启动：`curl http://localhost:8080/mcp/info`
   - 检查端口是否被占用：`netstat -an | grep 8080`

2. **工具调用失败**
   - 检查参数格式是否正确
   - 查看服务器日志获取详细错误信息

3. **Cursor/Claude 无法识别工具**
   - 检查配置文件路径是否正确
   - 确认脚本有执行权限：`chmod +x mcp-bash-client.sh`
   - 重启 AI 客户端

### 调试技巧

1. **启用详细日志**：
   ```bash
   # 在脚本中添加调试输出
   echo "Debug: Sending request: $request" >&2
   ```

2. **测试单个工具**：
   ```bash
   ./mcp-bash-client.sh call add '{"a":1,"b":2}'
   ```

3. **检查 JSON 格式**：
   ```bash
   echo '{"a":1,"b":2}' | jq '.'
   ```

通过以上示例，您可以在各种环境中成功使用 Galaxy Boot MCP Server，让 AI 助手能够调用您的业务 API 完成各种任务！
