#!/usr/bin/env python3

"""
Galaxy Boot MCP Server 简化客户端
使用标准库，不依赖第三方包
"""

import json
import sys
import urllib.request
import urllib.parse
import urllib.error
from typing import Dict, Any, Optional

class SimpleMcpClient:
    def __init__(self, server_url: str = "http://localhost:8080", 
                 endpoint: str = "/mcp/message", 
                 api_key: Optional[str] = None):
        self.server_url = server_url.rstrip('/')
        self.endpoint = endpoint
        self.api_key = api_key

    def send_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """发送请求到 MCP 服务器"""
        url = f"{self.server_url}{self.endpoint}"
        
        # 准备请求数据
        data = json.dumps(request).encode('utf-8')
        
        # 准备请求头
        headers = {
            'Content-Type': 'application/json',
            'Content-Length': str(len(data))
        }
        
        if self.api_key:
            headers['Authorization'] = f'Bearer {self.api_key}'
        
        try:
            # 创建请求
            req = urllib.request.Request(url, data=data, headers=headers, method='POST')
            
            # 发送请求
            with urllib.request.urlopen(req, timeout=30) as response:
                response_data = response.read().decode('utf-8')
                return json.loads(response_data)
                
        except urllib.error.HTTPError as e:
            return {
                "jsonrpc": "2.0",
                "id": request.get("id"),
                "error": {
                    "code": e.code,
                    "message": f"HTTP Error {e.code}: {e.reason}"
                }
            }
        except urllib.error.URLError as e:
            return {
                "jsonrpc": "2.0",
                "id": request.get("id"),
                "error": {
                    "code": -32603,
                    "message": f"Connection error: {str(e.reason)}"
                }
            }
        except Exception as e:
            return {
                "jsonrpc": "2.0",
                "id": request.get("id"),
                "error": {
                    "code": -32603,
                    "message": f"Unexpected error: {str(e)}"
                }
            }

    def health_check(self) -> bool:
        """检查服务器健康状态"""
        try:
            url = f"{self.server_url}/mcp/info"
            with urllib.request.urlopen(url, timeout=5) as response:
                return response.status == 200
        except:
            return False

    def list_tools(self) -> Dict[str, Any]:
        """获取可用工具列表"""
        request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "tools/list",
            "params": {}
        }
        return self.send_request(request)

    def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """调用指定工具"""
        request = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/call",
            "params": {
                "name": tool_name,
                "arguments": arguments
            }
        }
        return self.send_request(request)

def main():
    if len(sys.argv) < 2:
        print("用法: python3 simple-mcp-client.py <command> [args...]")
        print("命令:")
        print("  test                    - 运行测试")
        print("  list                    - 列出工具")
        print("  call <tool> <args_json> - 调用工具")
        print("  demo                    - 运行演示")
        sys.exit(1)
    
    client = SimpleMcpClient()
    command = sys.argv[1]
    
    if command == "test":
        print("=== Galaxy Boot MCP Python 客户端测试 ===")
        print()
        
        # 检查连接
        print("检查服务器连接...")
        if not client.health_check():
            print("❌ 无法连接到服务器")
            sys.exit(1)
        print("✅ 服务器连接正常")
        print()
        
        # 测试工具列表
        print("获取工具列表...")
        response = client.list_tools()
        if "result" in response:
            tools = response["result"]["tools"]
            print(f"✅ 发现 {len(tools)} 个工具")
            for tool in tools[:5]:  # 只显示前5个
                print(f"  - {tool['name']}: {tool['description']}")
            if len(tools) > 5:
                print(f"  ... 还有 {len(tools) - 5} 个工具")
        else:
            print(f"❌ 获取工具列表失败: {response.get('error', 'Unknown error')}")
        print()
        
        # 测试工具调用
        print("测试工具调用...")
        test_cases = [
            ("add", {"a": 10, "b": 5}),
            ("multiply", {"a": 6, "b": 7}),
            ("sqrt", {"number": 16})
        ]
        
        for tool_name, args in test_cases:
            print(f"调用 {tool_name} 工具...")
            response = client.call_tool(tool_name, args)
            if "result" in response:
                content = response["result"]["content"][0]["text"]
                print(f"✅ 结果: {content}")
            else:
                print(f"❌ 调用失败: {response.get('error', 'Unknown error')}")
        
        print()
        print("=== 测试完成 ===")
        
    elif command == "list":
        response = client.list_tools()
        if "result" in response:
            tools = response["result"]["tools"]
            print(f"可用工具 ({len(tools)} 个):")
            for tool in tools:
                print(f"  - {tool['name']}: {tool['description']}")
        else:
            print(f"错误: {response.get('error', 'Unknown error')}")
            
    elif command == "call":
        if len(sys.argv) < 4:
            print("用法: python3 simple-mcp-client.py call <tool_name> <args_json>")
            sys.exit(1)
        
        tool_name = sys.argv[2]
        try:
            args = json.loads(sys.argv[3])
            response = client.call_tool(tool_name, args)
            if "result" in response:
                content = response["result"]["content"][0]["text"]
                print(f"结果: {content}")
            else:
                print(f"错误: {response.get('error', 'Unknown error')}")
        except json.JSONDecodeError:
            print("错误: 参数必须是有效的 JSON 格式")
            
    elif command == "demo":
        print("=== Galaxy Boot MCP 演示 ===")
        print()
        
        if not client.health_check():
            print("❌ 无法连接到服务器")
            sys.exit(1)
        
        print("演示场景: AI 助手进行数学计算")
        print()
        
        demos = [
            ("计算 123 + 456", "add", {"a": 123, "b": 456}),
            ("计算 25 的平方根", "sqrt", {"number": 25}),
            ("计算 2 的 8 次方", "power", {"base": 2, "exponent": 8}),
            ("复合计算 a=20, b=4", "complexCalculation", {"a": 20, "b": 4})
        ]
        
        for description, tool_name, args in demos:
            print(f"用户: {description}")
            print(f"AI: 我来帮你{description}")
            
            response = client.call_tool(tool_name, args)
            if "result" in response:
                content = response["result"]["content"][0]["text"]
                print(f"✅ 结果: {content}")
            else:
                print(f"❌ 计算失败: {response.get('error', 'Unknown error')}")
            print()
        
        print("=== 演示完成 ===")
        
    else:
        print(f"未知命令: {command}")
        sys.exit(1)

if __name__ == "__main__":
    main()
