#!/usr/bin/env python3

"""
Galaxy Boot MCP Server HTTP 客户端
用于通过 HTTP 连接远程 Galaxy Boot MCP Server
"""

import json
import sys
import requests
import argparse
from typing import Dict, Any, Optional

class McpHttpClient:
    def __init__(self, server_url: str, endpoint: str = "/mcp/message", 
                 api_key: Optional[str] = None, timeout: int = 30):
        self.server_url = server_url.rstrip('/')
        self.endpoint = endpoint
        self.api_key = api_key
        self.timeout = timeout
        self.session = requests.Session()
        
        # 设置请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'Galaxy-MCP-HTTP-Client/1.0'
        })
        
        if self.api_key:
            self.session.headers['Authorization'] = f'Bearer {self.api_key}'

    def send_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """发送请求到远程 MCP 服务器"""
        url = f"{self.server_url}{self.endpoint}"
        
        try:
            response = self.session.post(
                url, 
                json=request, 
                timeout=self.timeout
            )
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.Timeout:
            return {
                "jsonrpc": "2.0",
                "id": request.get("id"),
                "error": {
                    "code": -32603,
                    "message": "Request timeout"
                }
            }
        except requests.exceptions.ConnectionError:
            return {
                "jsonrpc": "2.0",
                "id": request.get("id"),
                "error": {
                    "code": -32603,
                    "message": "Connection error"
                }
            }
        except requests.exceptions.HTTPError as e:
            return {
                "jsonrpc": "2.0",
                "id": request.get("id"),
                "error": {
                    "code": -32603,
                    "message": f"HTTP error: {e.response.status_code}"
                }
            }
        except Exception as e:
            return {
                "jsonrpc": "2.0",
                "id": request.get("id"),
                "error": {
                    "code": -32603,
                    "message": f"Unexpected error: {str(e)}"
                }
            }

    def health_check(self) -> bool:
        """检查服务器健康状态"""
        try:
            health_url = f"{self.server_url}/mcp/info"
            response = self.session.get(health_url, timeout=5)
            return response.status_code == 200
        except:
            return False

    def list_tools(self) -> Dict[str, Any]:
        """获取可用工具列表"""
        request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "tools/list",
            "params": {}
        }
        return self.send_request(request)

    def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """调用指定工具"""
        request = {
            "jsonrpc": "2.0",
            "id": 2,
            "method": "tools/call",
            "params": {
                "name": tool_name,
                "arguments": arguments
            }
        }
        return self.send_request(request)

    def run_interactive(self):
        """运行交互模式"""
        print("Galaxy Boot MCP Interactive Client")
        print("Commands: list, call <tool_name> <args_json>, quit")
        print()
        
        while True:
            try:
                command = input("mcp> ").strip()
                if not command:
                    continue
                    
                if command == "quit":
                    break
                elif command == "list":
                    response = self.list_tools()
                    if "result" in response:
                        tools = response["result"]["tools"]
                        print(f"Available tools ({len(tools)}):")
                        for tool in tools:
                            print(f"  - {tool['name']}: {tool['description']}")
                    else:
                        print(f"Error: {response.get('error', 'Unknown error')}")
                        
                elif command.startswith("call "):
                    parts = command.split(" ", 2)
                    if len(parts) < 3:
                        print("Usage: call <tool_name> <args_json>")
                        continue
                    
                    tool_name = parts[1]
                    try:
                        args = json.loads(parts[2])
                        response = self.call_tool(tool_name, args)
                        if "result" in response:
                            result = response["result"]
                            if "content" in result:
                                for content in result["content"]:
                                    print(f"Result: {content.get('text', content)}")
                            else:
                                print(f"Result: {result}")
                        else:
                            print(f"Error: {response.get('error', 'Unknown error')}")
                    except json.JSONDecodeError:
                        print("Invalid JSON arguments")
                        
                else:
                    print("Unknown command. Use 'list', 'call <tool> <args>', or 'quit'")
                    
            except KeyboardInterrupt:
                break
            except EOFError:
                break
                
        print("\nGoodbye!")

    def run_stdio(self):
        """运行标准输入输出模式（用于 MCP 客户端）"""
        try:
            for line in sys.stdin:
                line = line.strip()
                if not line:
                    continue
                
                try:
                    request = json.loads(line)
                    response = self.send_request(request)
                    print(json.dumps(response))
                    sys.stdout.flush()
                    
                except json.JSONDecodeError as e:
                    error_response = {
                        "jsonrpc": "2.0",
                        "id": None,
                        "error": {
                            "code": -32700,
                            "message": f"Parse error: {str(e)}"
                        }
                    }
                    print(json.dumps(error_response))
                    sys.stdout.flush()
                    
        except KeyboardInterrupt:
            pass

def main():
    parser = argparse.ArgumentParser(
        description='Galaxy Boot MCP Server HTTP Client'
    )
    parser.add_argument(
        '--server-url', 
        default='http://localhost:8080',
        help='MCP Server URL (default: http://localhost:8080)'
    )
    parser.add_argument(
        '--endpoint', 
        default='/mcp/message',
        help='MCP message endpoint (default: /mcp/message)'
    )
    parser.add_argument(
        '--api-key',
        help='API key for authentication'
    )
    parser.add_argument(
        '--timeout',
        type=int,
        default=30,
        help='Request timeout in seconds (default: 30)'
    )
    parser.add_argument(
        '--interactive',
        action='store_true',
        help='Run in interactive mode'
    )
    parser.add_argument(
        '--test',
        action='store_true',
        help='Run a simple test'
    )
    
    args = parser.parse_args()
    
    client = McpHttpClient(
        server_url=args.server_url,
        endpoint=args.endpoint,
        api_key=args.api_key,
        timeout=args.timeout
    )
    
    # 检查连接
    if not client.health_check():
        print(f"Error: Cannot connect to server {args.server_url}", file=sys.stderr)
        sys.exit(1)
    
    if args.test:
        # 运行测试
        print("Testing connection...", file=sys.stderr)
        
        # 测试工具列表
        response = client.list_tools()
        if "result" in response:
            tools = response["result"]["tools"]
            print(f"✅ Found {len(tools)} tools", file=sys.stderr)
            
            # 测试计算器工具
            calc_response = client.call_tool("add", {"a": 10, "b": 5})
            if "result" in calc_response:
                print("✅ Tool call test successful!", file=sys.stderr)
                print(f"10 + 5 = {calc_response['result']['content'][0]['text']}", file=sys.stderr)
            else:
                print("❌ Tool call test failed!", file=sys.stderr)
        else:
            print("❌ Connection test failed!", file=sys.stderr)
            sys.exit(1)
            
    elif args.interactive:
        client.run_interactive()
    else:
        client.run_stdio()

if __name__ == '__main__':
    main()
