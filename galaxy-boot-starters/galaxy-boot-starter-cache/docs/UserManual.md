### 组件功能介绍

#### 高性能缓存
基于 Caffeine 缓存库，提供：
- 高性能的本地缓存
- 多种缓存策略（LRU、LFU、FIFO）
- 自动过期机制
- 缓存统计功能

#### 配置合并机制
支持全局默认配置和特定缓存配置的智能合并：
- 全局默认配置作为基础配置
- 特定缓存配置会覆盖对应的默认配置项
- 未指定的配置项会继承默认配置

#### 缓存名称校验
提供启动时缓存名称校验功能：
- 检查 `@Cacheable` 注解中使用的缓存名称
- 验证缓存名称是否在配置中定义（防止出现未预期行为）
- 启动时发现配置错误，避免运行时问题

#### 异步缓存支持
支持 WebFlux 响应式编程模型：
- 异步缓存操作
- 支持 Mono 和 Flux 类型的缓存
- 与 WebFlux 完美集成

### 使用方式

#### 1. 启用缓存
在启动类上添加 `@EnableCaching` 注解：

```java
@SpringBootApplication
@EnableCaching
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

#### 2. 配置缓存
在 `application.yml` 中配置缓存：

```yaml
spring:
  # Spring Boot 原生的 Caffeine 默认配置
  cache:
    type: caffeine
    caffeine:
      # 全局默认配置，所有缓存都会继承这些配置
      spec: >
        maximumSize=500,
        expireAfterWrite=5m,
        recordStats
        
  # 自定义的特定缓存配置
  custom-caffeine:
    specs:
      # 用户缓存 - 长期缓存
      userCache:
        spec: maximumSize=1000,expireAfterWrite=30m,initialCapacity=100
      
      # 产品缓存 - 短期缓存
      productCache:
        spec: maximumSize=500,expireAfterWrite=5m
      
      # 配置缓存 - 只读缓存
      configCache:
        spec: maximumSize=100,expireAfterWrite=1h,recordStats
```

#### 3. 使用缓存注解
在业务方法上使用 Spring Cache 注解：

```java
@Service
public class UserService {
    
    // 使用用户缓存
    @Cacheable(cacheNames = "userCache", key = "#id")
    public User getUserById(Long id) {
        // 模拟数据库查询
        return userRepository.findById(id);
    }
    
    // 更新缓存
    @CachePut(cacheNames = "userCache", key = "#user.id")
    public User updateUser(User user) {
        return userRepository.save(user);
    }
    
    // 删除缓存
    @CacheEvict(cacheNames = "userCache", key = "#id")
    public void deleteUser(Long id) {
        userRepository.deleteById(id);
    }
    
    // 清空缓存
    @CacheEvict(cacheNames = "userCache", allEntries = true)
    public void clearUserCache() {
        // 清空所有用户缓存
    }
}
```

#### 4. 响应式缓存支持
支持 WebFlux 响应式编程：

```java
@Service
public class ProductService {
    
    // 缓存 Mono 类型
    @Cacheable(cacheNames = "productCache", key = "#id")
    public Mono<Product> getProductById(String id) {
        return productRepository.findById(id)
            .switchIfEmpty(Mono.error(new ProductNotFoundException(id)));
    }
    
    // 缓存 Flux 类型
    @Cacheable(cacheNames = "productCache", key = "'all'")
    public Flux<Product> getAllProducts() {
        return productRepository.findAll();
    }
}
```

### 配置说明

#### Caffeine 配置参数
支持的 Caffeine 配置参数：

```yaml
spring:
  cache:
    caffeine:
      spec: >
        initialCapacity=100,          # 初始容量
        maximumSize=1000,             # 最大条目数
        maximumWeight=10000,          # 最大权重
        expireAfterAccess=30m,        # 访问后过期时间
        expireAfterWrite=1h,          # 写入后过期时间
        refreshAfterWrite=30m,        # 写入后刷新时间
        weakKeys,                     # 弱引用键
        weakValues,                   # 弱引用值
        softValues,                   # 软引用值
        recordStats                   # 记录统计信息
```
官网参考: `https://github.com/ben-manes/caffeine/wiki/Specification`

#### 配置合并规则
特定缓存配置会覆盖全局默认配置：

```yaml
# 全局默认配置
spring:
  cache:
    caffeine:
      spec: maximumSize=500,expireAfterWrite=5m,recordStats

# 特定缓存配置
  custom-caffeine:
    specs:
      userCache:
        # 只覆盖 maximumSize 和 expireAfterWrite
        # recordStats 会继承自全局配置
        spec: maximumSize=1000,expireAfterWrite=30m
```

最终 `userCache` 的配置为：
```
maximumSize=1000,expireAfterWrite=30m,recordStats
```

#### 缓存名称校验（防止出现未预期行为）
组件会在启动时校验所有使用的缓存名称：

```java
// 这个缓存名称必须在配置中定义
@Cacheable(cacheNames = "userCache", key = "#id")
public User getUserById(Long id) {
    return userRepository.findById(id);
}
```

如果使用了未配置的缓存名称，启动时会抛出异常：
```
Validation failed: Cache name 'unknownCache' used on method [getUserById] in class [UserService] 
is not configured in 'spring.custom-caffeine.specs'. Available caches are: [userCache, productCache]
```

### 最佳实践

#### 1. 缓存分层设计
根据数据特性设计不同的缓存层：

```yaml
spring:
  custom-caffeine:
    specs:
      # L1 缓存 - 热点数据，小容量，短过期
      hotDataCache:
        spec: maximumSize=100,expireAfterWrite=1m
      
      # L2 缓存 - 常用数据，中等容量，中等过期
      commonDataCache:
        spec: maximumSize=1000,expireAfterWrite=10m
      
      # L3 缓存 - 冷数据，大容量，长过期
      coldDataCache:
        spec: maximumSize=10000,expireAfterWrite=1h
```

#### 2. 缓存键设计
设计合理的缓存键策略：

```java
@Service
public class UserService {
    
    // 简单键
    @Cacheable(cacheNames = "userCache", key = "#id")
    public User getUserById(Long id) { ... }
    
    // 复合键
    @Cacheable(cacheNames = "userCache", key = "#username + ':' + #status")
    public List<User> getUsersByUsernameAndStatus(String username, String status) { ... }
    
    // 使用 SpEL 表达式
    @Cacheable(cacheNames = "userCache", key = "#user.id + ':' + #user.version")
    public User updateUser(User user) { ... }
    
    // 条件缓存
    @Cacheable(cacheNames = "userCache", key = "#id", condition = "#id > 0")
    public User getUserById(Long id) { ... }
}
```

#### 3. 缓存失效策略
合理设计缓存失效策略：

```java
@Service
public class UserService {
    
    // 更新时同时更新缓存
    @CachePut(cacheNames = "userCache", key = "#user.id")
    public User updateUser(User user) {
        return userRepository.save(user);
    }
    
    // 删除时清除缓存
    @CacheEvict(cacheNames = "userCache", key = "#id")
    public void deleteUser(Long id) {
        userRepository.deleteById(id);
    }
    
    // 批量操作时清空相关缓存
    @CacheEvict(cacheNames = "userCache", allEntries = true)
    public void batchUpdateUsers(List<User> users) {
        userRepository.saveAll(users);
    }
}
```

#### 4. 监控和统计
启用缓存统计功能：

```yaml
spring:
  cache:
    caffeine:
      spec: recordStats  # 启用统计

  custom-caffeine:
    specs:
      userCache:
        spec: maximumSize=1000,expireAfterWrite=30m,recordStats
```

获取缓存统计信息：

```java
@Component
public class CacheMonitor {
    
    @Autowired
    private CacheManager cacheManager;
    
    public void printCacheStats() {
        Cache cache = cacheManager.getCache("userCache");
        if (cache instanceof CaffeineCache) {
            CaffeineCache caffeineCache = (CaffeineCache) cache;
            com.github.benmanes.caffeine.cache.Cache<Object, Object> nativeCache = 
                caffeineCache.getNativeCache();
            
            CacheStats stats = nativeCache.stats();
            System.out.println("命中率: " + stats.hitRate());
            System.out.println("请求总数: " + stats.requestCount());
            System.out.println("命中次数: " + stats.hitCount());
            System.out.println("未命中次数: " + stats.missCount());
        }
    }
}
```

### 性能优化

#### 1. 合理设置缓存大小
根据内存容量和数据特性设置合适的缓存大小：

```yaml
spring:
  custom-caffeine:
    specs:
      # 小数据量，高频访问
      smallDataCache:
        spec: maximumSize=100,expireAfterWrite=5m
      
      # 大数据量，低频访问
      largeDataCache:
        spec: maximumSize=10000,expireAfterAccess=1h
```

#### 2. 选择合适的过期策略
根据数据更新频率选择过期策略：

```yaml
spring:
  custom-caffeine:
    specs:
      # 数据更新频繁，使用写入后过期
      frequentUpdateCache:
        spec: maximumSize=1000,expireAfterWrite=5m
      
      # 数据读取频繁，使用访问后过期
      frequentReadCache:
        spec: maximumSize=1000,expireAfterAccess=30m
      
      # 数据需要定期刷新，使用刷新机制
      refreshableCache:
        spec: maximumSize=1000,refreshAfterWrite=10m
```

### 技术选型

#### 缓存方案对比

| **方案**        | **优点**                | **缺点**              | **适用场景**        |
|---------------|----------------------|---------------------|-----------------|
| **Caffeine**  | 性能极高，功能丰富，本地缓存      | 不支持分布式，内存限制        | 单机应用，高性能要求      |
| **Redis**     | 分布式支持，持久化，功能丰富      | 网络延迟，需要额外部署        | 分布式应用，数据共享      |
| **Ehcache**   | 功能完整，支持持久化          | 性能较低，配置复杂          | 传统应用，需要持久化      |
| **Guava**     | 轻量级，简单易用            | 功能有限，性能一般          | 简单缓存需求          |

#### 选择建议

- **高性能单机应用**：推荐使用 Galaxy Boot Starter Cache (Caffeine)
- **分布式应用**：推荐使用 Redis
- **混合方案**：L1 使用 Caffeine，L2 使用 Redis
- **简单需求**：可以使用 Guava Cache

### 使用示例

完整的使用示例请参考：`galaxy-boot-examples/galaxy-boot-cache-example`

该示例演示了：
- 缓存配置和合并机制
- MVC 和 WebFlux 模式的缓存使用
- 缓存统计和监控
- 不同缓存策略的应用 